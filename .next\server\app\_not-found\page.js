(()=>{var e={};e.id=7409,e.ids=[7409],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20571:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u}),o(7352),o(35866),o(36944);var r=o(23191),a=o(88716),n=o(37922),i=o.n(n),s=o(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);o.d(t,l);let u=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.t.bind(o,35866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,35866,23)),"next/dist/client/components/not-found-error"]}],c=[],d="/_not-found/page",p={require:o,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},15075:(e,t,o)=>{Promise.resolve().then(o.bind(o,94494)),Promise.resolve().then(o.bind(o,52807)),Promise.resolve().then(o.bind(o,67520))},10138:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,12994,23)),Promise.resolve().then(o.t.bind(o,96114,23)),Promise.resolve().then(o.t.bind(o,9727,23)),Promise.resolve().then(o.t.bind(o,79671,23)),Promise.resolve().then(o.t.bind(o,41868,23)),Promise.resolve().then(o.t.bind(o,84759,23))},94494:(e,t,o)=>{"use strict";o.d(t,{CartProvider:()=>f,j:()=>m});var r=o(10326),a=o(17577);let n=(0,a.createContext)(null),i=e=>{},s=()=>null,l=(e,t)=>{if(!t||0===t.length)return e;let o=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>`${e.name}:${e.value}`).join("|");return`${e}__${o}`},u=e=>e.variantKey||e.product?.id||e.id,c=()=>s()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},d=(e,t)=>{let o=e.reduce((e,t)=>e+t.product.price*t.quantity,0),r=e.reduce((e,t)=>e+t.quantity,0),a=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:o,itemCount:r,total:o,finalTotal:o-a,totalDiscount:a}},p=(e,t)=>{let o;switch(t.type){case"ADD_ITEM":{let r;let a=l(t.payload.id,t.selectedVariants);if(e.items.find(e=>u(e)===a))r=e.items.map(e=>u(e)===a?{...e,quantity:e.quantity+1,variantKey:a}:e);else{let o={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:a};r=[...e.items,o]}let n=d(r,e.coupons.appliedCoupons);o={...e,items:r,...n,coupons:{...e.coupons,totalDiscount:n.totalDiscount}};break}case"REMOVE_ITEM":{let r=e.items.filter(e=>u(e)!==t.payload),a=d(r,e.coupons.appliedCoupons);o={...e,items:r,...a,coupons:{...e.coupons,totalDiscount:a.totalDiscount}};break}case"UPDATE_QUANTITY":{let r=e.items.map(e=>u(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),a=d(r,e.coupons.appliedCoupons);o={...e,items:r,...a,coupons:{...e.coupons,totalDiscount:a.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let r=[...e.coupons.appliedCoupons,t.payload],a=d(e.items,r);o={...e,...a,coupons:{...e.coupons,appliedCoupons:r,totalDiscount:a.totalDiscount}};break}case"REMOVE_COUPON":{let r=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),a=d(e.items,r);o={...e,...a,coupons:{...e.coupons,appliedCoupons:r,totalDiscount:a.totalDiscount}};break}case"CLEAR_COUPONS":{let t=d(e.items,[]);o={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":o={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return i(o),o},f=({children:e})=>{let[t,o]=(0,a.useReducer)(p,c());return r.jsx(n.Provider,{value:{state:t,dispatch:o},children:e})},m=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useCart must be used within a CartProvider");return e}},52807:(e,t,o)=>{"use strict";o.d(t,{NotificationProvider:()=>l,z:()=>s});var r=o(10326),a=o(17577),n=o(77109);let i=(0,a.createContext)(void 0),s=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},l=({children:e})=>{let{data:t,status:o}=(0,n.useSession)(),[s,l]=(0,a.useState)([]),[u,c]=(0,a.useState)(0),[d,p]=(0,a.useState)(!1),[f,m]=(0,a.useState)(null),h=(0,a.useCallback)(async(e={})=>{if(t?.user?.id)try{p(!0),m(null);let t=new URLSearchParams({page:(e.page||1).toString(),limit:(e.limit||10).toString(),...e.unreadOnly&&{unreadOnly:"true"}}),o=await fetch(`/api/notifications?${t}`),r=await o.json();r.success?(l(r.data.notifications),c(r.data.unreadCount)):m(r.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),m("Failed to fetch notifications")}finally{p(!1)}},[t?.user?.id]),x=(0,a.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&c(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[t?.user?.id]),C=(0,a.useCallback)(async e=>{if(t?.user?.id)try{let t=await fetch(`/api/notifications/${e}/read`,{method:"POST"}),o=await t.json();o.success?(l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),c(e=>Math.max(0,e-1))):m(o.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),m("Failed to mark notification as read")}},[t?.user?.id]),v=(0,a.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(l(e=>e.map(e=>({...e,isRead:!0}))),c(0)):m(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),m("Failed to mark all notifications as read")}},[t?.user?.id]);return(0,a.useEffect)(()=>{"authenticated"===o&&t?.user?.id&&(h({limit:5}),x())},[o,t?.user?.id,h,x]),(0,a.useEffect)(()=>{if(!t?.user?.id)return;let e=setInterval(()=>{x()},3e4);return()=>clearInterval(e)},[t?.user?.id,x]),r.jsx(i.Provider,{value:{notifications:s,unreadCount:u,loading:d,error:f,fetchNotifications:h,markAsRead:C,markAllAsRead:v,refreshUnreadCount:x},children:e})}},67520:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});var r=o(10326),a=o(77109);function n({children:e}){return r.jsx(a.SessionProvider,{children:e})}},36944:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>p,metadata:()=>c,viewport:()=>d});var r=o(19510),a=o(77366),n=o.n(a);o(67272);var i=o(68570);let s=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let u=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let c={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},d={width:"device-width",initialScale:1,themeColor:"#16a34a"};function p({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:n().className,children:r.jsx(l,{children:r.jsx(u,{children:r.jsx(s,{children:e})})})})})}},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{isNotFoundError:function(){return a},notFound:function(){return r}});let o="NEXT_NOT_FOUND";function r(){let e=Error(o);throw e.digest=o,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===o}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,o)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return a},default:function(){return n}});let r=o(16399),a="next/dist/client/components/parallel-route-default.js";function n(){(0,r.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[9276,8571],()=>o(20571));module.exports=r})();