(()=>{var e={};e.id=1899,e.ids=[1899],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19980:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(982),s(36944),s(35866);var a=s(23191),r=s(88716),o=s(37922),i=s.n(o),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["addresses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,982)),"C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx"],u="/addresses/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/addresses/page",pathname:"/addresses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27051:(e,t,s)=>{Promise.resolve().then(s.bind(s,39388))},15075:(e,t,s)=>{Promise.resolve().then(s.bind(s,94494)),Promise.resolve().then(s.bind(s,52807)),Promise.resolve().then(s.bind(s,67520))},10138:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},39388:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(10326);s(17577);var r=s(35047),o=s(86333),i=s(83855),n=s(77636),l=s(69508),c=s(98091);let d=()=>{let e=(0,r.useRouter)();return a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[a.jsx("div",{className:"flex items-center mb-8",children:(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[a.jsx(o.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Back"})]})}),a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shipping Addresses"}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("button",{className:"w-full flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-xl hover:border-green-400 hover:bg-green-50 transition-colors",children:[a.jsx(i.Z,{className:"w-6 h-6 text-gray-400"}),a.jsx("span",{className:"text-gray-600 font-medium",children:"Add New Address"})]})}),[{id:"1",firstName:"John",lastName:"Doe",company:"Tech Corp",address1:"123 Main Street",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"India",phone:"+91 99878 10707",isDefault:!0},{id:"2",firstName:"John",lastName:"Doe",company:"",address1:"456 Oak Avenue",address2:"",city:"Brooklyn",state:"NY",postalCode:"11201",country:"India",phone:"+91 99878 10707",isDefault:!1}].map(e=>a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mt-1",children:a.jsx(n.Z,{className:"w-6 h-6 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&a.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&a.jsx("p",{className:"font-medium",children:e.company}),a.jsx("p",{children:e.address1}),e.address2&&a.jsx("p",{children:e.address2}),(0,a.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),a.jsx("p",{children:e.country}),e.phone&&a.jsx("p",{className:"font-medium",children:e.phone})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&a.jsx("button",{className:"px-4 py-2 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),a.jsx("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-4 h-4"})}),a.jsx("button",{className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:a.jsx(c.Z,{className:"w-4 h-4"})})]})]})},e.id)),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-2xl p-6 border border-blue-100",children:[a.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Address Information"}),a.jsx("p",{className:"text-blue-800 text-sm",children:"Your default address will be automatically selected during checkout. You can always change it before placing an order."})]})]})]})})}},94494:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>m,j:()=>h});var a=s(10326),r=s(17577);let o=(0,r.createContext)(null),i=e=>{},n=()=>null,l=(e,t)=>{if(!t||0===t.length)return e;let s=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>`${e.name}:${e.value}`).join("|");return`${e}__${s}`},c=e=>e.variantKey||e.product?.id||e.id,d=()=>n()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},u=(e,t)=>{let s=e.reduce((e,t)=>e+t.product.price*t.quantity,0),a=e.reduce((e,t)=>e+t.quantity,0),r=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:s,itemCount:a,total:s,finalTotal:s-r,totalDiscount:r}},p=(e,t)=>{let s;switch(t.type){case"ADD_ITEM":{let a;let r=l(t.payload.id,t.selectedVariants);if(e.items.find(e=>c(e)===r))a=e.items.map(e=>c(e)===r?{...e,quantity:e.quantity+1,variantKey:r}:e);else{let s={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:r};a=[...e.items,s]}let o=u(a,e.coupons.appliedCoupons);s={...e,items:a,...o,coupons:{...e.coupons,totalDiscount:o.totalDiscount}};break}case"REMOVE_ITEM":{let a=e.items.filter(e=>c(e)!==t.payload),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"UPDATE_QUANTITY":{let a=e.items.map(e=>c(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let a=[...e.coupons.appliedCoupons,t.payload],r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"REMOVE_COUPON":{let a=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"CLEAR_COUPONS":{let t=u(e.items,[]);s={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":s={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return i(s),s},m=({children:e})=>{let[t,s]=(0,r.useReducer)(p,d());return a.jsx(o.Provider,{value:{state:t,dispatch:s},children:e})},h=()=>{let e=(0,r.useContext)(o);if(!e)throw Error("useCart must be used within a CartProvider");return e}},52807:(e,t,s)=>{"use strict";s.d(t,{NotificationProvider:()=>l,z:()=>n});var a=s(10326),r=s(17577),o=s(77109);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},l=({children:e})=>{let{data:t,status:s}=(0,o.useSession)(),[n,l]=(0,r.useState)([]),[c,d]=(0,r.useState)(0),[u,p]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),x=(0,r.useCallback)(async(e={})=>{if(t?.user?.id)try{p(!0),h(null);let t=new URLSearchParams({page:(e.page||1).toString(),limit:(e.limit||10).toString(),...e.unreadOnly&&{unreadOnly:"true"}}),s=await fetch(`/api/notifications?${t}`),a=await s.json();a.success?(l(a.data.notifications),d(a.data.unreadCount)):h(a.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),h("Failed to fetch notifications")}finally{p(!1)}},[t?.user?.id]),f=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&d(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[t?.user?.id]),y=(0,r.useCallback)(async e=>{if(t?.user?.id)try{let t=await fetch(`/api/notifications/${e}/read`,{method:"POST"}),s=await t.json();s.success?(l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),d(e=>Math.max(0,e-1))):h(s.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),h("Failed to mark notification as read")}},[t?.user?.id]),v=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(l(e=>e.map(e=>({...e,isRead:!0}))),d(0)):h(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),h("Failed to mark all notifications as read")}},[t?.user?.id]);return(0,r.useEffect)(()=>{"authenticated"===s&&t?.user?.id&&(x({limit:5}),f())},[s,t?.user?.id,x,f]),(0,r.useEffect)(()=>{if(!t?.user?.id)return;let e=setInterval(()=>{f()},3e4);return()=>clearInterval(e)},[t?.user?.id,f]),a.jsx(i.Provider,{value:{notifications:n,unreadCount:c,loading:u,error:m,fetchNotifications:x,markAsRead:y,markAllAsRead:v,refreshUnreadCount:f},children:e})}},67520:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(10326),r=s(77109);function o({children:e}){return a.jsx(r.SessionProvider,{children:e})}},76557:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(17577),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let s=(0,a.forwardRef)(({color:s="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:c="",children:d,...u},p)=>(0,a.createElement)("svg",{ref:p,...r,width:i,height:i,stroke:s,strokeWidth:l?24*Number(n)/Number(i):n,className:["lucide",`lucide-${o(e)}`,c].join(" "),...u},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return s.displayName=`${e}`,s}},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},77636:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},69508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},982:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\addresses\page.tsx#default`)},36944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>d,viewport:()=>u});var a=s(19510),r=s(77366),o=s.n(r);s(67272);var i=s(68570);let n=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let d={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},u={width:"device-width",initialScale:1,themeColor:"#16a34a"};function p({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:o().className,children:a.jsx(l,{children:a.jsx(c,{children:a.jsx(n,{children:e})})})})})}},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571],()=>s(19980));module.exports=a})();