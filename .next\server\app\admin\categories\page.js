(()=>{var e={};e.id=4331,e.ids=[4331],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93077:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d}),a(14285),a(90596),a(36944),a(35866);var s=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(t,c);let d=["",{children:["admin",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,14285)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx"],x="/admin/categories/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/categories/page",pathname:"/admin/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},67319:(e,t,a)=>{Promise.resolve().then(a.bind(a,72156))},72156:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var s=a(10326),r=a(17577),i=a.n(r),l=a(88307),n=a(83855),c=a(75290),d=a(69508),o=a(98091);let x=()=>{let[e,t]=(0,r.useState)(""),[a,x]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),[h,y]=(0,r.useState)(null),[m,g]=(0,r.useState)([]),[f,j]=(0,r.useState)(!0),[k,v]=(0,r.useState)(null);(0,r.useEffect)(()=>{b()},[]);let b=async()=>{try{j(!0);let e=await fetch("/api/categories"),t=await e.json();t.success?g(t.data):v("Failed to fetch categories")}catch(e){console.error("Error fetching categories:",e),v("Failed to fetch categories")}finally{j(!1)}},w=m.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())),N=e=>{y(e),p(!0)},Z=async e=>{if(confirm("Are you sure you want to delete this category?"))try{(await fetch(`/api/categories/${e}`,{method:"DELETE"})).ok?g(t=>t.filter(t=>t.id!==e)):alert("Failed to delete category")}catch(e){console.error("Error deleting category:",e),alert("Failed to delete category")}};return(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("div",{className:"mb-6 flex flex-wrap items-center justify-between gap-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search categories...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),s.jsx(l.Z,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),(0,s.jsxs)("button",{onClick:()=>{x(!0)},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[s.jsx(n.Z,{className:"h-5 w-5"}),"Add Category"]})]})}),f?s.jsx("div",{className:"flex items-center justify-center py-8",children:s.jsx(c.Z,{className:"h-8 w-8 animate-spin text-green-600"})}):k?s.jsx("div",{className:"text-center py-8 text-red-600",children:k}):0===w.length?s.jsx("div",{className:"text-center py-8 text-gray-500",children:"No categories found"}):s.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Products"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created At"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),s.jsx("div",{className:"text-sm text-gray-500",children:e.slug})]}),s.jsx("td",{className:"px-6 py-4",children:s.jsx("div",{className:"text-sm text-gray-900",children:e.description||"-"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[e._count.products," products"]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm text-gray-900",children:new Date(e.createdAt).toLocaleDateString()})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[s.jsx("button",{onClick:()=>N(e),className:"text-green-600 hover:text-green-900 mr-3",children:s.jsx(d.Z,{className:"h-5 w-5"})}),s.jsx("button",{onClick:()=>Z(e.id),className:"text-red-600 hover:text-red-900",children:s.jsx(o.Z,{className:"h-5 w-5"})})]})]},e.id))})]})}),s.jsx(()=>{let[e,t]=(0,r.useState)({name:"",description:""}),[i,l]=(0,r.useState)(!1),n=async a=>{a.preventDefault(),l(!0);try{let a=await fetch("/api/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(a.ok){let e=await a.json();e.success?(g(t=>[...t,e.data]),x(!1),t({name:"",description:""})):alert("Failed to create category: "+e.error)}else alert("Failed to create category")}catch(e){console.error("Error creating category:",e),alert("Failed to create category")}finally{l(!1)}};return a?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Add New Category"}),s.jsx("button",{onClick:()=>x(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:n,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),s.jsx("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),s.jsx("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx("button",{type:"button",onClick:()=>x(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),s.jsx("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",disabled:i,children:i?"Creating...":"Add Category"})]})]})]})}):null},{}),s.jsx(()=>{let[e,t]=(0,r.useState)({name:h?.name||"",description:h?.description||""}),[a,l]=(0,r.useState)(!1);i().useEffect(()=>{h&&t({name:h.name,description:h.description||""})},[h]);let n=async t=>{if(t.preventDefault(),h){l(!0);try{let t=await fetch(`/api/categories/${h.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(t.ok){let e=await t.json();e.success?(g(t=>t.map(t=>t.id===h.id?e.data:t)),p(!1),y(null)):alert("Failed to update category: "+e.error)}else alert("Failed to update category")}catch(e){console.error("Error updating category:",e),alert("Failed to update category")}finally{l(!1)}}};return u?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Edit Category"}),s.jsx("button",{onClick:()=>{p(!1),y(null)},className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:n,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),s.jsx("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),s.jsx("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx("button",{type:"button",onClick:()=>{p(!1),y(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),s.jsx("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",disabled:a,children:a?"Saving...":"Save Changes"})]})]})]})}):null},{})]})}},6507:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},95920:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},83855:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88307:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69508:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},40765:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,a)=>{"use strict";var s=a(77389);a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},14285:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\categories\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[9276,8571,3599,6879],()=>a(93077));module.exports=s})();