(()=>{var e={};e.id=9699,e.ids=[9699],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},65462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c}),s(30640),s(90596),s(36944),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c=["",{children:["admin",{children:["newsletter",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30640)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\newsletter\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\newsletter\\page.tsx"],x="/admin/newsletter/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/newsletter/page",pathname:"/admin/newsletter",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36129:(e,t,s)=>{Promise.resolve().then(s.bind(s,15430))},15430:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(10326),r=s(17577),i=s(77109),n=s(35047),l=s(92878),d=s(24061),c=s(12714),o=s(91216),x=s(31540);let m=()=>{let{data:e,status:t}=(0,i.useSession)(),s=(0,n.useRouter)(),[m,p]=(0,r.useState)(!0),[u,g]=(0,r.useState)([]),[h,b]=(0,r.useState)({page:1,limit:50,total:0,pages:0}),[y,f]=(0,r.useState)({total:0,active:0,inactive:0}),[j,v]=(0,r.useState)({active:"all",source:"",search:""}),[w,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if("loading"!==t){if(!e||"ADMIN"!==e.user.role){s.push("/admin/login");return}k()}},[e,t,s,h.page,j]);let k=async()=>{try{p(!0);let e=new URLSearchParams({page:h.page.toString(),limit:h.limit.toString()});"all"!==j.active&&e.append("active",j.active),j.source&&e.append("source",j.source);let t=await fetch(`/api/newsletter?${e}`),s=await t.json();s.success&&(g(s.data.subscribers),b(s.data.pagination),f(s.data.stats))}catch(e){console.error("Error fetching subscribers:",e)}finally{p(!1)}},S=async(e="csv")=>{try{N(!0);let t=new URLSearchParams({format:e});"all"!==j.active&&t.append("active",j.active),j.source&&t.append("source",j.source);let s=await fetch(`/api/newsletter/export?${t}`);if("csv"===e){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`newsletter-subscribers-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}else{let e=await s.json(),t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=`newsletter-subscribers-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error exporting subscribers:",e)}finally{N(!1)}},U=e=>{b(t=>({...t,page:e}))};return"loading"===t||m?a.jsx(l.fq,{}):a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Newsletter Subscribers"}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Manage your newsletter subscribers and export data"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(d.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Subscribers"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:y.total})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(c.Z,{className:"w-8 h-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Subscribers"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:y.active})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(o.Z,{className:"w-8 h-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Unsubscribed"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:y.inactive})]})]})})]}),a.jsx("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:j.active,onChange:e=>v(t=>({...t,active:e.target.value})),className:"rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",children:[a.jsx("option",{value:"all",children:"All Subscribers"}),a.jsx("option",{value:"true",children:"Active Only"}),a.jsx("option",{value:"false",children:"Unsubscribed Only"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Source"}),(0,a.jsxs)("select",{value:j.source,onChange:e=>v(t=>({...t,source:e.target.value})),className:"rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",children:[a.jsx("option",{value:"",children:"All Sources"}),a.jsx("option",{value:"homepage",children:"Homepage"}),a.jsx("option",{value:"checkout",children:"Checkout"}),a.jsx("option",{value:"product",children:"Product Page"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>S("csv"),disabled:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),w?"Exporting...":"Export CSV"]}),(0,a.jsxs)("button",{onClick:()=>S("json"),disabled:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),w?"Exporting...":"Export JSON"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subscriber"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Source"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subscribed"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Unsubscribed"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.email}),e.name&&a.jsx("div",{className:"text-sm text-gray-500",children:e.name})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Unsubscribed"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.source||"Unknown"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.subscribedAt).toLocaleDateString()}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.unsubscribedAt?new Date(e.unsubscribedAt).toLocaleDateString():"-"})]},e.id))})]})}),h.pages>1&&(0,a.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[a.jsx("button",{onClick:()=>U(h.page-1),disabled:h.page<=1,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),a.jsx("button",{onClick:()=>U(h.page+1),disabled:h.page>=h.pages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[a.jsx("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",a.jsx("span",{className:"font-medium",children:(h.page-1)*h.limit+1})," ","to"," ",a.jsx("span",{className:"font-medium",children:Math.min(h.page*h.limit,h.total)})," ","of"," ",a.jsx("span",{className:"font-medium",children:h.total})," results"]})}),a.jsx("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[a.jsx("button",{onClick:()=>U(h.page-1),disabled:h.page<=1,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),Array.from({length:Math.min(5,h.pages)},(e,t)=>{let s=t+1;return a.jsx("button",{onClick:()=>U(s),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${s===h.page?"z-10 bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:s},s)}),a.jsx("button",{onClick:()=>U(h.page+1),disabled:h.page>=h.pages,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"Next"})]})})]})]})]})]})})}},31540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},91216:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},30640:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\newsletter\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571,3599,6879,9268],()=>s(65462));module.exports=a})();