(()=>{var e={};e.id=3614,e.ids=[3614],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13989:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d}),s(56377),s(90596),s(36944),s(35866);var a=s(23191),r=s(88716),i=s(37922),l=s.n(i),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["admin",{children:["notifications",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56377)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\history\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\history\\page.tsx"],x="/admin/notifications/history/page",h={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/notifications/history/page",pathname:"/admin/notifications/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78306:(e,t,s)=>{Promise.resolve().then(s.bind(s,19777))},19777:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(10326),r=s(17577),i=s(35047),l=s(86333),n=s(88307),c=s(87888),d=s(6507),o=s(79635),x=s(48998),h=s(12714),u=s(54659),p=s(91470);let y=()=>{let e=(0,i.useRouter)(),[t,s]=(0,r.useState)([]),[y,m]=(0,r.useState)(!0),[g,v]=(0,r.useState)(null),[j,f]=(0,r.useState)(""),[k,b]=(0,r.useState)(""),[N,Z]=(0,r.useState)(""),[w,P]=(0,r.useState)(""),[M,S]=(0,r.useState)(1),[E,R]=(0,r.useState)(1);(0,r.useEffect)(()=>{A()},[M,k,N,w]);let A=async()=>{try{m(!0);let e=new URLSearchParams({page:M.toString(),limit:"20",...j&&{search:j},...k&&{type:k},...N&&{priority:N},...w&&{status:w}}),t=await fetch(`/api/admin/notifications/history?${e}`),a=await t.json();a.success?(s(a.data.notifications),R(a.data.pagination.totalPages)):v("Failed to fetch notification history")}catch(e){console.error("Error fetching notifications:",e),v("Failed to fetch notification history")}finally{m(!1)}},C=()=>{S(1),A()},D=e=>{switch(e){case"ORDER_PLACED":return"bg-blue-100 text-blue-800";case"ORDER_SHIPPED":return"bg-green-100 text-green-800";case"ORDER_DELIVERED":return"bg-emerald-100 text-emerald-800";case"ADMIN_MESSAGE":return"bg-purple-100 text-purple-800";case"BROADCAST":return"bg-orange-100 text-orange-800";case"PROMOTIONAL":return"bg-pink-100 text-pink-800";default:return"bg-gray-100 text-gray-800"}},_=e=>{switch(e){case"URGENT":return"bg-red-100 text-red-800";case"HIGH":return"bg-orange-100 text-orange-800";case"NORMAL":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},O=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Notification History"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"View all sent notifications"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx("input",{type:"text",placeholder:"Search notifications...",value:j,onChange:e=>f(e.target.value),onKeyPress:e=>"Enter"===e.key&&C(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:k,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[a.jsx("option",{value:"",children:"All Types"}),a.jsx("option",{value:"ORDER_PLACED",children:"Order Placed"}),a.jsx("option",{value:"ORDER_SHIPPED",children:"Order Shipped"}),a.jsx("option",{value:"ORDER_DELIVERED",children:"Order Delivered"}),a.jsx("option",{value:"ADMIN_MESSAGE",children:"Admin Message"}),a.jsx("option",{value:"BROADCAST",children:"Broadcast"}),a.jsx("option",{value:"PROMOTIONAL",children:"Promotional"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:N,onChange:e=>Z(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[a.jsx("option",{value:"",children:"All Priorities"}),a.jsx("option",{value:"URGENT",children:"Urgent"}),a.jsx("option",{value:"HIGH",children:"High"}),a.jsx("option",{value:"NORMAL",children:"Normal"}),a.jsx("option",{value:"LOW",children:"Low"})]})}),a.jsx("div",{children:(0,a.jsxs)("select",{value:w,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[a.jsx("option",{value:"",children:"All Status"}),a.jsx("option",{value:"read",children:"Read"}),a.jsx("option",{value:"unread",children:"Unread"}),a.jsx("option",{value:"email_sent",children:"Email Sent"}),a.jsx("option",{value:"email_failed",children:"Email Failed"})]})})]}),a.jsx("div",{className:"mt-4 flex justify-end",children:(0,a.jsxs)("button",{onClick:C,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[a.jsx(n.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Search"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[y?a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})}):g?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(c.Z,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),a.jsx("p",{className:"text-red-600",children:g}),a.jsx("button",{onClick:A,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):0===t.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"No notifications found"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:t.map(e=>a.jsx("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:a.jsx(d.Z,{className:"w-5 h-5 text-green-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${D(e.type)}`,children:e.type.replace("_"," ")}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${_(e.priority)}`,children:e.priority})]})]}),a.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(o.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.user.name||e.user.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(x.Z,{className:"w-3 h-3"}),a.jsx("span",{children:O(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isRead?(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[a.jsx(h.Z,{className:"w-3 h-3"}),a.jsx("span",{children:"Read"})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-gray-400",children:[a.jsx(h.Z,{className:"w-3 h-3"}),a.jsx("span",{children:"Unread"})]}),e.emailSent?(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[a.jsx(u.Z,{className:"w-3 h-3"}),a.jsx("span",{children:"Email Sent"})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-red-500",children:[a.jsx(p.Z,{className:"w-3 h-3"}),a.jsx("span",{children:"Email Failed"})]})]})]})]})]})},e.id))}),E>1&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Page ",M," of ",E]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>S(M-1),disabled:1===M,className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),a.jsx("button",{onClick:()=>S(M+1),disabled:M===E,className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]})}},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},54659:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},88307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},79635:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},91470:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},94019:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},56377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\notifications\history\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571,3599,6879],()=>s(13989));module.exports=a})();