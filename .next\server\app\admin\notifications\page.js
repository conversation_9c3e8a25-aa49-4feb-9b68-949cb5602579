(()=>{var e={};e.id=7395,e.ids=[7395],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93748:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>n}),s(44163),s(90596),s(36944),s(35866);var a=s(23191),i=s(88716),r=s(37922),l=s.n(r),d=s(95231),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let n=["",{children:["admin",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,44163)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx"],h="/admin/notifications/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/admin/notifications/page",pathname:"/admin/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},30968:(e,t,s)=>{Promise.resolve().then(s.bind(s,594))},594:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(10326),i=s(17577),r=s(90434),l=s(69436),d=s(40617);let o=(0,s(76557).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var n=s(88378),c=s(17069),h=s(12714),x=s(37358),u=s(83855);let p=()=>{let[e,t]=(0,i.useState)(null),[s,p]=(0,i.useState)(!0),[m,y]=(0,i.useState)(null);(0,i.useEffect)(()=>{g()},[]);let g=async()=>{try{p(!0);let e=await fetch("/api/admin/notifications/stats"),s=await e.json();s.success?t(s.stats):y("Failed to fetch notification statistics")}catch(e){console.error("Error fetching notification stats:",e),y("Failed to fetch notification statistics")}finally{p(!1)}},v=[{title:"Send Notification",description:"Send a notification to users",href:"/admin/notifications/send",icon:l.Z,color:"bg-green-500",textColor:"text-green-600"},{title:"Broadcast Message",description:"Send message to all users",href:"/admin/notifications/broadcast",icon:d.Z,color:"bg-blue-500",textColor:"text-blue-600"},{title:"Notification History",description:"View sent notifications",href:"/admin/notifications/history",icon:o,color:"bg-purple-500",textColor:"text-purple-600"},{title:"Templates",description:"Manage notification templates",href:"/admin/notifications/templates",icon:n.Z,color:"bg-orange-500",textColor:"text-orange-600"}],f=[{title:"Total Sent",value:e?.totalSent||0,icon:l.Z,color:"bg-blue-500"},{title:"Delivered",value:e?.totalDelivered||0,icon:c.Z,color:"bg-green-500"},{title:"Read",value:e?.totalRead||0,icon:h.Z,color:"bg-purple-500"},{title:"Recent Activity",value:e?.recentActivity||0,icon:x.Z,color:"bg-orange-500"}];return s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-2"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]},t))})]}):m?(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[a.jsx("p",{className:"text-red-600",children:m}),a.jsx("button",{onClick:g,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Notifications"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Manage and send notifications to your customers"})]}),(0,a.jsxs)(r.default,{href:"/admin/notifications/send",className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[a.jsx(u.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Send Notification"})]})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:f.map((e,t)=>{let s=e.icon;return a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.title}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value.toLocaleString()})]}),a.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center`,children:a.jsx(s,{className:"w-6 h-6 text-white"})})]})},t)})}),e&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Delivery Rate"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${e.deliveryRate}%`}})})}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.deliveryRate.toFixed(1),"%"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Read Rate"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.readRate}%`}})})}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.readRate.toFixed(1),"%"]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:v.map((e,t)=>{let s=e.icon;return a.jsx(r.default,{href:e.href,className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`,children:a.jsx(s,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:`font-semibold ${e.textColor} group-hover:text-gray-900 transition-colors`,children:e.title}),a.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]})},t)})})]})]})}},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},37358:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},69436:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},44163:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\notifications\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571,3599,6879],()=>s(93748));module.exports=a})();