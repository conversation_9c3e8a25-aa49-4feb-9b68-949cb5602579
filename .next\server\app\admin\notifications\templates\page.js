(()=>{var e={};e.id=4885,e.ids=[4885],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>n}),s(7370),s(90596),s(36944),s(35866);var a=s(23191),r=s(88716),l=s(37922),i=s.n(l),c=s(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);s.d(t,d);let n=["",{children:["admin",{children:["notifications",{children:["templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7370)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\templates\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\templates\\page.tsx"],x="/admin/notifications/templates/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/notifications/templates/page",pathname:"/admin/notifications/templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},86891:(e,t,s)=>{Promise.resolve().then(s.bind(s,1867))},1867:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(10326),r=s(17577),l=s(35047),i=s(86333),c=s(54659),d=s(87888),n=s(83855),o=s(36283),x=s(12714),m=s(69508),h=s(98091);let p=()=>{let e=(0,l.useRouter)(),[t,s]=(0,r.useState)([]),[p,y]=(0,r.useState)(!0),[u,g]=(0,r.useState)(null),[v,j]=(0,r.useState)(null),[k,b]=(0,r.useState)(!1),[f,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{Z()},[]);let Z=async()=>{try{y(!0);let e=await fetch("/api/admin/notifications/templates"),t=await e.json();t.success?s(t.templates):g("Failed to fetch templates")}catch(e){console.error("Error fetching templates:",e),g("Failed to fetch templates")}finally{y(!1)}},w=e=>{j(e),b(!0)},M=e=>{j(e),b(!0)},P=async e=>{if(confirm("Are you sure you want to delete this template?"))try{let a=await fetch(`/api/admin/notifications/templates/${e}`,{method:"DELETE"});(await a.json()).success?s(t.filter(t=>t.id!==e)):g("Failed to delete template")}catch(e){console.error("Error deleting template:",e),g("Failed to delete template")}},C=e=>{switch(e){case"order_placed":return"bg-blue-100 text-blue-800";case"order_shipped":return"bg-green-100 text-green-800";case"order_delivered":return"bg-emerald-100 text-emerald-800";case"price_drop_alert":return"bg-orange-100 text-orange-800";case"review_request":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},_=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return k&&v?(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[a.jsx("button",{onClick:()=>b(!1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Template Preview"}),a.jsx("p",{className:"text-gray-600 mt-1",children:v.name})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Template Details"}),a.jsx("span",{className:`px-3 py-1 text-sm font-medium rounded-full ${C(v.type)}`,children:v.type.replace("_"," ")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),a.jsx("p",{className:"text-gray-900",children:v.name})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),a.jsx("p",{className:"text-gray-900",children:v.type.replace("_"," ")})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("p",{className:`inline-flex items-center space-x-1 ${v.isActive?"text-green-600":"text-red-600"}`,children:[v.isActive?a.jsx(c.Z,{className:"w-4 h-4"}):a.jsx(d.Z,{className:"w-4 h-4"}),a.jsx("span",{children:v.isActive?"Active":"Inactive"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Created"}),a.jsx("p",{className:"text-gray-900",children:_(v.createdAt)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),a.jsx("div",{className:"bg-gray-50 rounded-lg p-3",children:a.jsx("p",{className:"text-gray-900",children:v.subject})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content"}),a.jsx("div",{className:"bg-gray-50 rounded-lg p-3",children:a.jsx("div",{className:"text-gray-900 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:v.content}})})]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Notification Templates"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage notification templates"})]})]}),(0,a.jsxs)("button",{onClick:()=>N(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[a.jsx(n.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Create Template"})]})]}),u&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2",children:[a.jsx(d.Z,{className:"w-5 h-5 text-red-600"}),a.jsx("span",{className:"text-red-700",children:u})]}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:p?a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})}):0===t.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx(o.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"No templates found"}),a.jsx("button",{onClick:()=>N(!0),className:"mt-2 text-green-600 hover:text-green-700 underline",children:"Create your first template"})]}):a.jsx("div",{className:"divide-y divide-gray-200",children:t.map(e=>a.jsx("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx(o.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${C(e.type)}`,children:e.type.replace("_"," ")}),e.isActive?a.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",children:"Active"}):a.jsx("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800",children:"Inactive"})]}),a.jsx("p",{className:"text-sm text-gray-600 mb-1",children:e.subject}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Created ",_(e.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>w(e),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Preview",children:a.jsx(x.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>M(e),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Edit",children:a.jsx(m.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>P(e.id),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Delete",children:a.jsx(h.Z,{className:"w-4 h-4"})})]})]})},e.id))})})]})}},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},54659:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36283:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},7370:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\notifications\templates\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571,3599,6879],()=>s(24696));module.exports=a})();