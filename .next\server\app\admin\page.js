(()=>{var e={};e.id=3,e.ids=[3],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},87301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),s(66136),s(90596),s(36944),s(35866);var r=s(23191),a=s(88716),n=s(37922),l=s.n(n),i=s(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,66136)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx"],m="/admin/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94492:(e,t,s)=>{Promise.resolve().then(s.bind(s,60110))},60110:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(10326),a=s(17577),n=s(48705),l=s(34565),i=s(24061),d=s(33734),o=s(17069);let c=(0,s(76557).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var m=s(57671),x=s(9891),u=s(92878);let p=()=>{let[e,t]=(0,a.useState)(null),[s,p]=(0,a.useState)(!0),[h,g]=(0,a.useState)(null);(0,a.useEffect)(()=>{j()},[]);let j=async()=>{try{p(!0);let e=await fetch("/api/dashboard/stats"),s=await e.json();s.success?t(s.data):g("Failed to fetch dashboard data")}catch(e){console.error("Error fetching dashboard data:",e),g("Failed to fetch dashboard data")}finally{p(!1)}};if(s)return r.jsx(u.xP,{count:4});if(h)return(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[r.jsx("p",{className:"text-red-600",children:h}),r.jsx("button",{onClick:j,className:"mt-2 text-red-600 hover:text-red-700 underline",children:"Try again"})]});let y=[{title:"Total Products",value:e?.overview.totalProducts||0,change:e?.growth.productsGrowth||"+0%",trend:"up",icon:n.Z,color:"bg-green-500"},{title:"Total Categories",value:e?.overview.totalCategories||0,change:e?.growth.categoriesGrowth||"+0%",trend:"up",icon:l.Z,color:"bg-blue-500"},{title:"Total Users",value:e?.overview.totalUsers||0,change:e?.growth.usersGrowth||"+0%",trend:"up",icon:i.Z,color:"bg-purple-500"},{title:"Featured Products",value:e?.overview.featuredProducts||0,change:"+0%",trend:"up",icon:d.Z,color:"bg-orange-500"}],v=e?.recent.products||[];return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Welcome back! Here's what's happening with your store."})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:y.map((e,t)=>{let s=e.icon,a="up"===e.trend?o.Z:c;return(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.title}),r.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value})]}),r.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center`,children:r.jsx(s,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{className:"flex items-center mt-4",children:[r.jsx(a,{className:`w-4 h-4 mr-1 ${"up"===e.trend?"text-green-600":"text-red-600"}`}),r.jsx("span",{className:`text-sm font-medium ${"up"===e.trend?"text-green-600":"text-red-600"}`,children:e.change}),r.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]},t)})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Orders"}),r.jsx("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),r.jsx("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500",children:"No orders yet"}),r.jsx("p",{className:"text-sm text-gray-400",children:"Orders will appear here once customers start purchasing"})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Products"}),r.jsx("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),r.jsx("div",{className:"p-6",children:r.jsx("div",{className:"space-y-4",children:v.length>0?v.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center flex-1",children:[r.jsx("img",{src:e.images[0]?.url||"/images/default-product.jpg",alt:e.name,className:"w-10 h-10 rounded-lg object-cover mr-3"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[r.jsx("span",{className:"text-sm text-gray-600",children:e.category.name}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Stock: ",e.quantity]})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[r.jsx("p",{className:"font-medium text-gray-900",children:(0,x.T4)(e.price)}),e.isFeatured&&r.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",children:"Featured"})]})]},e.id)):(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(n.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500",children:"No products yet"}),r.jsx("p",{className:"text-sm text-gray-400",children:"Add products to see them here"})]})})})]})]})]})}},9891:(e,t,s)=>{"use strict";function r(e){return function(e,t=!0){if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return`₹${s}`}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function n(e,t){return(e?a(e):a(t))||"product"}s.d(t,{GD:()=>a,T4:()=>r,w:()=>n})},33734:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},66136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,8571,3599,6879,9268],()=>s(87301));module.exports=r})();