(()=>{var e={};e.id=4122,e.ids=[4122],e.modules={21841:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},32694:e=>{"use strict";e.exports=require("http2")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},84492:e=>{"use strict";e.exports=require("node:stream")},2695:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(65917),s(90596),s(36944),s(35866);var r=s(23191),a=s(88716),l=s(37922),i=s.n(l),n=s(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65917)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\products\\page.tsx"],u="/admin/products/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12421:(e,t,s)=>{Promise.resolve().then(s.bind(s,47933))},47933:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var r=s(10326),a=s(17577),l=s(83855),i=s(75290),n=s(88307),o=s(41137),c=s(36283),d=s(63685),u=s(98091),x=s(69508),m=s(76557);let p=(0,m.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var g=s(88378),h=s(9891),y=s(94019),f=s(31215);let b=(0,m.Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),j=(0,m.Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),v=({faq:e,index:t,totalCount:s,isEditing:l,onEdit:i,onUpdate:n,onDelete:o,onMove:c})=>{let[d,m]=(0,a.useState)({question:e.question,answer:e.answer});return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 bg-white",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"bg-gray-100 text-gray-600 px-2 py-1 rounded text-sm font-medium",children:["#",t+1]}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[r.jsx("button",{onClick:()=>c(e,"up"),disabled:0===t,className:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move up",children:r.jsx(b,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>c(e,"down"),disabled:t===s-1,className:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move down",children:r.jsx(j,{className:"w-4 h-4"})})]})]}),r.jsx("div",{className:"flex space-x-2",children:l?(0,r.jsxs)(r.Fragment,{children:[r.jsx("button",{onClick:()=>{if(!d.question.trim()||!d.answer.trim()){alert("Please fill in both question and answer");return}n({...e,question:d.question,answer:d.answer})},className:"p-1 text-green-600 hover:text-green-700",title:"Save",children:r.jsx(f.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>{m({question:e.question,answer:e.answer}),i(null)},className:"p-1 text-gray-400 hover:text-gray-600",title:"Cancel",children:r.jsx(y.Z,{className:"w-4 h-4"})})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("button",{onClick:()=>i(e),className:"p-1 text-blue-600 hover:text-blue-700",title:"Edit",children:r.jsx(x.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>o(e.id),className:"p-1 text-red-600 hover:text-red-700",title:"Delete",children:r.jsx(u.Z,{className:"w-4 h-4"})})]})})]}),l?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question"}),r.jsx("input",{type:"text",value:d.question,onChange:e=>m({...d,question:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Answer"}),r.jsx("textarea",{value:d.answer,onChange:e=>m({...d,answer:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}):(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:e.question}),r.jsx("p",{className:"text-gray-600 whitespace-pre-wrap",children:e.answer})]})]})},N=({isOpen:e,onClose:t,product:s})=>{let[i,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[x,m]=(0,a.useState)(!1),[p,g]=(0,a.useState)({question:"",answer:""});(0,a.useEffect)(()=>{e&&s&&h()},[e,s]);let h=async()=>{try{c(!0);let e=await fetch(`/api/products/${s.id}/faqs`),t=await e.json();t.success&&n(t.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{c(!1)}},b=async()=>{if(!p.question.trim()||!p.answer.trim()){alert("Please fill in both question and answer");return}try{let e=await fetch(`/api/products/${s.id}/faqs`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)}),t=await e.json();t.success?(n(e=>[...e,t.data]),g({question:"",answer:""}),m(!1)):alert("Failed to add FAQ")}catch(e){console.error("Error adding FAQ:",e),alert("Failed to add FAQ")}},j=async e=>{try{let t=await fetch(`/api/products/${s.id}/faqs/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();r.success?(n(t=>t.map(t=>t.id===e.id?r.data:t)),u(null)):alert("Failed to update FAQ")}catch(e){console.error("Error updating FAQ:",e),alert("Failed to update FAQ")}},N=async e=>{if(confirm("Are you sure you want to delete this FAQ?"))try{let t=await fetch(`/api/products/${s.id}/faqs/${e}`,{method:"DELETE"});(await t.json()).success?n(t=>t.filter(t=>t.id!==e)):alert("Failed to delete FAQ")}catch(e){console.error("Error deleting FAQ:",e),alert("Failed to delete FAQ")}},w=async(e,t)=>{let s=i.findIndex(t=>t.id===e.id),r="up"===t?s-1:s+1;if(r<0||r>=i.length)return;let a=[...i];[a[s],a[r]]=[a[r],a[s]],a[s].position=s,a[r].position=r,n(a),await j(a[r]),await j(a[s])};return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage FAQs - ",s.name]}),r.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:r.jsx(y.Z,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("button",{onClick:()=>m(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[r.jsx(l.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add FAQ"})]})}),x&&(0,r.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("h3",{className:"text-lg font-medium mb-4",children:"Add New FAQ"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question"}),r.jsx("input",{type:"text",value:p.question,onChange:e=>g({...p,question:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter the question"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Answer"}),r.jsx("textarea",{value:p.answer,onChange:e=>g({...p,answer:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter the answer"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:b,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[r.jsx(f.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Save FAQ"})]}),r.jsx("button",{onClick:()=>{m(!1),g({question:"",answer:""})},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors",children:"Cancel"})]})]})]}),o?r.jsx("div",{className:"text-center py-8",children:r.jsx("div",{className:"text-gray-500",children:"Loading FAQs..."})}):0===i.length?r.jsx("div",{className:"text-center py-8",children:r.jsx("div",{className:"text-gray-500",children:"No FAQs found. Add your first FAQ above."})}):r.jsx("div",{className:"space-y-4",children:i.map((e,t)=>r.jsx(v,{faq:e,index:t,totalCount:i.length,isEditing:d?.id===e.id,onEdit:u,onUpdate:j,onDelete:N,onMove:w},e.id))})]})]})}):null};var w=s(48705);let C=({variation:e,isEditing:t,onEdit:s,onUpdate:l,onDelete:i})=>{let[n,o]=(0,a.useState)({name:e.name,value:e.value,price:e.price?.toString()||"",pricingMode:e.pricingMode||"INCREMENT"});return r.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-white",children:(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[r.jsx("div",{className:"flex-1",children:t?(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Name"}),r.jsx("input",{type:"text",value:n.name,onChange:e=>o({...n,name:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Value"}),r.jsx("input",{type:"text",value:n.value,onChange:e=>o({...n,value:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),r.jsx("input",{type:"number",step:"0.01",value:n.price,onChange:e=>o({...n,price:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Pricing Mode"}),(0,r.jsxs)("select",{value:n.pricingMode,onChange:e=>o({...n,pricingMode:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",children:[r.jsx("option",{value:"INCREMENT",children:"Add to Base"}),r.jsx("option",{value:"REPLACE",children:"Replace Base"}),r.jsx("option",{value:"FIXED",children:"Fixed Price"})]})]})]}):(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-xs text-gray-500",children:"Name"}),r.jsx("div",{className:"font-medium",children:e.name})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-xs text-gray-500",children:"Value"}),r.jsx("div",{className:"font-medium",children:e.value})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-xs text-gray-500",children:"Price Adjustment"}),r.jsx("div",{className:"font-medium",children:e.price?(0,h.T4)(e.price):"No adjustment"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-xs text-gray-500",children:"Mode"}),r.jsx("div",{className:"font-medium text-xs",children:"REPLACE"===e.pricingMode?"Replace":"FIXED"===e.pricingMode?"Fixed":"Add to Base"})]})]})}),r.jsx("div",{className:"flex space-x-2 ml-4",children:t?(0,r.jsxs)(r.Fragment,{children:[r.jsx("button",{onClick:()=>{if(!n.name.trim()||!n.value.trim()){alert("Please fill in both name and value");return}l({...e,name:n.name,value:n.value,price:n.price?parseFloat(n.price):void 0,pricingMode:n.pricingMode})},className:"p-1 text-green-600 hover:text-green-700",title:"Save",children:r.jsx(f.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>{o({name:e.name,value:e.value,price:e.price?.toString()||"",pricingMode:e.pricingMode||"INCREMENT"}),s(null)},className:"p-1 text-gray-400 hover:text-gray-600",title:"Cancel",children:r.jsx(y.Z,{className:"w-4 h-4"})})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("button",{onClick:()=>s(e),className:"p-1 text-blue-600 hover:text-blue-700",title:"Edit",children:r.jsx(x.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>i(e.id),className:"p-1 text-red-600 hover:text-red-700",title:"Delete",children:r.jsx(u.Z,{className:"w-4 h-4"})})]})})]})})},k=({isOpen:e,onClose:t,product:s})=>{let[i,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[x,m]=(0,a.useState)(!1),[p,g]=(0,a.useState)({name:"",value:"",price:"",pricingMode:"INCREMENT"});(0,a.useEffect)(()=>{e&&s&&h()},[e,s]);let h=async()=>{try{c(!0);let e=await fetch(`/api/products/${s.id}/variations`),t=await e.json();t.success&&n(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{c(!1)}},b=async()=>{if(!p.name.trim()||!p.value.trim()){alert("Please fill in both name and value");return}try{let e=await fetch(`/api/products/${s.id}/variations`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:p.name,value:p.value,price:p.price?parseFloat(p.price):null,pricingMode:p.pricingMode})}),t=await e.json();t.success?(n(e=>[...e,t.data]),g({name:"",value:"",price:"",pricingMode:"INCREMENT"}),m(!1)):alert(t.error||"Failed to add variation")}catch(e){console.error("Error adding variation:",e),alert("Failed to add variation")}},j=async e=>{try{let t=await fetch(`/api/products/${s.id}/variations/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();r.success?(n(t=>t.map(t=>t.id===e.id?r.data:t)),u(null)):alert(r.error||"Failed to update variation")}catch(e){console.error("Error updating variation:",e),alert("Failed to update variation")}},v=async e=>{if(confirm("Are you sure you want to delete this variation?"))try{let t=await fetch(`/api/products/${s.id}/variations/${e}`,{method:"DELETE"});(await t.json()).success?n(t=>t.filter(t=>t.id!==e)):alert("Failed to delete variation")}catch(e){console.error("Error deleting variation:",e),alert("Failed to delete variation")}},N=i.reduce((e,t)=>(e[t.name]||(e[t.name]=[]),e[t.name].push(t),e),{});return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center space-x-2",children:[r.jsx(w.Z,{className:"w-5 h-5"}),(0,r.jsxs)("span",{children:["Manage Variations - ",s.name]})]}),r.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:r.jsx(y.Z,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("button",{onClick:()=>m(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[r.jsx(l.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add Variation"})]})}),x&&(0,r.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("h3",{className:"text-lg font-medium mb-4",children:"Add New Variation"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name (e.g., Size, Color)"}),r.jsx("input",{type:"text",value:p.name,onChange:e=>g({...p,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Size"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value (e.g., Large, Red)"}),r.jsx("input",{type:"text",value:p.value,onChange:e=>g({...p,value:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Large"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),r.jsx("input",{type:"number",step:"0.01",value:p.price,onChange:e=>g({...p,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"0.00"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pricing Mode"}),(0,r.jsxs)("select",{value:p.pricingMode,onChange:e=>g({...p,pricingMode:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[r.jsx("option",{value:"INCREMENT",children:"Add to Base Price"}),r.jsx("option",{value:"REPLACE",children:"Replace Base Price"}),r.jsx("option",{value:"FIXED",children:"Use as Fixed Price"})]}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"How this variation affects the final price"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 mt-4",children:[(0,r.jsxs)("button",{onClick:b,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[r.jsx(f.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Save Variation"})]}),r.jsx("button",{onClick:()=>{m(!1),g({name:"",value:"",price:"",pricingMode:"INCREMENT"})},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors",children:"Cancel"})]})]}),o?r.jsx("div",{className:"text-center py-8",children:r.jsx("div",{className:"text-gray-500",children:"Loading variations..."})}):0===i.length?r.jsx("div",{className:"text-center py-8",children:r.jsx("div",{className:"text-gray-500",children:"No variations found. Add your first variation above."})}):r.jsx("div",{className:"space-y-6",children:Object.entries(N).map(([e,t])=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[r.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4 capitalize",children:e}),r.jsx("div",{className:"space-y-3",children:t.map(e=>r.jsx(C,{variation:e,isEditing:d?.id===e.id,onEdit:u,onUpdate:j,onDelete:v},e.id))})]},e))})]})]})}):null},S=(0,m.Z)("Move",[["polyline",{points:"5 9 2 12 5 15",key:"1r5uj5"}],["polyline",{points:"9 5 12 2 15 5",key:"5v383o"}],["polyline",{points:"15 19 12 22 9 19",key:"g7qi8m"}],["polyline",{points:"19 9 22 12 19 15",key:"tpp73q"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}]]);var F=s(12714),P=s(71709),E=s(49758),A=s(39572),Z=s(924),M=s(29389),q=s(32933),L=s(47483);let D=({isOpen:e,onClose:t,onSelect:s,allowedTypes:i=["image","video","document"],title:o="Select Media",multiple:c=!1,currentSelection:d})=>{let[u,x]=(0,a.useState)([]),[m,p]=(0,a.useState)(!0),[g,h]=(0,a.useState)(""),[f,b]=(0,a.useState)(null),[j,v]=(0,a.useState)([]),[N,w]=(0,a.useState)("grid"),[C,k]=(0,a.useState)(!1),S=(0,a.useRef)(null);(0,a.useEffect)(()=>{e&&I()},[e]);let F=e=>{c?j.some(t=>t.key===e.key)?v(j.filter(t=>t.key!==e.key)):v([...j,e]):b(e)},D=()=>{c?j.forEach(e=>s(e)):f&&s(f),t()},I=async()=>{try{p(!0);let e=await fetch("/api/media/list"),t=await e.json();t.success&&x(t.files.filter(e=>i.includes(e.type)))}catch(e){console.error("Error loading files:",e)}finally{p(!1)}},$=u.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())),R=e=>{switch(e){case"image":return r.jsx(P.Z,{className:"w-5 h-5"});case"video":return r.jsx(E.Z,{className:"w-5 h-5"});default:return r.jsx(A.Z,{className:"w-5 h-5"})}},T=async e=>{k(!0);let t=Array.from(e).map(async e=>{let t=new FormData;t.append("file",e),t.append("folder","uploads");try{let e=await fetch("/api/media/upload",{method:"POST",body:t});return await e.json()}catch(t){return console.error(`Upload failed for ${e.name}:`,t),{success:!1,error:t instanceof Error?t.message:"Upload failed"}}}),s=(await Promise.all(t)).filter(e=>e.success);s.length>0&&(I(),!c&&s[0]?.file&&b(s[0].file)),k(!1),S.current&&(S.current.value="")};return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:o}),r.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:r.jsx(y.Z,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search files...",value:g,onChange:e=>h(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{onClick:()=>{S.current?.click()},disabled:C,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:C?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Uploading..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"Upload"]})}),(0,r.jsxs)("div",{className:"flex border border-gray-300 rounded-lg",children:[r.jsx("button",{onClick:()=>w("grid"),className:`p-2 ${"grid"===N?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"}`,children:r.jsx(Z.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>w("list"),className:`p-2 ${"list"===N?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"}`,children:r.jsx(M.Z,{className:"w-4 h-4"})})]})]})]}),r.jsx("input",{ref:S,type:"file",accept:"image/*,video/*,.pdf",multiple:!0,onChange:e=>e.target.files&&T(e.target.files),className:"hidden"})]}),r.jsx("div",{className:"flex-1 overflow-auto p-6",children:m?r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})}):0===$.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(P.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500 text-lg",children:"No files found"}),r.jsx("p",{className:"text-gray-400",children:"Try adjusting your search or upload new files"})]}):"grid"===N?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:$.map(e=>{let t=c?j.some(t=>t.key===e.key):f?.key===e.key;return(0,r.jsxs)("div",{onClick:()=>F(e),className:`bg-white rounded-lg border-2 p-3 cursor-pointer transition-all hover:shadow-md ${t?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[(0,r.jsxs)("div",{className:"relative",children:[t&&r.jsx("div",{className:"absolute top-2 right-2 bg-green-600 text-white rounded-full p-1",children:r.jsx(q.Z,{className:"w-3 h-3"})}),r.jsx("div",{className:"aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden",children:"image"===e.type?r.jsx("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):r.jsx("div",{className:"text-gray-400",children:R(e.type)})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",title:e.name,children:e.name}),r.jsx("p",{className:"text-xs text-gray-500",children:(0,L.sS)(e.size)})]})]},e.key)})}):r.jsx("div",{className:"space-y-2",children:$.map(e=>{let t=c?j.some(t=>t.key===e.key):f?.key===e.key;return(0,r.jsxs)("div",{onClick:()=>F(e),className:`flex items-center p-3 rounded-lg border cursor-pointer transition-all ${t?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:[r.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3 overflow-hidden",children:"image"===e.type?r.jsx("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):r.jsx("div",{className:"text-gray-400",children:R(e.type)})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," • ",(0,L.sS)(e.size)," • ",e.folder||"Root"]})]}),t&&r.jsx("div",{className:"bg-green-600 text-white rounded-full p-1 ml-3",children:r.jsx(q.Z,{className:"w-4 h-4"})})]},e.key)})})}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200",children:[r.jsx("div",{className:"text-sm text-gray-500",children:c?`${j.length} file(s) selected`:f?`Selected: ${f.name}`:"No file selected"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),r.jsx("button",{onClick:()=>{D(),b(null),v([])},disabled:c?0===j.length:!f,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Select Files":"Select File"})]})]})]})}):null},I=({value:e,onChange:t,label:s="Image",placeholder:l="Select an image",className:i=""})=>{let[n,o]=(0,a.useState)(!1);return(0,r.jsxs)("div",{className:i,children:[s&&r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:s}),(0,r.jsxs)("div",{className:"space-y-3",children:[e&&(0,r.jsxs)("div",{className:"relative inline-block",children:[r.jsx("img",{src:e,alt:"Selected image",className:"w-32 h-32 object-cover rounded-lg border border-gray-300"}),r.jsx("button",{type:"button",onClick:()=>{t("")},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:r.jsx(y.Z,{className:"w-3 h-3"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>o(!0),className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[r.jsx(P.Z,{className:"w-4 h-4 mr-2"}),e?"Change Image":"Select Image"]}),!e&&r.jsx("span",{className:"text-sm text-gray-500",children:l})]}),r.jsx("div",{children:r.jsx("input",{type:"url",value:e||"",onChange:e=>t(e.target.value),placeholder:"Or enter image URL directly",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"})})]}),r.jsx(D,{isOpen:n,onClose:()=>o(!1),onSelect:e=>{t(e.url)},allowedTypes:["image"],title:"Select Image"})]})},$=({images:e,onChange:t,productName:s="Product"})=>{let[i,n]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),[u,x]=(0,a.useState)(null),m=s=>{t(e.filter((e,t)=>t!==s).map((e,t)=>({...e,position:t})))},p=(s,r)=>{let a=[...e];a[s]={...a[s],alt:r},t(a)},g=(s,r)=>{if(s===r)return;let a=[...e],[l]=a.splice(s,1);a.splice(r,0,l),t(a.map((e,t)=>({...e,position:t})))},h=(e,t)=>{x(t),e.dataTransfer.effectAllowed="move"},f=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},b=(e,t)=>{e.preventDefault(),null!==u&&(g(u,t),x(null))};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Product Images"}),(0,r.jsxs)("button",{type:"button",onClick:()=>n(!0),className:"bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 text-sm",children:[r.jsx(l.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add Image"})]})]}),0===e.length?(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[r.jsx(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500 mb-4",children:"No images added yet"}),r.jsx("button",{type:"button",onClick:()=>n(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Add First Image"})]}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map((e,t)=>(0,r.jsxs)("div",{draggable:!0,onDragStart:e=>h(e,t),onDragOver:f,onDrop:e=>b(e,t),className:`relative group border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow ${u===t?"opacity-50":""}`,children:[r.jsx("div",{className:"absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-10",children:0===t?"Featured":`#${t+1}`}),r.jsx("div",{className:"absolute top-2 right-2 bg-black bg-opacity-75 text-white p-1 rounded cursor-move opacity-0 group-hover:opacity-100 transition-opacity z-10",children:r.jsx(S,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"aspect-square relative",children:[r.jsx("img",{src:e.url,alt:e.alt,className:"w-full h-full object-cover"}),r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2",children:[r.jsx("button",{type:"button",onClick:()=>window.open(e.url,"_blank"),className:"bg-white text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors",title:"View full size",children:r.jsx(F.Z,{className:"w-4 h-4"})}),r.jsx("button",{type:"button",onClick:()=>m(t),className:"bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-colors",title:"Remove image",children:r.jsx(y.Z,{className:"w-4 h-4"})})]})})]}),r.jsx("div",{className:"p-3",children:r.jsx("input",{type:"text",value:e.alt,onChange:e=>p(t,e.target.value),placeholder:"Image description (alt text)",className:"w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-green-500"})})]},t))}),i&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[r.jsx("h3",{className:"text-lg font-semibold",children:"Select Image"}),r.jsx("button",{onClick:()=>n(!1),className:"text-gray-400 hover:text-gray-600",children:r.jsx(y.Z,{className:"w-6 h-6"})})]}),r.jsx("div",{className:"p-4",children:r.jsx(I,{value:"",onChange:r=>{let a={url:r,alt:`${s} - Image ${e.length+1}`,position:e.length};t([...e,a]),n(!1)},label:"",placeholder:"Select or enter image URL"})})]})}),e.length>0&&(0,r.jsxs)("div",{className:"text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[r.jsx("p",{className:"font-medium mb-1",children:"Tips:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[r.jsx("li",{children:"The first image will be used as the featured image"}),r.jsx("li",{children:"Drag and drop images to reorder them"}),r.jsx("li",{children:"Add descriptive alt text for better SEO and accessibility"}),r.jsx("li",{children:"Recommended image size: 800x800px or larger"})]})]})]})};var R=s(941);let T=({categories:e,selectedCategoryIds:t,onChange:s,label:l="Categories",placeholder:i="Select categories...",required:n=!1})=>{let[o,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(""),x=(0,a.useRef)(null),m=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=e=>{x.current&&!x.current.contains(e.target)&&(c(!1),u(""))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let p=e.filter(e=>e.name.toLowerCase().includes(d.toLowerCase())),g=e.filter(e=>t.includes(e.id)),h=e=>{t.includes(e)?s(t.filter(t=>t!==e)):s([...t,e])},f=e=>{s(t.filter(t=>t!==e))};return(0,r.jsxs)("div",{className:"relative",ref:x,children:[l&&(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[l,n&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),r.jsx("div",{className:"w-full min-h-[42px] px-3 py-2 border border-gray-300 rounded-lg focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500 cursor-pointer bg-white",onClick:()=>c(!o),children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 items-center",children:[g.length>0?g.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800",children:[e.name,r.jsx("button",{type:"button",onClick:t=>{t.stopPropagation(),f(e.id)},className:"ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-green-200 focus:outline-none",children:r.jsx(y.Z,{className:"w-3 h-3"})})]},e.id)):r.jsx("span",{className:"text-gray-500 text-sm",children:i}),r.jsx("div",{className:"flex-1 flex justify-end",children:r.jsx(R.Z,{className:`w-5 h-5 text-gray-400 transition-transform ${o?"transform rotate-180":""}`})})]})}),o&&(0,r.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden",children:[r.jsx("div",{className:"p-2 border-b border-gray-200",children:r.jsx("input",{ref:m,type:"text",placeholder:"Search categories...",value:d,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",autoFocus:!0})}),g.length>0&&r.jsx("div",{className:"p-2 border-b border-gray-200",children:(0,r.jsxs)("button",{type:"button",onClick:()=>{s([])},className:"text-xs text-red-600 hover:text-red-800 font-medium",children:["Clear all (",g.length,")"]})}),r.jsx("div",{className:"max-h-40 overflow-y-auto",children:p.length>0?p.map(e=>{let s=t.includes(e.id);return(0,r.jsxs)("div",{className:`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${s?"bg-green-50":""}`,onClick:()=>h(e.id),children:[r.jsx("span",{className:`text-sm ${s?"text-green-800 font-medium":"text-gray-700"}`,children:e.name}),s&&r.jsx(q.Z,{className:"w-4 h-4 text-green-600"})]},e.id)}):r.jsx("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No categories found"})})]})]})},U=({isOpen:e,onClose:t,onSave:s,categories:l,product:i})=>{let[n,o]=(0,a.useState)({name:i?.name||"",slug:i?.slug||"",description:i?.description||"",shortDescription:i?.shortDescription||"",comparePrice:i?.comparePrice?.toString()||"",categoryId:i?.category?.id||"",isFeatured:i?.isFeatured||!1}),[c,d]=(0,a.useState)(()=>i?.productCategories&&i.productCategories.length>0?i.productCategories.map(e=>e.category.id):i?.category?.id?[i.category.id]:[]),[u,x]=(0,a.useState)(i?.images?.map(e=>({id:e.id||"",url:e.url,alt:e.alt||"",position:e.position||0}))||[]),[m,p]=(0,a.useState)(i?.variants?.map(e=>({id:e.id,name:e.name,value:e.value,price:e.price?.toString()||"0"}))||[]),[g,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(!1);(0,a.useEffect)(()=>{i?.variants?p(i.variants.map(e=>({id:e.id,name:e.name,value:e.value,price:e.price?.toString()||"0"}))):p([])},[i]);let j=(e,t,s)=>{let r=[...m];r[e]={...r[e],[t]:s},p(r)},v=e=>{p(m.filter((t,s)=>s!==e))},N=async e=>{e.preventDefault(),b(!0);try{let e=i?`/api/products/${i.id}`:"/api/products",r=await fetch(e,{method:i?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...n,slug:n.slug||(0,h.GD)(n.name),comparePrice:n.comparePrice?parseFloat(n.comparePrice):null,categoryIds:c,images:u.map((e,t)=>({url:e.url,alt:e.alt||n.name,position:t})),variations:m.filter(e=>e.name&&e.value).map(e=>({name:e.name,value:e.value,price:e.price?parseFloat(e.price):0}))})}),a=await r.json();r.ok?(a.warnings&&a.warnings.length>0&&alert(`Product saved successfully!

Warnings:
${a.warnings.join("\n")}`),s(),t()):alert(`Failed to save product: ${a.error||"Unknown error"}`)}catch(e){alert("Failed to save product")}finally{b(!1)}};return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:i?"Edit Product":"Add Product"}),r.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,r.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name"}),r.jsx("input",{type:"text",value:n.name,onChange:e=>{let t=e.target.value;o({...n,name:t,slug:n.slug||(0,h.GD)(t)})},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["URL Slug",r.jsx("span",{className:"text-xs text-gray-500 ml-1",children:"(SEO-friendly URL)"})]}),r.jsx("input",{type:"text",value:n.slug,onChange:e=>o({...n,slug:(0,h.GD)(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"auto-generated-from-name"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Preview: /products/",n.slug||(0,h.GD)(n.name)]})]}),r.jsx("div",{children:r.jsx(T,{categories:l,selectedCategoryIds:c,onChange:d,label:"Categories",placeholder:"Select categories...",required:!0})}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Compare Price (₹)"}),r.jsx("input",{type:"number",step:"0.01",value:n.comparePrice,onChange:e=>o({...n,comparePrice:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter compare price in Rupees"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Short Description"}),r.jsx("input",{type:"text",value:n.shortDescription,onChange:e=>o({...n,shortDescription:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),r.jsx("textarea",{value:n.description,onChange:e=>o({...n,description:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),r.jsx($,{images:u,onChange:e=>x(e.map(e=>({id:e.id||"",url:e.url,alt:e.alt||"",position:e.position||0}))),productName:n.name||"Product"}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Product Variations"}),r.jsx("button",{type:"button",onClick:()=>y(!g),className:"text-sm text-blue-600 hover:text-blue-700",children:g?"Hide Variations":"Add Variations"})]}),g&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"text-sm text-gray-600 mb-3",children:"Add variations like Size, Color, Material, etc. Each variation can have its own price adjustment and stock."}),m.map((e,t)=>(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Name (e.g., Size)"}),r.jsx("input",{type:"text",value:e.name,onChange:e=>j(t,"name",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"Size"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Value (e.g., Large)"}),r.jsx("input",{type:"text",value:e.value,onChange:e=>j(t,"value",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"Large"})]}),(0,r.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),r.jsx("input",{type:"number",step:"0.01",value:e.price,onChange:e=>j(t,"price",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"0.00"})]}),r.jsx("button",{type:"button",onClick:()=>v(t),className:"px-2 py-1 text-red-600 hover:text-red-700 text-sm",title:"Remove variation",children:"\xd7"})]})]},t)),r.jsx("button",{type:"button",onClick:()=>{p([...m,{name:"",value:"",price:"0"}])},className:"w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors",children:"+ Add Variation"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",id:"featured",checked:n.isFeatured,onChange:e=>o({...n,isFeatured:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),r.jsx("label",{htmlFor:"featured",className:"ml-2 text-sm text-gray-700",children:"Featured Product"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 pt-4",children:[r.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),r.jsx("button",{type:"submit",disabled:f,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50",children:f?"Saving...":i?"Update":"Create"})]})]})]})}):null},O=()=>{let[e,t]=(0,a.useState)(""),[s,m]=(0,a.useState)([]),[y,f]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!1),[v,w]=(0,a.useState)(null),[C,S]=(0,a.useState)([]),[F,P]=(0,a.useState)([]),[E,A]=(0,a.useState)(!0),[Z,M]=(0,a.useState)(null),[q,L]=(0,a.useState)(!1),[D,I]=(0,a.useState)(!1),[$,R]=(0,a.useState)(!1),[T,O]=(0,a.useState)(null),[_,z]=(0,a.useState)(!1),[V,Q]=(0,a.useState)(null);(0,a.useEffect)(()=>{J(),B()},[]);let J=async()=>{try{A(!0);let e=await fetch("/api/products?limit=1000"),t=await e.json();t.success?S(t.data):M("Failed to fetch products")}catch(e){M("Failed to fetch products")}finally{A(!1)}},B=async()=>{try{let e=await fetch("/api/categories"),t=await e.json();t.success&&P(t.data)}catch(e){}},G=C.filter(t=>{if(!e)return!0;let s=t.name.toLowerCase().includes(e.toLowerCase()),r=t.category?.name.toLowerCase().includes(e.toLowerCase()),a=t.productCategories?.some(t=>t.category.name.toLowerCase().includes(e.toLowerCase()));return s||r||a}),H=e=>{m(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},K=e=>{w(e),j(!0)},X=async e=>{if(confirm("Are you sure you want to delete this product?"))try{let t=await fetch(`/api/products/${e}`,{method:"DELETE"}),s=await t.json();t.ok?("soft_delete"===s.type?alert(`Product deactivated: ${s.message}`):alert("Product deleted successfully"),S(t=>t.filter(t=>t.id!==e))):alert(`Failed to delete product: ${s.error||"Unknown error"}`)}catch(e){alert("Failed to delete product")}},W=async e=>{if(0===s.length){alert("Please select products first");return}if(confirm({delete:"Are you sure you want to delete the selected products? This action cannot be undone.",activate:"Are you sure you want to activate the selected products?",deactivate:"Are you sure you want to deactivate the selected products?",feature:"Are you sure you want to feature the selected products?",unfeature:"Are you sure you want to unfeature the selected products?"}[e]))try{I(!0);let t=await fetch("/api/products/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,productIds:s})}),r=await t.json();r.success?(alert(r.message),m([]),J()):alert(`Failed to ${e} products: ${r.error}`)}catch(t){console.error(`Error in bulk ${e}:`,t),alert(`Failed to ${e} products`)}finally{I(!1)}},Y=e=>{O(e),R(!0)},ee=e=>{Q(e),z(!0)},et=e=>({Skincare:"bg-green-100 text-green-800","Hair Care":"bg-purple-100 text-purple-800","Body Care":"bg-blue-100 text-blue-800",cleanser:"bg-blue-100 text-blue-800",serum:"bg-purple-100 text-purple-800",moisturizer:"bg-green-100 text-green-800",mask:"bg-yellow-100 text-yellow-800",exfoliator:"bg-pink-100 text-pink-800","eye-care":"bg-indigo-100 text-indigo-800"})[e]||"bg-gray-100 text-gray-800";return(0,r.jsxs)("div",{children:[r.jsx("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Products"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your skincare product catalog"})]}),r.jsx("div",{className:"mt-4 sm:mt-0",children:(0,r.jsxs)("button",{onClick:()=>f(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[r.jsx(l.Z,{className:"w-5 h-5 mr-2"}),"Add Product"]})})]})}),E&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[r.jsx(i.Z,{className:"w-8 h-8 animate-spin text-green-600"}),r.jsx("span",{className:"ml-2 text-gray-600",children:"Loading products..."})]}),Z&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[r.jsx("p",{className:"text-red-600",children:Z}),r.jsx("button",{onClick:J,className:"mt-2 text-red-600 hover:text-red-700 underline",children:"Try again"})]}),!E&&!Z&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search products...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),r.jsx("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:r.jsx(o.Z,{className:"w-5 h-5 text-gray-600"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx("button",{onClick:()=>{let e=new Blob(['Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Variations\nSample Product,sample-product,"A great product description","Short description",99.99,149.99,Skincare,"Skincare;Face Care",yes,yes,"[{""name"":""Size"",""value"":""50ml"",""price"":99.99}]"\n# Instructions:\n# - Name: Required product name\n# - Slug: SEO-friendly URL (auto-generated if empty)\n# - Price: Required base price in rupees\n# - Category: Single category name\n# - Categories: Multiple categories separated by semicolons\n# - Featured: yes/no\n# - Active: yes/no (defaults to yes)\n# - Variations: JSON array of variations with name, value, and price'],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="products_template.csv",s.click(),window.URL.revokeObjectURL(t)},className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Download CSV template",children:r.jsx(c.Z,{className:"w-5 h-5 text-gray-600"})}),r.jsx("button",{onClick:()=>{let e=new Blob([JSON.stringify({products:[{name:"Sample Product",slug:"sample-product",description:"A detailed product description",shortDescription:"Short description",price:99.99,comparePrice:149.99,categoryNames:["Skincare","Face Care"],isFeatured:!0,isActive:!0,variations:[{name:"Size",value:"50ml",price:99.99},{name:"Size",value:"100ml",price:179.99}]}]},null,2)],{type:"application/json"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="products_template.json",s.click(),window.URL.revokeObjectURL(t)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium text-gray-600",title:"Download JSON template",children:"JSON"})]}),(0,r.jsxs)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept=".csv,.json",e.onchange=async e=>{let t=e.target.files?.[0];if(t)try{L(!0);let e=await t.text(),s=[];if(t.name.toLowerCase().endsWith(".json"))try{let t=JSON.parse(e);if(t.products&&Array.isArray(t.products))s=t.products;else if(Array.isArray(t))s=t;else{alert('Invalid JSON format. Expected format: {"products": [...]} or direct array of products.');return}let r=[];for(let e of s){if(!e.name)continue;let t=[];e.category&&t.push(e.category),e.categoryNames&&Array.isArray(e.categoryNames)&&t.push(...e.categoryNames),0!==t.length&&(e.price&&e.price>0||e.variations&&e.variations.length>0)&&r.push({...e,categoryNames:t,slug:e.slug||(0,h.GD)(e.name),isFeatured:!!e.isFeatured,isActive:!1!==e.isActive})}s=r}catch(e){alert("Invalid JSON file. Please check the file format.");return}else{let t=e.split("\n").filter(e=>e.trim()&&!e.trim().startsWith("#")),r=t[0].split(",").map(e=>e.trim().replace(/"/g,"")),a=["Name"].filter(e=>!r.some(t=>t.toLowerCase().includes(e.toLowerCase())));if(a.length>0){alert(`Missing required columns: ${a.join(", ")}`);return}if(!r.some(e=>e.toLowerCase().includes("category"))){alert('Missing category information. Please include either "Category" or "Categories" column.');return}for(let e=1;e<t.length;e++){let a=t[e].trim();if(!a)continue;let l=a.split(",").map(e=>e.trim().replace(/"/g,"")),i=r.findIndex(e=>e.toLowerCase().includes("name")),n=r.findIndex(e=>e.toLowerCase().includes("slug")),o=r.findIndex(e=>e.toLowerCase().includes("variations")),c=i>=0&&l[i]||"",d=n>=0&&l[n]||"",u=o>=0&&l[o]||"",x=[];if(u)try{x=JSON.parse(u)}catch(e){}let m=[],p=r.findIndex(e=>"categories"===e.toLowerCase()),g=p>=0&&l[p]||"";if(g&&m.push(...g.split(";").map(e=>e.trim()).filter(e=>e)),0===m.length){let e=r.findIndex(e=>"category"===e.toLowerCase()),t=e>=0&&l[e]||"";t&&m.push(t)}let y=r.findIndex(e=>"price"===e.toLowerCase()),f=y>=0&&l[y]||"",b=parseFloat(f)||0,j=b>=0||x&&x.length>0,v=r.findIndex(e=>"description"===e.toLowerCase()),N=r.findIndex(e=>e.toLowerCase().includes("short description")),w=r.findIndex(e=>e.toLowerCase().includes("compare price")),C=r.findIndex(e=>"featured"===e.toLowerCase()),k=r.findIndex(e=>"active"===e.toLowerCase()),S={name:c,slug:(0,h.w)(d,c),description:v>=0&&l[v]||"",shortDescription:N>=0&&l[N]||"",price:b,comparePrice:w>=0&&parseFloat(l[w]||"0")||null,categoryNames:m,isFeatured:C>=0&&l[C]?.toLowerCase()==="yes",isActive:!(k>=0)||l[k]?.toLowerCase()!=="no",variations:x},F=m.length>0;S.name&&F&&j&&s.push(S)}}if(0===s.length){alert('No valid products found in the file. Please ensure:\n\n1. Each product has a Name\n2. Each product has at least one Category\n3. Price must be provided OR variations must have prices\n\nFor JSON: Use format {"products": [...]} or direct array\nFor CSV: Check the CSV template for the correct format.');return}let r=await fetch("/api/products/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({products:s})}),a=await r.json();if(a.success){let{success:e,failed:t,errors:s}=a.data,r=`Import completed!
✅ ${e} products imported successfully`;t>0&&(r+=`
❌ ${t} products failed`,s.length>0&&(r+=`

Errors:
${s.slice(0,5).join("\n")}`,s.length>5&&(r+=`
... and ${s.length-5} more errors`))),alert(r),e>0&&J()}else alert(`Import failed: ${a.error}`)}catch(e){alert("Failed to import products. Please check the file format.")}finally{L(!1)}},e.click()},disabled:q,className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",title:"Import products from CSV/JSON",children:[q?r.jsx(i.Z,{className:"w-4 h-4 mr-2 animate-spin"}):r.jsx(d.Z,{className:"w-4 h-4 mr-2"}),q?"Importing...":"Import"]})]})]})}),s.length>0&&r.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:[s.length," product",s.length>1?"s":""," selected"]}),r.jsx("button",{onClick:()=>m([]),className:"text-blue-600 hover:text-blue-700 text-sm",children:"Clear selection"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>W("activate"),disabled:D,className:"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50",children:"Activate"}),r.jsx("button",{onClick:()=>W("deactivate"),disabled:D,className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 disabled:opacity-50",children:"Deactivate"}),r.jsx("button",{onClick:()=>W("feature"),disabled:D,className:"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50",children:"Feature"}),r.jsx("button",{onClick:()=>W("unfeature"),disabled:D,className:"px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 disabled:opacity-50",children:"Unfeature"}),(0,r.jsxs)("button",{onClick:()=>W("delete"),disabled:D,className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 flex items-center",children:[D?r.jsx(i.Z,{className:"w-4 h-4 mr-1 animate-spin"}):r.jsx(u.Z,{className:"w-4 h-4 mr-1"}),"Delete"]})]})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[r.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left",children:r.jsx("input",{type:"checkbox",checked:s.length===G.length&&G.length>0,onChange:()=>{m(s.length===G.length?[]:G.map(e=>e.id))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:G.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4",children:r.jsx("input",{type:"checkbox",checked:s.includes(e.id),onChange:()=>H(e.id),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),r.jsx("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("img",{src:e.images[0]?.url||"/images/default-product.jpg",alt:e.images[0]?.alt||e.name,className:"w-12 h-12 rounded-lg object-cover mr-4"}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.shortDescription})]})]})}),r.jsx("td",{className:"px-6 py-4",children:r.jsx("div",{className:"flex flex-wrap gap-1",children:e.productCategories&&e.productCategories.length>0?e.productCategories.map(e=>r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${et(e.category.name)}`,children:e.category.name},e.category.id)):e.category&&r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${et(e.category.name)}`,children:e.category.name})})}),(0,r.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-900",children:[e.variants&&e.variants.length>0?(()=>{let t=e.variants.map(e=>e.price).filter(e=>"number"==typeof e&&e>0);if(0===t.length)return"No pricing";let s=Math.min(...t),r=Math.max(...t);return s===r?(0,h.T4)(s):`${(0,h.T4)(s)} - ${(0,h.T4)(r)}`})():r.jsx("span",{className:"text-gray-500",children:"No variations"}),e.comparePrice&&r.jsx("span",{className:"text-xs text-gray-500 line-through ml-2",children:(0,h.T4)(e.comparePrice)})]}),r.jsx("td",{className:"px-6 py-4",children:r.jsx("div",{className:"text-sm text-gray-900",children:e.variants&&e.variants.length>0?(0,r.jsxs)("span",{className:"text-blue-600",children:[e.variants.length," variations"]}):r.jsx("span",{className:"text-gray-500",children:"No variations"})})}),r.jsx("td",{className:"px-6 py-4",children:r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${e.isFeatured?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.isFeatured?"Featured":"Active"})}),r.jsx("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>K(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Edit Product",children:r.jsx(x.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>Y(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Manage FAQs",children:r.jsx(p,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>ee(e),className:"p-1 text-gray-400 hover:text-purple-600 transition-colors",title:"Manage Variations",children:r.jsx(g.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>X(e.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete Product",children:r.jsx(u.Z,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),(0,r.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",G.length," of ",C.length," products"]})]}),y&&r.jsx(U,{isOpen:y,onClose:()=>f(!1),onSave:J,categories:F}),b&&v&&r.jsx(U,{isOpen:b,onClose:()=>{j(!1),w(null)},onSave:J,categories:F,product:v}),$&&T&&r.jsx(N,{isOpen:$,onClose:()=>{R(!1),O(null)},product:T}),_&&V&&r.jsx(k,{isOpen:_,onClose:()=>{z(!1),Q(null)},product:V})]})}},9891:(e,t,s)=>{"use strict";function r(e){return function(e,t=!0){if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return`₹${s}`}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,t){return(e?a(e):a(t))||"product"}s.d(t,{GD:()=>a,T4:()=>r,w:()=>l})},47483:(e,t,s)=>{"use strict";s.d(t,{sS:()=>a});var r=s(21841);function a(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}s(86947),new r.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0}),process.env.R2_BUCKET_NAME,process.env.R2_PUBLIC_URL||process.env.R2_ACCOUNT_ID},941:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},36283:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},41137:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},69508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},65917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\products\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,8571,3599,6453,6879],()=>s(2695));module.exports=r})();