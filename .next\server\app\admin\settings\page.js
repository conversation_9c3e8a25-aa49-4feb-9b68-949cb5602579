(()=>{var e={};e.id=6140,e.ids=[6140],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93945:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>c}),s(82740),s(90596),s(36944),s(35866);var r=s(23191),a=s(88716),l=s(37922),n=s.n(l),o=s(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let c=["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82740)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\settings\\page.tsx"],u="/admin/settings/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59758:(e,t,s)=>{Promise.resolve().then(s.bind(s,9356))},9356:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(10326),a=s(17577);let l=(0,s(76557).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]);var n=s(5932),o=s(54014),i=s(58038),c=s(6507),d=s(28916),u=s(31215);let m=()=>{let[e,t]=(0,a.useState)("general"),[s,m]=(0,a.useState)("idle"),[g,x]=(0,a.useState)({storeName:"Herbalicious",storeDescription:"Natural skincare products for radiant, healthy skin",storeEmail:"<EMAIL>",storePhone:"+91 99878 10707",storeAddress:"123 Wellness Street, Natural City, NC 12345",currency:"USD",timezone:"America/New_York",smtpHost:"smtp.gmail.com",smtpPort:"587",smtpUsername:"<EMAIL>",smtpPassword:"********",seoTitle:"Herbalicious - Natural Skincare Products",seoDescription:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin.",seoKeywords:"natural skincare, botanical, organic, herbal products",googleAnalyticsId:"UA-123456789-1",twoFactorAuth:!1,sessionTimeout:30,passwordPolicy:{minLength:8,requireUppercase:!0,requireNumbers:!0,requireSpecialChars:!0}}),h=[{id:"general",label:"General",icon:l},{id:"email",label:"Email",icon:n.Z},{id:"seo",label:"SEO",icon:o.Z},{id:"security",label:"Security",icon:i.Z},{id:"notifications",label:"Notifications",icon:c.Z},{id:"payments",label:"Payments",icon:d.Z}];return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your store configuration and preferences"})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[r.jsx("div",{className:"lg:w-64",children:r.jsx("nav",{className:"space-y-1",children:h.map(s=>{let a=s.icon;return(0,r.jsxs)("button",{onClick:()=>t(s.id),className:`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${e===s.id?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[r.jsx(a,{className:`w-5 h-5 mr-3 ${e===s.id?"text-green-600":"text-gray-400"}`}),s.label]},s.id)})})}),r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(()=>{switch(e){case"general":return r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Store Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Name"}),r.jsx("input",{type:"text",value:g.storeName,onChange:e=>x({...g,storeName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Email"}),r.jsx("input",{type:"email",value:g.storeEmail,onChange:e=>x({...g,storeEmail:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),r.jsx("input",{type:"tel",value:g.storePhone,onChange:e=>x({...g,storePhone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Currency"}),(0,r.jsxs)("select",{value:g.currency,onChange:e=>x({...g,currency:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[r.jsx("option",{value:"USD",children:"USD - US Dollar"}),r.jsx("option",{value:"EUR",children:"EUR - Euro"}),r.jsx("option",{value:"GBP",children:"GBP - British Pound"}),r.jsx("option",{value:"CAD",children:"CAD - Canadian Dollar"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Description"}),r.jsx("textarea",{value:g.storeDescription,onChange:e=>x({...g,storeDescription:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{className:"mt-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Address"}),r.jsx("textarea",{value:g.storeAddress,onChange:e=>x({...g,storeAddress:e.target.value}),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})});case"email":return r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"SMTP Configuration"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SMTP Host"}),r.jsx("input",{type:"text",value:g.smtpHost,onChange:e=>x({...g,smtpHost:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SMTP Port"}),r.jsx("input",{type:"text",value:g.smtpPort,onChange:e=>x({...g,smtpPort:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),r.jsx("input",{type:"text",value:g.smtpUsername,onChange:e=>x({...g,smtpUsername:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),r.jsx("input",{type:"password",value:g.smtpPassword,onChange:e=>x({...g,smtpPassword:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})]})});case"seo":return r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"SEO Settings"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SEO Title"}),r.jsx("input",{type:"text",value:g.seoTitle,onChange:e=>x({...g,seoTitle:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SEO Description"}),r.jsx("textarea",{value:g.seoDescription,onChange:e=>x({...g,seoDescription:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Keywords"}),r.jsx("input",{type:"text",value:g.seoKeywords,onChange:e=>x({...g,seoKeywords:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Separate keywords with commas"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Google Analytics ID"}),r.jsx("input",{type:"text",value:g.googleAnalyticsId,onChange:e=>x({...g,googleAnalyticsId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"UA-XXXXXXXXX-X"})]})]})]})});case"security":return r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Security Settings"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:g.twoFactorAuth,onChange:e=>x({...g,twoFactorAuth:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),r.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Enable Two-Factor Authentication for Admin"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Session Timeout (minutes)"}),r.jsx("input",{type:"number",value:g.sessionTimeout,onChange:e=>x({...g,sessionTimeout:parseInt(e.target.value)}),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})]})});default:return r.jsx("div",{className:"text-center py-12",children:(0,r.jsxs)("p",{className:"text-gray-500",children:["Settings for ",e," coming soon..."]})})}})(),r.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("button",{onClick:()=>{m("saving"),setTimeout(()=>{m("saved"),setTimeout(()=>m("idle"),2e3)},1e3)},disabled:"saving"===s,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center disabled:opacity-50",children:[r.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"saving"===s?"Saving...":"saved"===s?"Saved!":"Save Changes"]})})]})})]})]})}},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},28916:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},54014:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},31215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,s)=>{"use strict";var r=s(77389);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},82740:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\settings\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,8571,3599,6879],()=>s(93945));module.exports=r})();