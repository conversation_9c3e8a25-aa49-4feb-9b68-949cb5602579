"use strict";(()=>{var e={};e.id=78,e.ids=[78],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},74066:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>O,patchFetch:()=>P,requestAsyncStorage:()=>E,routeModule:()=>N,serverHooks:()=>g,staticGenerationAsyncStorage:()=>I});var i={};r.r(i),r.d(i,{POST:()=>f});var o=r(49303),a=r(88716),n=r(60670),s=r(87070),d=r(75571),c=r(95306),u=r(89585),p=r(54211),l=r(65630),y=r(29489);let m=l.Ry({userIds:l.IX(l.Z_()).min(1,"At least one user must be selected"),title:l.Z_().min(1,"Title is required"),content:l.Z_().min(1,"Content is required"),type:l.Km(["ADMIN_MESSAGE","BROADCAST","PROMOTIONAL","SYSTEM"]).default("ADMIN_MESSAGE"),priority:l.Km(["LOW","NORMAL","HIGH","URGENT"]).default("NORMAL"),sendEmail:l.O7().default(!0),sendInApp:l.O7().default(!0)});async function f(e){try{let t=await (0,d.getServerSession)(c.L);if(!t?.user?.id||"ADMIN"!==t.user.role)return s.NextResponse.json({error:"Admin access required"},{status:401});let r=await e.json(),i=m.parse(r);p.kg.info("Admin sending notifications",{adminId:t.user.id,userCount:i.userIds.length,type:i.type,priority:i.priority});let o=[],a=0,n=0;for(let e of i.userIds)try{await u.$T.adminMessage(e,{title:i.title,content:i.content,type:i.type,priority:i.priority,sendEmail:i.sendEmail,sendInApp:i.sendInApp}),o.push({userId:e,success:!0}),a++}catch(r){p.kg.error("Failed to send notification to user",r,{userId:e,adminId:t.user.id}),o.push({userId:e,success:!1,error:r instanceof Error?r.message:"Unknown error"}),n++}return p.kg.info("Notification sending completed",{adminId:t.user.id,totalUsers:i.userIds.length,successCount:a,errorCount:n}),s.NextResponse.json({success:!0,message:`Notifications sent successfully to ${a} user(s)`,stats:{total:i.userIds.length,success:a,errors:n},results:o})}catch(e){if(e instanceof y.j)return s.NextResponse.json({error:"Validation error",details:e.issues},{status:400});return p.kg.error("Failed to send admin notifications",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let N=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/notifications/send/route",pathname:"/api/admin/notifications/send",filename:"route",bundlePath:"app/api/admin/notifications/send/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\send\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:E,staticGenerationAsyncStorage:I,serverHooks:g}=N,O="/api/admin/notifications/send/route";function P(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:I})}},89585:(e,t,r)=>{r.d(t,{$T:()=>d,aZ:()=>a,kg:()=>s,un:()=>n});var i=r(68602),o=r(53524);let a={orderPlaced:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${t.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:t.orderId,orderNumber:t.orderNumber,amount:t.total,currency:t.currency,itemCount:t.itemCount},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderConfirmed:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${t.orderNumber} has been confirmed and is being prepared for shipment.${t.estimatedDelivery?` Estimated delivery: ${t.estimatedDelivery}`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,estimatedDelivery:t.estimatedDelivery},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderProcessing:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${t.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:t.orderId,orderNumber:t.orderNumber},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderShipped:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${t.orderNumber} has been shipped.${t.estimatedDelivery?` Estimated delivery: ${t.estimatedDelivery}`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,estimatedDelivery:t.estimatedDelivery},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderDelivered:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${t.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:t.orderId,orderNumber:t.orderNumber,deliveredAt:t.deliveredAt},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderCancelled:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${t.orderNumber} has been cancelled.${t.reason?` Reason: ${t.reason}`:""}${t.refundAmount?` A refund of ${t.currency} ${t.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,reason:t.reason,refundAmount:t.refundAmount,currency:t.currency},priority:o.NotificationPriority.HIGH,sendEmail:!0})},n={itemAdded:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${t.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:t.productId,productName:t.productName,price:t.price,currency:t.currency},priority:o.NotificationPriority.LOW,sendEmail:!1}),itemRemoved:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${t.productName} has been removed from your wishlist.`,data:{productId:t.productId,productName:t.productName},priority:o.NotificationPriority.LOW,sendEmail:!1}),priceDropAlert:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${t.productName} is now ${t.discountPercentage}% off! Price dropped from ${t.currency} ${t.oldPrice} to ${t.currency} ${t.newPrice}.`,data:{productId:t.productId,productName:t.productName,oldPrice:t.oldPrice,newPrice:t.newPrice,currency:t.currency,discountPercentage:t.discountPercentage},priority:o.NotificationPriority.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},s={async reviewRequest(e,t){let r=t.productNames.join(", ");return await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${r}. Your review helps other customers make informed decisions!`,data:{orderId:t.orderId,orderNumber:t.orderNumber,productIds:t.productIds,productNames:t.productNames},priority:o.NotificationPriority.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${t.rating}-star review of ${t.productName}! Your feedback is valuable to us and other customers.`,data:{productId:t.productId,productName:t.productName,rating:t.rating},priority:o.NotificationPriority.LOW,sendEmail:!1})},d={adminMessage:async(e,t)=>await i.B.createNotification({userId:e,type:t.type||o.NotificationType.ADMIN_MESSAGE,title:t.title,message:t.content,data:{sentByAdmin:!0,sendEmail:!1!==t.sendEmail,sendInApp:!1!==t.sendInApp},priority:t.priority||o.NotificationPriority.NORMAL,sendEmail:!1!==t.sendEmail}),systemAlert:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:t.title,message:t.message,data:{severity:t.severity},priority:"critical"===t.severity?o.NotificationPriority.URGENT:"high"===t.severity?o.NotificationPriority.HIGH:o.NotificationPriority.NORMAL,sendEmail:"critical"===t.severity||"high"===t.severity}),maintenanceNotice:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${t.startTime} to ${t.endTime}. ${t.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:t.startTime,maintenanceEnd:t.endTime},priority:o.NotificationPriority.HIGH,sendEmail:!0}),sendMessage:async(e,t)=>await i.B.createNotification({userId:e,type:o.NotificationType.ADMIN_MESSAGE,title:t.title,message:t.message,priority:t.priority||o.NotificationPriority.NORMAL,sendEmail:t.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:o.NotificationType.BROADCAST,title:e.title,message:e.message,priority:e.priority||o.NotificationPriority.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:o.NotificationType.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:o.NotificationPriority.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,6575,9489,5245,5630,2125],()=>r(74066));module.exports=i})();