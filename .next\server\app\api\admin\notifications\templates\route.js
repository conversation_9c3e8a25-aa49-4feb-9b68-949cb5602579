"use strict";(()=>{var e={};e.id=3615,e.ids=[3615],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},66137:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{GET:()=>d});var s=r(49303),n=r(88716),a=r(60670),o=r(87070),l=r(75571),u=r(95306),c=r(3474),p=r(54211);async function d(e){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return o.NextResponse.json({error:"Admin access required"},{status:401});let t=await c._.notificationTemplate.findMany({select:{id:!0,name:!0,subject:!0,content:!0,type:!0,isActive:!0,createdAt:!0},orderBy:{name:"asc"}});return p.kg.info("Admin fetched notification templates",{adminId:e.user.id,templateCount:t.length}),o.NextResponse.json({success:!0,templates:t})}catch(e){return p.kg.error("Failed to fetch notification templates",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/notifications/templates/route",pathname:"/api/admin/notifications/templates",filename:"route",bundlePath:"app/api/admin/notifications/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\templates\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:y}=m,g="/api/admin/notifications/templates/route";function v(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},95306:(e,t,r)=>{r.d(t,{L:()=>l});var i=r(13539),s=r(77234),n=r(53797),a=r(98691),o=r(3474);let l={adapter:(0,i.N)(o._),providers:[(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await o._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await a.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await o._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>s});var i=r(53524);let s=globalThis.prisma??new i.PrismaClient({log:["error"]})},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class s{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:s,context:n,error:a,userId:o,requestId:l}=e,u=i[r],c=`[${t}] ${u}: ${s}`;return o&&(c+=` | User: ${o}`),l&&(c+=` | Request: ${l}`),n&&Object.keys(n).length>0&&(c+=` | Context: ${JSON.stringify(n)}`),a&&(c+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(c+=`
Stack: ${a.stack}`)),c}log(e,t,r,i){if(!this.shouldLog(e))return;let s={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},n=this.formatMessage(s);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(s))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,s){this.info(`API ${e} ${t} - ${r}`,{...s,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,s){this.error(`API ${e} ${t} failed`,r,{...s,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new s},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=s?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(i,n,o):i[n]=e[n]}return i.default=e,r&&r.set(e,i),i}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,6575],()=>r(66137));module.exports=i})();