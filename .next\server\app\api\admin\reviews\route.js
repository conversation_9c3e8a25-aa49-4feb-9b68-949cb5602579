"use strict";(()=>{var e={};e.id=5314,e.ids=[5314],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},39091:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>h,requestAsyncStorage:()=>w,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>p,PATCH:()=>m});var i=t(49303),a=t(88716),n=t(60670),o=t(87070),u=t(75571),l=t(95306),c=t(3474);let d=["PENDING","APPROVED","REJECTED"];async function p(e){try{let r;let t=await (0,u.getServerSession)(l.L);if(!t?.user)return o.NextResponse.json({error:"Authentication required"},{status:401});if(t.user.id?r=await c._.user.findUnique({where:{id:t.user.id},select:{id:!0,role:!0,email:!0}}):t.user.email&&(r=await c._.user.findUnique({where:{email:t.user.email},select:{id:!0,role:!0,email:!0}})),!r||"ADMIN"!==r.role)return o.NextResponse.json({error:"Unauthorized - Admin access required"},{status:401});let{searchParams:s}=new URL(e.url),i=s.get("status")||"PENDING",a=parseInt(s.get("page")||"1"),n=parseInt(s.get("limit")||"20"),p=d.includes(i)?i:"PENDING",m=(a-1)*n,f=await c._.review.findMany({where:{status:p},include:{user:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,slug:!0}}},orderBy:{createdAt:"desc"},skip:m,take:n}),w=await c._.review.count({where:{status:p}});return o.NextResponse.json({success:!0,data:f,pagination:{page:a,limit:n,total:w,pages:Math.ceil(w/n)}})}catch(e){return console.error("Error fetching reviews:",e),o.NextResponse.json({success:!1,error:"Failed to fetch reviews"},{status:500})}}async function m(e){try{let r;let t=await (0,u.getServerSession)(l.L);if(!t?.user)return o.NextResponse.json({error:"Authentication required"},{status:401});if(t.user.id?r=await c._.user.findUnique({where:{id:t.user.id},select:{id:!0,role:!0,email:!0}}):t.user.email&&(r=await c._.user.findUnique({where:{email:t.user.email},select:{id:!0,role:!0,email:!0}})),!r||"ADMIN"!==r.role)return o.NextResponse.json({error:"Unauthorized - Admin access required"},{status:401});let{reviewId:s,status:i}=await e.json();if(!s||!i||!["APPROVED","REJECTED"].includes(i))return o.NextResponse.json({error:"Invalid request. Status must be APPROVED or REJECTED"},{status:400});let a=await c._.review.update({where:{id:s},data:{status:i}});return o.NextResponse.json({success:!0,data:a,message:`Review ${i.toLowerCase()} successfully`})}catch(e){return console.error("Error updating review:",e),o.NextResponse.json({success:!1,error:"Failed to update review"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/reviews/route",pathname:"/api/admin/reviews",filename:"route",bundlePath:"app/api/admin/reviews/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\reviews\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:v,serverHooks:g}=f,y="/api/admin/reviews/route";function h(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:v})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),i=t(77234),a=t(53797),n=t(98691),o=t(3474);let u={adapter:(0,s.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(39091));module.exports=s})();