"use strict";(()=>{var e={};e.id=2628,e.ids=[2628],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},44829:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>v,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>d});var i=t(49303),n=t(88716),a=t(60670),o=t(87070),l=t(75571),u=t(95306),c=t(3474),p=t(54211);async function d(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user?.id||"ADMIN"!==r.user.role)return o.NextResponse.json({error:"Admin access required"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"50"),n=t.get("search"),a=t.get("role"),d=(s-1)*i,m={};n&&(m.OR=[{name:{contains:n,mode:"insensitive"}},{email:{contains:n,mode:"insensitive"}}]),a&&(m.role=a);let[f,h]=await Promise.all([c._.user.findMany({where:m,select:{id:!0,name:!0,email:!0,role:!0,createdAt:!0,preferences:{select:{emailNotifications:!0,inAppNotifications:!0}},_count:{select:{orders:!0,notifications:!0}}},orderBy:{createdAt:"desc"},skip:d,take:i}),c._.user.count({where:m})]),g=Math.ceil(h/i);return p.kg.info("Admin fetched users list",{adminId:r.user.id,page:s,limit:i,totalCount:h,search:n,role:a}),o.NextResponse.json({success:!0,data:{users:f,pagination:{page:s,limit:i,totalCount:h,totalPages:g,hasNext:s<g,hasPrev:s>1}}})}catch(e){return p.kg.error("Failed to fetch users for admin",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:g}=m,y="/api/admin/users/route";function v(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},95306:(e,r,t)=>{t.d(r,{L:()=>l});var s=t(13539),i=t(77234),n=t(53797),a=t(98691),o=t(3474);let l={adapter:(0,s.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await a.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})},54211:(e,r,t)=>{var s;t.d(r,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:r,level:t,message:i,context:n,error:a,userId:o,requestId:l}=e,u=s[t],c=`[${r}] ${u}: ${i}`;return o&&(c+=` | User: ${o}`),l&&(c+=` | Request: ${l}`),n&&Object.keys(n).length>0&&(c+=` | Context: ${JSON.stringify(n)}`),a&&(c+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(c+=`
Stack: ${a.stack}`)),c}log(e,r,t,s){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:r,context:t,error:s},n=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(i))}error(e,r,t){this.log(0,e,t,r)}warn(e,r){this.log(1,e,r)}info(e,r){this.log(2,e,r)}debug(e,r){this.log(3,e,r)}apiRequest(e,r,t,s){this.info(`API ${e} ${r}`,{...s,userId:t,type:"api_request"})}apiResponse(e,r,t,s,i){this.info(`API ${e} ${r} - ${t}`,{...i,statusCode:t,duration:s,type:"api_response"})}apiError(e,r,t,s,i){this.error(`API ${e} ${r} failed`,t,{...i,userId:s,type:"api_error"})}authSuccess(e,r,t){this.info("Authentication successful",{...t,userId:e,method:r,type:"auth_success"})}authFailure(e,r,t,s){this.warn("Authentication failed",{...s,email:e,method:r,reason:t,type:"auth_failure"})}dbQuery(e,r,t,s){this.debug(`DB ${e} on ${r}`,{...s,operation:e,table:r,duration:t,type:"db_query"})}dbError(e,r,t,s){this.error(`DB ${e} on ${r} failed`,t,{...s,operation:e,table:r,type:"db_error"})}securityEvent(e,r,t){this.log("high"===r?0:"medium"===r?1:2,`Security event: ${e}`,{...t,severity:r,type:"security_event"})}rateLimitHit(e,r,t,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:r,window:t,type:"rate_limit"})}emailSent(e,r,t,s){this.info("Email sent",{...s,to:e,subject:r,template:t,type:"email_sent"})}emailError(e,r,t,s){this.error("Email failed to send",t,{...s,to:e,subject:r,type:"email_error"})}performance(e,r,t){this.log(r>5e3?1:3,`Performance: ${e} took ${r}ms`,{...t,operation:e,duration:r,type:"performance"})}}let n=new i},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=i?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(s,n,o):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(45609));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(44829));module.exports=s})();