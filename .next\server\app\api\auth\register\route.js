"use strict";(()=>{var e={};e.id=3002,e.ids=[3002],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},75849:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>R,requestAsyncStorage:()=>x,routeModule:()=>y,serverHooks:()=>w,staticGenerationAsyncStorage:()=>b});var s={};r.r(s),r.d(s,{POST:()=>f});var o=r(49303),i=r(88716),a=r(60670),n=r(87070),l=r(98691),d=r(65630),p=r(3474),c=r(8149),u=r(95921),h=r(84875),m=r(54211);let g=d.Ry({name:d.Z_().min(2,"Name must be at least 2 characters"),email:d.Z_().email("Invalid email address"),password:d.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number")}),f=(0,h.lm)(async e=>{let t=Date.now();m.kg.apiRequest("POST","/api/auth/register"),await (0,c.er)(e,c.Ri,5);let r=await e.json(),{name:s,email:o,password:i}=g.parse(r);if(m.kg.info("Registration attempt",{email:o,name:s}),await p._.user.findUnique({where:{email:o}}))throw m.kg.authFailure(o,"registration","User already exists"),new h.AY("User with this email already exists");let a=await l.vp(i,12),d=await p._.user.create({data:{name:s,email:o,password:a,role:"CUSTOMER"},select:{id:!0,name:!0,email:!0,role:!0,createdAt:!0}});m.kg.authSuccess(d.id,"registration",{email:o,name:s});try{await (0,u.Pi)(o,s),m.kg.emailSent(o,"Welcome Email","welcome")}catch(e){m.kg.emailError(o,"Welcome Email",e)}let f=Date.now()-t;return m.kg.performance("user_registration",f),n.NextResponse.json({success:!0,message:"User created successfully",user:d},{status:201})}),y=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:b,serverHooks:w}=y,v="/api/auth/register/route";function R(){return(0,a.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:b})}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var s=r(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})},95921:(e,t,r)=>{r.d(t,{Cz:()=>l,LS:()=>d,Pi:()=>p,bn:()=>c,jC:()=>u});var s=r(55245),o=r(54211);async function i(e){let t=process.env.BREVO_API_KEY;if(!t)throw Error("BREVO_API_KEY is not configured");let r={sender:{name:process.env.FROM_NAME||"Herbalicious",email:process.env.FROM_EMAIL||"<EMAIL>"},to:[{email:e.to}],subject:e.subject,htmlContent:e.html},s=await fetch("https://api.brevo.com/v3/smtp/email",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","api-key":t},body:JSON.stringify(r)});if(!s.ok){let t=await s.text();throw o.kg.emailError(e.to,e.subject,Error(`Brevo API error: ${s.status} - ${t}`)),Error(`Failed to send email via Brevo API: ${s.status}`)}o.kg.emailSent(e.to,e.subject,"brevo-api")}let a=()=>{if(process.env.SMTP_HOST)return s.createTransport({host:process.env.SMTP_HOST,port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}});throw Error("No email configuration found")};async function n(e){let t=a(),r={from:e.from||process.env.FROM_EMAIL||"<EMAIL>",to:e.to,subject:e.subject,html:e.html};await t.sendMail(r),o.kg.emailSent(e.to,e.subject,"smtp")}async function l(e){try{if(process.env.BREVO_API_KEY){await i(e);return}await n(e)}catch(t){throw o.kg.emailError(e.to,e.subject,t),t}}async function d(e,t){let r=`${process.env.NEXTAUTH_URL}/reset-password?token=${t}`,s=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Password Reset Request</h2>
      <p>You have requested to reset your password for your Herbalicious account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${r}" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${r}</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Password Reset Request - Herbalicious",html:s})}async function p(e,t){let r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Welcome to Herbalicious, ${t}!</h2>
      <p>Thank you for joining our community of natural health enthusiasts.</p>
      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL}/shop" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Shopping
        </a>
      </div>
      <p>If you have any questions, feel free to contact our support team.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Welcome to Herbalicious!",html:r})}async function c(e,t){let r=t.items.map(e=>`
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${e.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${e.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${e.price.toFixed(2)}</td>
    </tr>
  `).join(""),s=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Confirmation</h2>
      <p>Thank you for your order! Here are the details:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0;">Order #${t.orderId}</h3>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${r}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px; font-weight: bold; border-top: 2px solid #ddd;">Total:</td>
            <td style="padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;">₹${t.total.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div style="margin: 20px 0;">
        <h4>Shipping Address:</h4>
        <p style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">${t.shippingAddress}</p>
      </div>
      
      <p>We'll send you another email when your order ships.</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:`Order Confirmation - ${t.orderId}`,html:s})}async function u(e){let t=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your Brevo integration is working properly!</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        Sent at: ${new Date().toISOString()}
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious for testing purposes.
      </p>
    </div>
  `;await l({to:e,subject:"Email Configuration Test - Herbalicious",html:t})}},84875:(e,t,r)=>{r.d(t,{AY:()=>p,M_:()=>l,_7:()=>n,dR:()=>d,gz:()=>i,lm:()=>u,p8:()=>a});var s=r(87070),o=r(29489);class i extends Error{constructor(e,t=500,r="INTERNAL_ERROR",s){super(e),this.statusCode=t,this.code=r,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,i)}}class a extends i{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class n extends i{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class l extends i{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class d extends i{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class p extends i{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class c extends i{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function u(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){if(e instanceof i)return s.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof o.j){let t=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,details:t.details}},{status:t.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let t=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new p(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new n("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new d;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new c("Database operation failed",{code:e.code,message:e.message})}}(e);return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,...t.details&&{details:t.details}}},{status:t.statusCode})}return e instanceof Error&&e.message,s.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>i}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:i,error:a,userId:n,requestId:l}=e,d=s[r],p=`[${t}] ${d}: ${o}`;return n&&(p+=` | User: ${n}`),l&&(p+=` | Request: ${l}`),i&&Object.keys(i).length>0&&(p+=` | Context: ${JSON.stringify(i)}`),a&&(p+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(p+=`
Stack: ${a.stack}`)),p}log(e,t,r,s){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},i=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(i);break;case 1:console.warn(i);break;case 2:console.info(i);break;case 3:console.debug(i)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let i=new o},8149:(e,t,r)=>{r.d(t,{Ri:()=>i,Xw:()=>a,er:()=>l,jO:()=>n});var s=r(919);function o(e){let t=new s.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((s,o)=>{let i=t.get(r)||[0];0===i[0]&&t.set(r,i),i[0]+=1,i[0]>=e?o(Error("Rate limit exceeded")):s()})}}let i=o({interval:9e5,uniqueTokenPerInterval:500}),a=o({interval:6e4,uniqueTokenPerInterval:500}),n=o({interval:36e5,uniqueTokenPerInterval:500});async function l(e,t,r){let s=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,s)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,9489,5245,5630,138],()=>r(75849));module.exports=s})();