"use strict";(()=>{var e={};e.id=9436,e.ids=[9436],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},6092:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>k,requestAsyncStorage:()=>x,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{POST:()=>m});var a=t(49303),n=t(88716),o=t(60670),i=t(87070),u=t(98691),l=t(65630),p=t(29489),d=t(3474),c=t(8149);let w=l.Ry({token:l.Z_().min(1,"Reset token is required"),password:l.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number")});async function m(e){try{await (0,c.er)(e,c.Ri,10);let r=await e.json(),{token:t,password:s}=w.parse(r),a=await d._.user.findFirst({where:{resetToken:t,resetTokenExpiry:{gt:new Date}}});if(!a)return i.NextResponse.json({error:"Invalid or expired reset token"},{status:400});let n=await u.vp(s,12);return await d._.user.update({where:{id:a.id},data:{password:n,resetToken:null,resetTokenExpiry:null}}),i.NextResponse.json({message:"Password reset successfully"},{status:200})}catch(e){if(e instanceof p.j)return i.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});if(e instanceof Error&&e.message.includes("Rate limit"))return i.NextResponse.json({error:"Too many password reset attempts. Please try again later."},{status:429});return console.error("Reset password error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:v,serverHooks:f}=h,g="/api/auth/reset-password/route";function k(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:v})}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})},8149:(e,r,t)=>{t.d(r,{Ri:()=>n,Xw:()=>o,er:()=>u,jO:()=>i});var s=t(919);function a(e){let r=new s.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((s,a)=>{let n=r.get(t)||[0];0===n[0]&&r.set(t,n),n[0]+=1,n[0]>=e?a(Error("Rate limit exceeded")):s()})}}let n=a({interval:9e5,uniqueTokenPerInterval:500}),o=a({interval:6e4,uniqueTokenPerInterval:500}),i=a({interval:36e5,uniqueTokenPerInterval:500});async function u(e,r,t){let s=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,s)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,9489,5630,138],()=>t(6092));module.exports=s})();