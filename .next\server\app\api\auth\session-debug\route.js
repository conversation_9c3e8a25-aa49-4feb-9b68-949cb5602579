"use strict";(()=>{var e={};e.id=4148,e.ids=[4148],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},4818:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>b});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(49303),a=t(88716),n=t(60670),o=t(87070),u=t(75571),l=t(95306),d=t(3474),c=t(20330);async function p(e){try{let r=await (0,u.getServerSession)(l.L),t=await (0,c.getToken)({req:e,secret:process.env.NEXTAUTH_SECRET});if(!r?.user)return o.NextResponse.json({authenticated:!1,message:"No session found",token:t?{sub:t.sub,email:t.email,role:t.role}:null});let s=null,i=null;if(r.user.email&&(s=await d._.user.findUnique({where:{email:r.user.email},select:{id:!0,email:!0,name:!0,role:!0,createdAt:!0}})),r.user.id)try{i=await d._.user.findUnique({where:{id:r.user.id},select:{id:!0,email:!0,name:!0,role:!0,createdAt:!0}})}catch(e){}return o.NextResponse.json({authenticated:!0,session:{user:{id:r.user.id,email:r.user.email,name:r.user.name,role:r.user.role}},token:t?{sub:t.sub,email:t.email,role:t.role}:null,databaseLookups:{byEmail:s,byId:i},validation:{hasSessionId:!!r.user.id,hasSessionEmail:!!r.user.email,userExistsByEmail:!!s,userExistsById:!!i,idsMatch:s?.id===r.user.id,emailsMatch:s?.email===r.user.email}})}catch(e){return console.error("Session debug error:",e),o.NextResponse.json({error:"Failed to check session",details:e},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/session-debug/route",pathname:"/api/auth/session-debug",filename:"route",bundlePath:"app/api/auth/session-debug/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\session-debug\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:b,serverHooks:y}=m,h="/api/auth/session-debug/route";function g(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:b})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),i=t(77234),a=t(53797),n=t(98691),o=t(3474);let u={adapter:(0,s.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(4818));module.exports=s})();