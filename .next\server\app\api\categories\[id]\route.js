"use strict";(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},42349:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>m,requestAsyncStorage:()=>g,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>u,PATCH:()=>d});var o=r(49303),a=r(88716),n=r(60670),c=r(87070),i=r(3474);async function u(e,{params:t}){try{let e=await i._.category.findUnique({where:{id:t.id},include:{products:{include:{images:{take:1,orderBy:{position:"asc"}}}},parent:!0,children:!0,_count:{select:{products:!0}}}});if(!e)return c.NextResponse.json({success:!1,error:"Category not found"},{status:404});return c.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching category:",e),c.NextResponse.json({success:!1,error:"Failed to fetch category"},{status:500})}}async function d(e,{params:t}){try{let{name:r,slug:s,description:o,image:a,isActive:n,parentId:u}=await e.json(),d=await i._.category.update({where:{id:t.id},data:{...r&&{name:r},...s&&{slug:s},...o&&{description:o},...a&&{image:a},...void 0!==n&&{isActive:n},...u&&{parentId:u}},include:{parent:!0,_count:{select:{products:!0}}}});return c.NextResponse.json({success:!0,data:d,message:"Category updated successfully"})}catch(e){return console.error("Error updating category:",e),c.NextResponse.json({success:!1,error:"Failed to update category"},{status:500})}}async function p(e,{params:t}){try{if(await i._.product.count({where:{categoryId:t.id}})>0)return c.NextResponse.json({success:!1,error:"Cannot delete category with products"},{status:400});return await i._.category.delete({where:{id:t.id}}),c.NextResponse.json({success:!0,message:"Category deleted successfully"})}catch(e){return console.error("Error deleting category:",e),c.NextResponse.json({success:!1,error:"Failed to delete category"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:h}=l,x="/api/categories/[id]/route";function m(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var s=r(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972],()=>r(42349));module.exports=s})();