"use strict";(()=>{var e={};e.id=9961,e.ids=[9961],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},95875:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>p});var a=t(49303),o=t(88716),n=t(60670),c=t(87070),i=t(3474);async function u(){try{let e=await i._.category.findMany({where:{isActive:!0},include:{_count:{select:{products:!0}}},orderBy:{name:"asc"}});return c.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching categories:",e),c.NextResponse.json({success:!1,error:"Failed to fetch categories"},{status:500})}}async function p(e){try{let{name:r,slug:t,description:s,parentId:a}=await e.json(),o=await i._.category.create({data:{name:r,slug:t,description:s,parentId:a||null},include:{parent:!0,_count:{select:{products:!0}}}});return c.NextResponse.json({success:!0,data:o,message:"Category created successfully"})}catch(e){return console.error("Error creating category:",e),c.NextResponse.json({success:!1,error:"Failed to create category"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:m}=d,h="/api/categories/route";function x(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(95875));module.exports=s})();