"use strict";(()=>{var e={};e.id=5502,e.ids=[5502],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},26891:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>h,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>w,staticGenerationAsyncStorage:()=>x});var o={};t.r(o),t.d(o,{DELETE:()=>m,GET:()=>c,PUT:()=>p});var s=t(49303),n=t(88716),i=t(60670),a=t(87070),u=t(75571),l=t(95306),d=t(3474);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await d._.coupon.findUnique({where:{id:r.id},include:{usages:{include:{user:{select:{id:!0,name:!0,email:!0}},order:{select:{id:!0,orderNumber:!0,total:!0}}}}}});if(!t)return a.NextResponse.json({error:"Coupon not found"},{status:404});return a.NextResponse.json(t)}catch(e){return console.error("Error fetching coupon:",e),a.NextResponse.json({error:"Failed to fetch coupon"},{status:500})}}async function p(e,{params:r}){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await e.json();if(!o.name||!o.type||!o.discountType||void 0===o.discountValue)return a.NextResponse.json({error:"Missing required fields"},{status:400});let s=await d._.coupon.findUnique({where:{id:r.id}});if(!s)return a.NextResponse.json({error:"Coupon not found"},{status:404});if(o.code&&o.code!==s.code&&await d._.coupon.findUnique({where:{code:o.code.toUpperCase()}}))return a.NextResponse.json({error:"Coupon code already exists"},{status:400});let n=await d._.coupon.update({where:{id:r.id},data:{code:o.code?o.code.toUpperCase():s.code,name:o.name,description:o.description,type:o.type,discountType:o.discountType,discountValue:o.discountValue,minimumAmount:o.minimumAmount,maximumDiscount:o.maximumDiscount,usageLimit:o.usageLimit,userUsageLimit:o.userUsageLimit,isActive:o.isActive??s.isActive,isStackable:o.isStackable??s.isStackable,showInModule:o.showInModule??s.showInModule,validFrom:o.validFrom?new Date(o.validFrom):s.validFrom,validUntil:o.validUntil?new Date(o.validUntil):null,applicableProducts:o.applicableProducts||s.applicableProducts,applicableCategories:o.applicableCategories||s.applicableCategories,excludedProducts:o.excludedProducts||s.excludedProducts,excludedCategories:o.excludedCategories||s.excludedCategories,customerSegments:o.customerSegments||s.customerSegments}});return a.NextResponse.json(n)}catch(e){return console.error("Error updating coupon:",e),a.NextResponse.json({error:"Failed to update coupon"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await d._.coupon.findUnique({where:{id:r.id},include:{usages:!0}});if(!t)return a.NextResponse.json({error:"Coupon not found"},{status:404});if(t.usages.length>0)return a.NextResponse.json({error:"Cannot delete coupon that has been used. Consider deactivating it instead."},{status:400});return await d._.coupon.delete({where:{id:r.id}}),a.NextResponse.json({message:"Coupon deleted successfully"})}catch(e){return console.error("Error deleting coupon:",e),a.NextResponse.json({error:"Failed to delete coupon"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/coupons/[id]/route",pathname:"/api/coupons/[id]",filename:"route",bundlePath:"app/api/coupons/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:w}=f,y="/api/coupons/[id]/route";function h(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:x})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var o=t(13539),s=t(77234),n=t(53797),i=t(98691),a=t(3474);let u={adapter:(0,o.N)(a._),providers:[(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await i.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await a._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:o}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>s});var o=t(53524);let s=globalThis.prisma??new o.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=s?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(o,n,a):o[n]=e[n]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,5972,8691,6575],()=>t(26891));module.exports=o})();