"use strict";(()=>{var e={};e.id=2475,e.ids=[2475],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},68726:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>d});var o=r(49303),i=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),c=r(3474);async function p(e){try{let t=await (0,u.getServerSession)(l.L),{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),o=parseInt(r.get("limit")||"10"),i=r.get("active"),n=r.get("showInModule"),p=r.get("type"),d=r.get("userId"),m=(s-1)*o,f={};"true"===i?f.isActive=!0:"false"===i&&(f.isActive=!1),"true"===n?f.showInModule=!0:"false"===n&&(f.showInModule=!1),p&&(f.type=p),t?.user&&"ADMIN"===t.user.role||(f.isActive=!0,f.validFrom={lte:new Date},f.OR=[{validUntil:null},{validUntil:{gte:new Date}}]);let[g,y]=await Promise.all([c._.coupon.findMany({where:f,skip:m,take:o,orderBy:{createdAt:"desc"},include:{usages:!!d&&{where:{userId:d},select:{id:!0,usedAt:!0}}}}),c._.coupon.count({where:f})]);return a.NextResponse.json({coupons:g,pagination:{page:s,limit:o,total:y,pages:Math.ceil(y/o)}})}catch(e){return console.error("Error fetching coupons:",e),a.NextResponse.json({error:"Failed to fetch coupons"},{status:500})}}async function d(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json();if(!r.code||!r.name||!r.type||!r.discountType||void 0===r.discountValue)return a.NextResponse.json({error:"Missing required fields"},{status:400});if(await c._.coupon.findUnique({where:{code:r.code.toUpperCase()}}))return a.NextResponse.json({error:"Coupon code already exists"},{status:400});let s=await c._.coupon.create({data:{code:r.code.toUpperCase(),name:r.name,description:r.description,type:r.type,discountType:r.discountType,discountValue:r.discountValue,minimumAmount:r.minimumAmount,maximumDiscount:r.maximumDiscount,usageLimit:r.usageLimit,userUsageLimit:r.userUsageLimit,isActive:r.isActive??!0,isStackable:r.isStackable??!1,showInModule:r.showInModule??!1,validFrom:r.validFrom?new Date(r.validFrom):new Date,validUntil:r.validUntil?new Date(r.validUntil):null,applicableProducts:r.applicableProducts||[],applicableCategories:r.applicableCategories||[],excludedProducts:r.excludedProducts||[],excludedCategories:r.excludedCategories||[],customerSegments:r.customerSegments||[]}});return a.NextResponse.json(s,{status:201})}catch(e){return console.error("Error creating coupon:",e),a.NextResponse.json({error:"Failed to create coupon"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/coupons/route",pathname:"/api/coupons",filename:"route",bundlePath:"app/api/coupons/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:y}=m,w="/api/coupons/route";function v(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},95306:(e,t,r)=>{r.d(t,{L:()=>u});var s=r(13539),o=r(77234),i=r(53797),n=r(98691),a=r(3474);let u={adapter:(0,s.N)(a._),providers:[(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await a._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await n.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await a._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var s=r(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(s,i,a):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,6575],()=>r(68726));module.exports=s})();