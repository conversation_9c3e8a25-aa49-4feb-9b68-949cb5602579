"use strict";(()=>{var e={};e.id=976,e.ids=[976],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},77249:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>b,staticGenerationAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{POST:()=>p});var a=r(49303),o=r(88716),s=r(60670),n=r(87070),u=r(75571),l=r(95306),c=r(3474);async function p(e){try{await (0,u.getServerSession)(l.L);let{couponCode:t,cartItems:r,userId:i,subtotal:a}=await e.json();if(!t||!r||void 0===a)return n.NextResponse.json({error:"Missing required fields"},{status:400});let o=await d(t,r,a,i);return n.NextResponse.json(o)}catch(e){return console.error("Error validating coupon:",e),n.NextResponse.json({error:"Failed to validate coupon"},{status:500})}}async function d(e,t,r,i){let a=await c._.coupon.findUnique({where:{code:e.toUpperCase()},include:{usages:!!i&&{where:{userId:i}}}});if(!a)return{isValid:!1,discountAmount:0,errorMessage:"Coupon code not found"};if(!a.isActive)return{isValid:!1,discountAmount:0,errorMessage:"This coupon is no longer active"};let o=new Date;if(a.validFrom>o)return{isValid:!1,discountAmount:0,errorMessage:"This coupon is not yet valid"};if(a.validUntil&&a.validUntil<o)return{isValid:!1,discountAmount:0,errorMessage:"This coupon has expired"};if(a.usageLimit&&a.usageCount>=a.usageLimit)return{isValid:!1,discountAmount:0,errorMessage:"This coupon has reached its usage limit"};if(i&&a.userUsageLimit&&(Array.isArray(a.usages)?a.usages.length:0)>=a.userUsageLimit)return{isValid:!1,discountAmount:0,errorMessage:"You have already used this coupon the maximum number of times"};if(a.minimumAmount&&r<a.minimumAmount)return{isValid:!1,discountAmount:0,errorMessage:`Minimum order amount of ₹${a.minimumAmount} required`};let s=function(e,t){let r=t.map(e=>e.product.id),i=t.flatMap(e=>e.product.categories?.map(e=>e.id)||[]);if(e.excludedProducts.length>0&&r.some(t=>e.excludedProducts.includes(t)))return{isApplicable:!1,message:"This coupon cannot be applied to some items in your cart",applicableAmount:0};if(e.excludedCategories.length>0&&i.some(t=>e.excludedCategories.includes(t)))return{isApplicable:!1,message:"This coupon cannot be applied to some categories in your cart",applicableAmount:0};let a=0;switch(e.type){case"STORE_WIDE":a=t.reduce((e,t)=>e+t.product.price*t.quantity,0);break;case"PRODUCT_SPECIFIC":if(0===e.applicableProducts.length)return{isApplicable:!1,message:"No applicable products specified for this coupon",applicableAmount:0};let o=t.filter(t=>e.applicableProducts.includes(t.product.id));if(0===o.length)return{isApplicable:!1,message:"This coupon is not applicable to any items in your cart",applicableAmount:0};a=o.reduce((e,t)=>e+t.product.price*t.quantity,0);break;case"CATEGORY_SPECIFIC":if(0===e.applicableCategories.length)return{isApplicable:!1,message:"No applicable categories specified for this coupon",applicableAmount:0};let s=t.filter(t=>t.product.categories?.some(t=>e.applicableCategories.includes(t.id)));if(0===s.length)return{isApplicable:!1,message:"This coupon is not applicable to any categories in your cart",applicableAmount:0};a=s.reduce((e,t)=>e+t.product.price*t.quantity,0);break;default:a=t.reduce((e,t)=>e+t.product.price*t.quantity,0)}return{isApplicable:!0,message:"",applicableAmount:a}}(a,t);return s.isApplicable?{isValid:!0,discountAmount:function(e,t,r,i){let a=0;switch(e.discountType){case"PERCENTAGE":a=i*e.discountValue/100;break;case"FIXED_AMOUNT":a=Math.min(e.discountValue,i);break;default:a=0}return e.maximumDiscount&&a>e.maximumDiscount&&(a=e.maximumDiscount),Math.round(100*a)/100}(a,0,0,s.applicableAmount),coupon:{...a,description:a.description||void 0,minimumAmount:a.minimumAmount||void 0,maximumDiscount:a.maximumDiscount||void 0,usageLimit:a.usageLimit||void 0,userUsageLimit:a.userUsageLimit||void 0,validUntil:a.validUntil?.toISOString()||void 0,validFrom:a.validFrom.toISOString(),createdAt:a.createdAt.toISOString(),updatedAt:a.updatedAt.toISOString()}}:{isValid:!1,discountAmount:0,errorMessage:s.message}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/coupons/validate/route",pathname:"/api/coupons/validate",filename:"route",bundlePath:"app/api/coupons/validate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\validate\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:b}=m,h="/api/coupons/validate/route";function v(){return(0,s.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:f})}},95306:(e,t,r)=>{r.d(t,{L:()=>u});var i=r(13539),a=r(77234),o=r(53797),s=r(98691),n=r(3474);let u={adapter:(0,i.N)(n._),providers:[(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await n._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await s.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await n._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await n._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>a});var i=r(53524);let a=globalThis.prisma??new i.PrismaClient({log:["error"]})},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var a=r(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var n=a?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(i,o,n):i[o]=e[o]}return i.default=e,r&&r.set(e,i),i}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,6575],()=>r(77249));module.exports=i})();