{"success": true, "data": {"overview": {"totalProducts": 28, "totalCategories": 13, "totalUsers": 2, "totalCustomers": 1, "totalOrders": 3, "activeProducts": 28, "featuredProducts": 1}, "recent": {"products": [{"id": "cmdiy1rs9008multsa0w0ozpr", "name": "Vitamin-C <PERSON>", "slug": "vitamin-c-serum", "description": "", "shortDescription": "Brightens skin tone and fades dark spots.", "comparePrice": null, "costPrice": null, "weight": null, "dimensions": null, "isActive": true, "isFeatured": false, "metaTitle": null, "metaDescription": null, "createdAt": "2025-07-25T14:56:01.256Z", "updatedAt": "2025-07-25T23:52:40.453Z", "categoryId": "cmdixk63h0004ulxk0wlb7x3q", "price": 800, "category": {"id": "cmdixk63h0004ulxk0wlb7x3q", "name": "Gels & Serums", "slug": "gels-serums", "description": "Concentrated gels and serums for targeted skincare", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.998Z", "updatedAt": "2025-07-25T14:42:19.998Z"}, "images": []}, {"id": "cmdiy1qks008gultspyw90mcn", "name": "Under-<PERSON> Gel", "slug": "under-eye-gel", "description": "", "shortDescription": "Reduces dark circles and puffiness.", "comparePrice": null, "costPrice": null, "weight": null, "dimensions": null, "isActive": true, "isFeatured": false, "metaTitle": null, "metaDescription": null, "createdAt": "2025-07-25T14:55:59.692Z", "updatedAt": "2025-07-25T23:52:40.312Z", "categoryId": "cmdixk63h0004ulxk0wlb7x3q", "price": 450, "category": {"id": "cmdixk63h0004ulxk0wlb7x3q", "name": "Gels & Serums", "slug": "gels-serums", "description": "Concentrated gels and serums for targeted skincare", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.998Z", "updatedAt": "2025-07-25T14:42:19.998Z"}, "images": []}, {"id": "cmdiy1pdt008aultsx11ms2f0", "name": "<PERSON><PERSON>", "slug": "supermane-hair-oil", "description": "", "shortDescription": "Strengthens hair and controls hair fall naturally.", "comparePrice": null, "costPrice": null, "weight": null, "dimensions": null, "isActive": true, "isFeatured": false, "metaTitle": null, "metaDescription": null, "createdAt": "2025-07-25T14:55:58.145Z", "updatedAt": "2025-07-25T23:52:40.169Z", "categoryId": "cmdixk6jo0008ulxkfa87a9za", "price": 300, "category": {"id": "cmdixk6jo0008ulxkfa87a9za", "name": "Hair Oils", "slug": "hair-oils", "description": "Natural hair oils for healthy hair and scalp", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.580Z", "updatedAt": "2025-07-25T14:42:20.580Z"}, "images": []}, {"id": "cmdiy1o5h0085ultsvgr5eyzo", "name": "Strawberry Silk Booster", "slug": "strawberry-silk-booster", "description": "", "shortDescription": "Boosts skin softness and fruity radiance.", "comparePrice": null, "costPrice": null, "weight": null, "dimensions": null, "isActive": true, "isFeatured": false, "metaTitle": null, "metaDescription": null, "createdAt": "2025-07-25T14:55:56.549Z", "updatedAt": "2025-07-25T23:52:40.028Z", "categoryId": "cmdixk63h0004ulxk0wlb7x3q", "price": 700, "category": {"id": "cmdixk63h0004ulxk0wlb7x3q", "name": "Gels & Serums", "slug": "gels-serums", "description": "Concentrated gels and serums for targeted skincare", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.998Z", "updatedAt": "2025-07-25T14:42:19.998Z"}, "images": []}, {"id": "cmdiy1myb0080ults2e70i0qu", "name": "Snow Face Cleanser", "slug": "snow-face-cleanser", "description": "", "shortDescription": "Refreshes and deeply cleanses skin gently.", "comparePrice": null, "costPrice": null, "weight": null, "dimensions": null, "isActive": true, "isFeatured": false, "metaTitle": null, "metaDescription": null, "createdAt": "2025-07-25T14:55:54.995Z", "updatedAt": "2025-07-25T23:52:39.882Z", "categoryId": "cmdixk5is0000ulxkfy2s07bo", "price": 550, "category": {"id": "cmdixk5is0000ulxkfy2s07bo", "name": "Cleansers & Face Wash", "slug": "cleansers-face-wash", "description": "Gentle cleansers and face washes for daily skincare routine", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.252Z", "updatedAt": "2025-07-25T14:42:19.252Z"}, "images": []}], "users": [{"id": "cmdhti8he000gulhw8krshp9c", "email": "<EMAIL>", "name": "<PERSON>", "phone": "+1234567890", "avatar": null, "password": "$2b$12$IyBS4bxGEBGP2lSCQJWl0uoR.Tx/yRhSuQmI.1f6UDlvAhK3wZDoy", "role": "CUSTOMER", "emailVerified": null, "resetToken": null, "resetTokenExpiry": null, "createdAt": "2025-07-24T20:01:05.138Z", "updatedAt": "2025-07-24T22:57:10.712Z"}, {"id": "cmdhti89l000fulhwdl0057fe", "email": "<EMAIL>", "name": "Admin User", "phone": "9819922201", "avatar": null, "password": "$2b$12$3gtCzb4AVUryvFay4L4DTO45GKiFpPILy03X1JSLlXirBuLYPjwK6", "role": "ADMIN", "emailVerified": null, "resetToken": null, "resetTokenExpiry": null, "createdAt": "2025-07-24T20:01:04.857Z", "updatedAt": "2025-07-25T16:24:13.485Z"}]}, "growth": {"productsGrowth": "+12.5%", "categoriesGrowth": "****%", "usersGrowth": "****%", "ordersGrowth": "+15.3%"}}}