"use strict";(()=>{var e={};e.id=6041,e.ids=[6041],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30305:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>l,staticGenerationAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(49303),o=t(88716),i=t(60670),d=t(87070),n=t(3474);async function u(){try{let[e,r,t,s,a,o,i]=await Promise.all([n._.product.count(),n._.category.count(),n._.user.count(),n._.user.count({where:{role:"CUSTOMER"}}),n._.order.count(),n._.product.count({where:{isActive:!0}}),n._.product.count({where:{isFeatured:!0}})]),u=await n._.product.findMany({take:5,orderBy:{createdAt:"desc"},include:{category:!0,images:{take:1,orderBy:{position:"asc"}}}}),c=await n._.user.findMany({take:5,orderBy:{createdAt:"desc"}});return d.NextResponse.json({success:!0,data:{overview:{totalProducts:e,totalCategories:r,totalUsers:t,totalCustomers:s,totalOrders:a,activeProducts:o,featuredProducts:i},recent:{products:u,users:c},growth:{productsGrowth:"+12.5%",categoriesGrowth:"+5.2%",usersGrowth:"+8.1%",ordersGrowth:"+15.3%"}}})}catch(e){return console.error("Error fetching dashboard stats:",e),d.NextResponse.json({success:!1,error:"Failed to fetch dashboard statistics"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:l}=c,g="/api/dashboard/stats/route";function m(){return(0,i.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:h})}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(30305));module.exports=s})();