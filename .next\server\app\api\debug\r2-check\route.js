"use strict";(()=>{var e={};e.id=7148,e.ids=[7148],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},84492:e=>{e.exports=require("node:stream")},64878:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>v,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(49303),n=t(88716),i=t(60670),a=t(87070),c=t(8379);async function p(e){try{let e=await (0,c.NA)("uploads",10);return a.NextResponse.json({success:!0,files:e.slice(0,5),totalFiles:e.length})}catch(e){return console.error("R2 debug error:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/debug/r2-check/route",pathname:"/api/debug/r2-check",filename:"route",bundlePath:"app/api/debug/r2-check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\debug\\r2-check\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:m}=u,f="/api/debug/r2-check/route";function v(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}},8379:(e,r,t)=>{t.d(r,{D0:()=>m,NA:()=>d,Z7:()=>u,fo:()=>p,rP:()=>n});var s=t(21841);t(38376);let o=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function n(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:i,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let i=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function c(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(i)?`${a}/${e}`:`${a}/${i}/${e}`}async function p(e,r="uploads"){try{let t=Date.now(),n=e.name.replace(/[^a-zA-Z0-9.-]/g,"_"),a=`${r}/${t}_${n}`,p=await e.arrayBuffer(),u=new s.PutObjectCommand({Bucket:i,Key:a,Body:new Uint8Array(p),ContentType:e.type,ContentLength:e.size});await o.send(u);let d={key:a,name:e.name,size:e.size,type:l(e.name,e.type),url:c(a),lastModified:new Date,folder:r};return{success:!0,file:d}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function u(e){try{let r=new s.DeleteObjectCommand({Bucket:i,Key:e});return await o.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function d(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:i,Prefix:e?`${e}/`:void 0,MaxKeys:r}),n=await o.send(t);if(!n.Contents)return[];return n.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:l(t),url:c(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function l(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function m(e){return e.size>10485760?{valid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type)?{valid:!0}:{valid:!1,error:"File type not supported"}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8376],()=>t(64878));module.exports=s})();