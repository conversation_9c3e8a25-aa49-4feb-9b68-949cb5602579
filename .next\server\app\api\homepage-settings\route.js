"use strict";(()=>{var e={};e.id=3446,e.ids=[3446],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},44757:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>w,requestAsyncStorage:()=>v,routeModule:()=>h,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>p});var o=r(49303),i=r(88716),a=r(60670),n=r(87070),l=r(3474),u=r(75571),d=r(95306);async function c(){try{let e=await l._.homepageSetting.findMany({orderBy:{createdAt:"desc"}}),t=null;e.length>0&&e[0].productOfTheMonthId&&(t=await l._.product.findUnique({where:{id:e[0].productOfTheMonthId,isActive:!0},include:{images:!0,category:!0,_count:{select:{reviews:!0}}}})),t||(t=await l._.product.findFirst({where:{isFeatured:!0,isActive:!0},include:{images:!0,category:!0,_count:{select:{reviews:!0}}}}));let r=[];r=e.length>0&&e[0].bestsellerIds&&e[0].bestsellerIds.length>0?(await Promise.all(e[0].bestsellerIds.map(async e=>await l._.product.findUnique({where:{id:e,isActive:!0},include:{images:!0,category:!0,_count:{select:{reviews:!0}}}})))).filter(Boolean):await l._.product.findMany({where:{isActive:!0},include:{images:!0,category:!0,_count:{select:{reviews:!0}}},orderBy:{reviews:{_count:"desc"}},take:4});let s=await l._.category.findMany({where:{isActive:!0},include:{_count:{select:{products:{where:{isActive:!0}}}}},take:6});return n.NextResponse.json({success:!0,data:{settings:e.length>0?e[0]:null,featuredProduct:t,bestsellers:r,categories:s}})}catch(e){return console.error("Error fetching homepage settings:",e),n.NextResponse.json({success:!1,error:"Failed to fetch homepage settings"},{status:500})}}async function p(e){try{let t=await (0,u.getServerSession)(d.L);if(!t||"ADMIN"!==t.user.role)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{heroTitle:r,heroSubtitle:s,heroCtaText:o,heroCtaLink:i,heroBackgroundColor:a,showHero:c,productOfTheMonthId:p,showProductOfMonth:h,bannerText:v,bannerCtaText:f,bannerCtaLink:g,bannerBackgroundColor:m,showBanner:w,showCategories:y,productSectionBgColor:b,bestsellerIds:x,showBestsellers:_,newsletterTitle:j,newsletterSubtitle:O,showNewsletter:q,showTrustBadges:S,flashSaleTitle:P,flashSaleSubtitle:k,flashSaleEndDate:E,flashSaleBackgroundColor:T,showFlashSale:C,testimonialsTitle:A,testimonialsSubtitle:M,testimonialsBackgroundColor:I,showTestimonials:N,isActive:R}=await e.json(),U=await l._.homepageSetting.upsert({where:{id:"homepage-settings"},update:{...void 0!==r&&{heroTitle:r},...void 0!==s&&{heroSubtitle:s},...void 0!==o&&{heroCtaText:o},...void 0!==i&&{heroCtaLink:i},...void 0!==a&&{heroBackgroundColor:a},...void 0!==c&&{showHero:c},...void 0!==p&&{productOfTheMonthId:p},...void 0!==h&&{showProductOfMonth:h},...void 0!==v&&{bannerText:v},...void 0!==f&&{bannerCtaText:f},...void 0!==g&&{bannerCtaLink:g},...void 0!==m&&{bannerBackgroundColor:m},...void 0!==w&&{showBanner:w},...void 0!==y&&{showCategories:y},...void 0!==b&&{productSectionBgColor:b},...void 0!==x&&{bestsellerIds:x},...void 0!==_&&{showBestsellers:_},...void 0!==j&&{newsletterTitle:j},...void 0!==O&&{newsletterSubtitle:O},...void 0!==q&&{showNewsletter:q},...void 0!==S&&{showTrustBadges:S},...void 0!==P&&{flashSaleTitle:P},...void 0!==k&&{flashSaleSubtitle:k},...void 0!==E&&{flashSaleEndDate:E?new Date(E):null},...void 0!==T&&{flashSaleBackgroundColor:T},...void 0!==C&&{showFlashSale:C},...void 0!==A&&{testimonialsTitle:A},...void 0!==M&&{testimonialsSubtitle:M},...void 0!==I&&{testimonialsBackgroundColor:I},...void 0!==N&&{showTestimonials:N},...void 0!==R&&{isActive:R},updatedAt:new Date},create:{id:"homepage-settings",heroTitle:r||"Natural Skincare Essentials",heroSubtitle:s||"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:o||"Shop Collection",heroCtaLink:i||"/shop",heroBackgroundColor:a||"#f0fdf4",showHero:void 0===c||c,productOfTheMonthId:p,showProductOfMonth:void 0===h||h,bannerText:v,bannerCtaText:f,bannerCtaLink:g,bannerBackgroundColor:m||"#22c55e",showBanner:void 0===w||w,showCategories:void 0===y||y,productSectionBgColor:b||"#f0fdf4",bestsellerIds:x||[],showBestsellers:void 0===_||_,newsletterTitle:j||"Stay Updated",newsletterSubtitle:O||"Get the latest updates on new products and exclusive offers",showNewsletter:void 0===q||q,showTrustBadges:void 0===S||S,flashSaleTitle:P||"Weekend Flash Sale",flashSaleSubtitle:k||"Get 25% off all natural skincare products",flashSaleEndDate:E?new Date(E):null,flashSaleBackgroundColor:T||"#16a34a",showFlashSale:void 0===C||C,testimonialsTitle:A||"What Our Customers Say",testimonialsSubtitle:M||"Real reviews from real customers who love our natural skincare",testimonialsBackgroundColor:I||"#f0fdf4",showTestimonials:void 0===N||N,isActive:void 0===R||R}});return n.NextResponse.json({success:!0,data:U,message:"Homepage settings updated successfully"})}catch(e){return console.error("Error updating homepage settings:",e),n.NextResponse.json({success:!1,error:"Failed to update homepage settings"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/homepage-settings/route",pathname:"/api/homepage-settings",filename:"route",bundlePath:"app/api/homepage-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\homepage-settings\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:f,serverHooks:g}=h,m="/api/homepage-settings/route";function w(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:f})}},95306:(e,t,r)=>{r.d(t,{L:()=>l});var s=r(13539),o=r(77234),i=r(53797),a=r(98691),n=r(3474);let l={adapter:(0,s.N)(n._),providers:[(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await n._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await a.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await n._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await n._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var s=r(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var n=o?Object.getOwnPropertyDescriptor(e,i):null;n&&(n.get||n.set)?Object.defineProperty(s,i,n):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,6575],()=>r(44757));module.exports=s})();