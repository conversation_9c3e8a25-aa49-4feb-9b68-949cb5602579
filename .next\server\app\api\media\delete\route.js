"use strict";(()=>{var e={};e.id=7092,e.ids=[7092],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},84492:e=>{e.exports=require("node:stream")},18591:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>v,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{DELETE:()=>c});var o=t(49303),i=t(88716),n=t(60670),a=t(87070),p=t(8379);async function c(e){try{let{key:r}=await e.json();if(!r)return console.error("No file key provided in delete request"),a.NextResponse.json({success:!1,error:"No file key provided"},{status:400});if(await (0,p.Z7)(r))return a.NextResponse.json({success:!0});return console.error("Failed to delete file from R2:",r),a.NextResponse.json({success:!1,error:"Failed to delete file"},{status:500})}catch(e){return console.error("Delete error:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Delete failed"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/media/delete/route",pathname:"/api/media/delete",filename:"route",bundlePath:"app/api/media/delete/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\delete\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:m}=u,f="/api/media/delete/route";function v(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}},8379:(e,r,t)=>{t.d(r,{D0:()=>m,NA:()=>d,Z7:()=>u,fo:()=>c,rP:()=>i});var s=t(21841);t(38376);let o=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function i(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:n,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let n=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function p(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(n)?`${a}/${e}`:`${a}/${n}/${e}`}async function c(e,r="uploads"){try{let t=Date.now(),i=e.name.replace(/[^a-zA-Z0-9.-]/g,"_"),a=`${r}/${t}_${i}`,c=await e.arrayBuffer(),u=new s.PutObjectCommand({Bucket:n,Key:a,Body:new Uint8Array(c),ContentType:e.type,ContentLength:e.size});await o.send(u);let d={key:a,name:e.name,size:e.size,type:l(e.name,e.type),url:p(a),lastModified:new Date,folder:r};return{success:!0,file:d}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function u(e){try{let r=new s.DeleteObjectCommand({Bucket:n,Key:e});return await o.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function d(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:n,Prefix:e?`${e}/`:void 0,MaxKeys:r}),i=await o.send(t);if(!i.Contents)return[];return i.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:l(t),url:p(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function l(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function m(e){return e.size>10485760?{valid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type)?{valid:!0}:{valid:!1,error:"File type not supported"}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8376],()=>t(18591));module.exports=s})();