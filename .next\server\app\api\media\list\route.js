"use strict";(()=>{var e={};e.id=844,e.ids=[844],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},84492:e=>{e.exports=require("node:stream")},84246:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(49303),n=t(88716),o=t(60670),a=t(87070),l=t(75571),u=t(95306),c=t(8379);async function p(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("folder")||void 0,i=parseInt(t.get("maxKeys")||"100"),n=await (0,c.NA)(s,i);return a.NextResponse.json({success:!0,files:n})}catch(e){return console.error("Error listing R2 files:",e),a.NextResponse.json({success:!1,error:"Failed to list files from R2",files:[]},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/media/list/route",pathname:"/api/media/list",filename:"route",bundlePath:"app/api/media/list/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\list\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:v}=d,y="/api/media/list/route";function g(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>l});var s=t(13539),i=t(77234),n=t(53797),o=t(98691),a=t(3474);let l={adapter:(0,s.N)(a._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await o.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await a._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})},8379:(e,r,t)=>{t.d(r,{D0:()=>f,NA:()=>p,Z7:()=>c,fo:()=>u,rP:()=>n});var s=t(21841);t(38376);let i=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function n(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:o,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let o=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function l(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(o)?`${a}/${e}`:`${a}/${o}/${e}`}async function u(e,r="uploads"){try{let t=Date.now(),n=e.name.replace(/[^a-zA-Z0-9.-]/g,"_"),a=`${r}/${t}_${n}`,u=await e.arrayBuffer(),c=new s.PutObjectCommand({Bucket:o,Key:a,Body:new Uint8Array(u),ContentType:e.type,ContentLength:e.size});await i.send(c);let p={key:a,name:e.name,size:e.size,type:d(e.name,e.type),url:l(a),lastModified:new Date,folder:r};return{success:!0,file:p}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function c(e){try{let r=new s.DeleteObjectCommand({Bucket:o,Key:e});return await i.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function p(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:o,Prefix:e?`${e}/`:void 0,MaxKeys:r}),n=await i.send(t);if(!n.Contents)return[];return n.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:d(t),url:l(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function d(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function f(e){return e.size>10485760?{valid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type)?{valid:!0}:{valid:!1,error:"File type not supported"}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=i?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575,8376],()=>t(84246));module.exports=s})();