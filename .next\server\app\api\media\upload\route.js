"use strict";(()=>{var e={};e.id=5511,e.ids=[5511],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},84492:e=>{e.exports=require("node:stream")},89062:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>y,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(49303),n=t(88716),i=t(60670),a=t(87070),l=t(75571),u=t(95306),p=t(8379);async function c(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.formData(),s=t.get("file"),o=t.get("folder")||"uploads";if(!s)return a.NextResponse.json({error:"No file provided"},{status:400});let n=(0,p.D0)(s);if(!n.valid)return a.NextResponse.json({error:n.error},{status:400});let i=await (0,p.fo)(s,o);if(i.success&&i.file)return a.NextResponse.json({success:!0,file:i.file,message:"File uploaded successfully to R2"});return a.NextResponse.json({success:!1,error:i.error||"Upload failed"},{status:500})}catch(e){return console.error("Error uploading file to R2:",e),a.NextResponse.json({success:!1,error:"Failed to upload file to R2"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/media/upload/route",pathname:"/api/media/upload",filename:"route",bundlePath:"app/api/media/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\upload\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:v}=d,g="/api/media/upload/route";function y(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>l});var s=t(13539),o=t(77234),n=t(53797),i=t(98691),a=t(3474);let l={adapter:(0,s.N)(a._),providers:[(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await i.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await a._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>o});var s=t(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})},8379:(e,r,t)=>{t.d(r,{D0:()=>f,NA:()=>c,Z7:()=>p,fo:()=>u,rP:()=>n});var s=t(21841);t(38376);let o=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function n(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:i,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let i=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function l(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(i)?`${a}/${e}`:`${a}/${i}/${e}`}async function u(e,r="uploads"){try{let t=Date.now(),n=e.name.replace(/[^a-zA-Z0-9.-]/g,"_"),a=`${r}/${t}_${n}`,u=await e.arrayBuffer(),p=new s.PutObjectCommand({Bucket:i,Key:a,Body:new Uint8Array(u),ContentType:e.type,ContentLength:e.size});await o.send(p);let c={key:a,name:e.name,size:e.size,type:d(e.name,e.type),url:l(a),lastModified:new Date,folder:r};return{success:!0,file:c}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function p(e){try{let r=new s.DeleteObjectCommand({Bucket:i,Key:e});return await o.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function c(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:i,Prefix:e?`${e}/`:void 0,MaxKeys:r}),n=await o.send(t);if(!n.Contents)return[];return n.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:d(t),url:l(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function d(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function f(e){return e.size>10485760?{valid:!1,error:"File size must be less than 10MB"}:["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type)?{valid:!0}:{valid:!1,error:"File type not supported"}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575,8376],()=>t(89062));module.exports=s})();