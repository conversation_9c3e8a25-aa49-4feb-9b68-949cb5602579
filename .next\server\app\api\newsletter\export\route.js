"use strict";(()=>{var e={};e.id=2480,e.ids=[2480],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},41346:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>w,patchFetch:()=>x,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>b,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(49303),i=t(88716),o=t(60670),a=t(87070),u=t(75571),l=t(95306),c=t(3474);async function p(e){try{let r=await (0,u.getServerSession)(l.L);if(!r||"ADMIN"!==r.user.role)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("active"),n=t.get("source"),i=t.get("format")||"csv",o={};null!==s&&(o.isActive="true"===s),n&&(o.source=n);let p=await c._.newsletterSubscriber.findMany({where:o,orderBy:{subscribedAt:"desc"}});if("csv"===i){let e=p.map(e=>[e.email,e.name||"",e.isActive?"Active":"Inactive",e.source||"",e.subscribedAt.toISOString(),e.unsubscribedAt?.toISOString()||""]),r=["Email,Name,Status,Source,Subscribed At,Unsubscribed At",...e.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n");return new a.NextResponse(r,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="newsletter-subscribers-${new Date().toISOString().split("T")[0]}.csv"`}})}if("json"===i)return a.NextResponse.json({success:!0,data:{subscribers:p,exportedAt:new Date().toISOString(),total:p.length}});return a.NextResponse.json({success:!1,error:"Unsupported format. Use csv or json."},{status:400})}catch(e){return console.error("Error exporting newsletter subscribers:",e),a.NextResponse.json({success:!1,error:"Failed to export newsletter subscribers"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/newsletter/export/route",pathname:"/api/newsletter/export",filename:"route",bundlePath:"app/api/newsletter/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\export\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:b}=d,w="/api/newsletter/export/route";function x(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),n=t(77234),i=t(53797),o=t(98691),a=t(3474);let u={adapter:(0,s.N)(a._),providers:[(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await o.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await a._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>n});var s=t(53524);let n=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(s,i,a):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(41346));module.exports=s})();