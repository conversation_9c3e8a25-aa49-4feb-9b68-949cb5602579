"use strict";(()=>{var e={};e.id=5497,e.ids=[5497],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},84395:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>w,routeModule:()=>f,serverHooks:()=>y,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>b,GET:()=>d,POST:()=>p});var n=t(49303),i=t(88716),a=t(60670),o=t(87070),u=t(75571),c=t(95306),l=t(3474);async function p(e){try{let{email:r,name:t,whatsapp:s,source:n="homepage"}=await e.json();if(!r)return o.NextResponse.json({success:!1,error:"Email is required"},{status:400});let i=await l._.newsletterSubscriber.findUnique({where:{email:r}});if(i){if(i.isActive)return o.NextResponse.json({success:!1,error:"Email is already subscribed"},{status:409});{let e=await l._.newsletterSubscriber.update({where:{email:r},data:{isActive:!0,name:t||i.name,whatsapp:s||i.whatsapp,source:n,subscribedAt:new Date,unsubscribedAt:null}});return o.NextResponse.json({success:!0,data:e,message:"Successfully resubscribed to newsletter"})}}let a=await l._.newsletterSubscriber.create({data:{email:r,name:t,whatsapp:s,source:n,isActive:!0}});return o.NextResponse.json({success:!0,data:a,message:"Successfully subscribed to newsletter"})}catch(e){return console.error("Error subscribing to newsletter:",e),o.NextResponse.json({success:!1,error:"Failed to subscribe to newsletter"},{status:500})}}async function d(e){try{let r=await (0,u.getServerSession)(c.L);if(!r||"ADMIN"!==r.user.role)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"50"),i=t.get("active"),a=t.get("source"),p=(s-1)*n,d={};null!==i&&(d.isActive="true"===i),a&&(d.source=a);let[b,f]=await Promise.all([l._.newsletterSubscriber.findMany({where:d,orderBy:{subscribedAt:"desc"},skip:p,take:n}),l._.newsletterSubscriber.count({where:d})]),w=await l._.newsletterSubscriber.groupBy({by:["isActive"],_count:!0}),m=w.find(e=>e.isActive)?._count||0,y=w.find(e=>!e.isActive)?._count||0;return o.NextResponse.json({success:!0,data:{subscribers:b,pagination:{page:s,limit:n,total:f,pages:Math.ceil(f/n)},stats:{total:f,active:m,inactive:y}}})}catch(e){return console.error("Error fetching newsletter subscribers:",e),o.NextResponse.json({success:!1,error:"Failed to fetch newsletter subscribers"},{status:500})}}async function b(e){try{let{searchParams:r}=new URL(e.url),t=r.get("email");if(!t)return o.NextResponse.json({success:!1,error:"Email is required"},{status:400});let s=await l._.newsletterSubscriber.update({where:{email:t},data:{isActive:!1,unsubscribedAt:new Date}});return o.NextResponse.json({success:!0,data:s,message:"Successfully unsubscribed from newsletter"})}catch(e){return console.error("Error unsubscribing from newsletter:",e),o.NextResponse.json({success:!1,error:"Failed to unsubscribe from newsletter"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/newsletter/route",pathname:"/api/newsletter",filename:"route",bundlePath:"app/api/newsletter/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:m,serverHooks:y}=f,g="/api/newsletter/route";function v(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),n=t(77234),i=t(53797),a=t(98691),o=t(3474);let u={adapter:(0,s.N)(o._),providers:[(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await a.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>n});var s=t(53524);let n=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=n?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(45609));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(84395));module.exports=s})();