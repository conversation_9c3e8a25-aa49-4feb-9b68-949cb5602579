"use strict";(()=>{var e={};e.id=8663,e.ids=[8663],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},74669:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var i={};t.r(i),t.d(i,{POST:()=>d});var s=t(49303),a=t(88716),n=t(60670),o=t(87070),l=t(75571),u=t(95306),c=t(3474),p=t(54211);async function d(e){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.id)return o.NextResponse.json({error:"Authentication required"},{status:401});let r=await c._.notification.updateMany({where:{userId:e.user.id,isRead:!1,OR:[{expiresAt:null},{expiresAt:{gt:new Date}}]},data:{isRead:!0}});return p.kg.info(`Marked ${r.count} notifications as read for user ${e.user.id}`),o.NextResponse.json({success:!0,message:`Marked ${r.count} notifications as read`,count:r.count})}catch(e){return p.kg.error("Failed to mark all notifications as read",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/notifications/mark-all-read/route",pathname:"/api/notifications/mark-all-read",filename:"route",bundlePath:"app/api/notifications/mark-all-read/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\mark-all-read\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:g}=f,y="/api/notifications/mark-all-read/route";function v(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},95306:(e,r,t)=>{t.d(r,{L:()=>l});var i=t(13539),s=t(77234),a=t(53797),n=t(98691),o=t(3474);let l={adapter:(0,i.N)(o._),providers:[(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:i}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>s});var i=t(53524);let s=globalThis.prisma??new i.PrismaClient({log:["error"]})},54211:(e,r,t)=>{var i;t.d(r,{kg:()=>a}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class s{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:r,level:t,message:s,context:a,error:n,userId:o,requestId:l}=e,u=i[t],c=`[${r}] ${u}: ${s}`;return o&&(c+=` | User: ${o}`),l&&(c+=` | Request: ${l}`),a&&Object.keys(a).length>0&&(c+=` | Context: ${JSON.stringify(a)}`),n&&(c+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(c+=`
Stack: ${n.stack}`)),c}log(e,r,t,i){if(!this.shouldLog(e))return;let s={timestamp:new Date().toISOString(),level:e,message:r,context:t,error:i},a=this.formatMessage(s);if(this.isDevelopment)switch(e){case 0:console.error(a);break;case 1:console.warn(a);break;case 2:console.info(a);break;case 3:console.debug(a)}else console.log(JSON.stringify(s))}error(e,r,t){this.log(0,e,t,r)}warn(e,r){this.log(1,e,r)}info(e,r){this.log(2,e,r)}debug(e,r){this.log(3,e,r)}apiRequest(e,r,t,i){this.info(`API ${e} ${r}`,{...i,userId:t,type:"api_request"})}apiResponse(e,r,t,i,s){this.info(`API ${e} ${r} - ${t}`,{...s,statusCode:t,duration:i,type:"api_response"})}apiError(e,r,t,i,s){this.error(`API ${e} ${r} failed`,t,{...s,userId:i,type:"api_error"})}authSuccess(e,r,t){this.info("Authentication successful",{...t,userId:e,method:r,type:"auth_success"})}authFailure(e,r,t,i){this.warn("Authentication failed",{...i,email:e,method:r,reason:t,type:"auth_failure"})}dbQuery(e,r,t,i){this.debug(`DB ${e} on ${r}`,{...i,operation:e,table:r,duration:t,type:"db_query"})}dbError(e,r,t,i){this.error(`DB ${e} on ${r} failed`,t,{...i,operation:e,table:r,type:"db_error"})}securityEvent(e,r,t){this.log("high"===r?0:"medium"===r?1:2,`Security event: ${e}`,{...t,severity:r,type:"security_event"})}rateLimitHit(e,r,t,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:r,window:t,type:"rate_limit"})}emailSent(e,r,t,i){this.info("Email sent",{...i,to:e,subject:r,template:t,type:"email_sent"})}emailError(e,r,t,i){this.error("Email failed to send",t,{...i,to:e,subject:r,type:"email_error"})}performance(e,r,t){this.log(r>5e3?1:3,`Performance: ${e} took ${r}ms`,{...t,operation:e,duration:r,type:"performance"})}}let a=new s},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=s?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(i,a,o):i[a]=e[a]}return i.default=e,t&&t.set(e,i),i}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575],()=>t(74669));module.exports=i})();