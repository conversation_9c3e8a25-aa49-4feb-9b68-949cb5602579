"use strict";(()=>{var e={};e.id=5498,e.ids=[5498],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},55954:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>N});var i={};t.r(i),t.d(i,{POST:()=>y});var o=t(49303),a=t(88716),n=t(60670),s=t(87070),d=t(75571),c=t(95306),p=t(3474),u=t(89585),l=t(54211);async function y(e){try{let e=await (0,d.getServerSession)(c.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return s.NextResponse.json({error:"Admin access required"},{status:401});l.kg.info("Starting price drop check for all wishlisted items");let r=await p._.wishlistItem.findMany({include:{product:!0,user:{include:{preferences:!0}}}}),t=0,i=new Set;for(let e of r)try{if(i.has(e.productId))continue;i.add(e.productId);let r=e.product,o=e.user.preferences;if(!o?.priceDropAlerts)continue;let a=r.price||0;if((a.toString().endsWith(".99")||a.toString().endsWith(".49"))&&a>0){let e=Math.round(120*a)/100,i=Math.round((e-a)/e*100);for(let o of(await p._.wishlistItem.findMany({where:{productId:r.id,user:{preferences:{priceDropAlerts:!0}}},include:{user:!0}})))try{await u.un.priceDropAlert(o.userId,{productId:r.id,productName:r.name,oldPrice:e,newPrice:a,currency:"INR",discountPercentage:i}),t++,l.kg.info("Price drop notification sent",{userId:o.userId,productId:r.id,productName:r.name,oldPrice:e,newPrice:a,discountPercentage:i})}catch(e){l.kg.error("Failed to send price drop notification",{error:e,userId:o.userId,productId:r.id})}}}catch(r){l.kg.error("Error processing wishlist item for price drop",{error:r,itemId:e.id,productId:e.productId})}return l.kg.info("Price drop check completed",{totalItems:r.length,notificationsSent:t}),s.NextResponse.json({success:!0,message:"Price drop check completed",stats:{totalItems:r.length,notificationsSent:t}})}catch(e){return l.kg.error("Failed to check for price drops",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/notifications/price-drop-check/route",pathname:"/api/notifications/price-drop-check",filename:"route",bundlePath:"app/api/notifications/price-drop-check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\price-drop-check\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:N,serverHooks:I}=m,g="/api/notifications/price-drop-check/route";function h(){return(0,n.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:N})}},89585:(e,r,t)=>{t.d(r,{$T:()=>d,aZ:()=>a,kg:()=>s,un:()=>n});var i=t(68602),o=t(53524);let a={orderPlaced:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:o.NotificationPriority.HIGH,sendEmail:!0})},n={itemAdded:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:o.NotificationPriority.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:o.NotificationPriority.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:o.NotificationPriority.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},s={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:o.NotificationPriority.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:o.NotificationPriority.LOW,sendEmail:!1})},d={adminMessage:async(e,r)=>await i.B.createNotification({userId:e,type:r.type||o.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||o.NotificationPriority.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?o.NotificationPriority.URGENT:"high"===r.severity?o.NotificationPriority.HIGH:o.NotificationPriority.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:o.NotificationPriority.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||o.NotificationPriority.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:o.NotificationType.BROADCAST,title:e.title,message:e.message,priority:e.priority||o.NotificationPriority.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:o.NotificationType.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:o.NotificationPriority.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,t&&t.set(e,i),i}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,5245,2125],()=>t(55954));module.exports=i})();