"use strict";(()=>{var e={};e.id=87,e.ids=[87],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},12401:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>P,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>I,serverHooks:()=>R,staticGenerationAsyncStorage:()=>h});var i={};t.r(i),t.d(i,{GET:()=>E,PUT:()=>w});var a=t(49303),o=t(88716),s=t(60670),n=t(87070),d=t(65630),c=t(75571),u=t(95306),l=t(3474),p=t(84875),m=t(54211),y=t(8149),f=t(89585);let N=d.Ry({status:d.Km(["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"]).optional(),paymentStatus:d.Km(["PENDING","PAID","FAILED","REFUNDED"]).optional(),notes:d.Z_().optional()}),E=(0,p.lm)(async(e,{params:r})=>{m.kg.apiRequest("GET",`/api/orders/${r.id}`),await (0,y.er)(e,y.Xw,20);let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id)throw new p._7("Authentication required");let i=r.id,a="ADMIN"===t.user.role;try{let e=await l._.order.findUnique({where:{id:i},include:{items:{include:{product:{select:{id:!0,name:!0,slug:!0,price:!0,images:{select:{url:!0,alt:!0},take:1}}}}},address:!0,user:!!a&&{select:{id:!0,name:!0,email:!0,phone:!0}}}});if(!e)throw new p.p8("Order not found");if(!a&&e.userId!==t.user.id)throw new p._7("Unauthorized access to order");return m.kg.info("Order details retrieved",{orderId:i,userId:t.user.id,isAdmin:a,orderStatus:e.status}),n.NextResponse.json({success:!0,order:e})}catch(e){throw m.kg.error("Failed to retrieve order details",e),e}}),w=(0,p.lm)(async(e,{params:r})=>{m.kg.apiRequest("PUT",`/api/orders/${r.id}`),await (0,y.er)(e,y.Xw,10);let t=await (0,c.getServerSession)(u.L);if(!t?.user?.id||"ADMIN"!==t.user.role)throw new p._7("Admin access required");let i=r.id,a=await e.json(),o=N.parse(a);try{let e=await l._.order.findUnique({where:{id:i},include:{user:!0,items:{include:{product:!0}},address:!0}});if(!e)throw new p.p8("Order not found");let r={updatedAt:new Date};o.status&&(r.status=o.status),o.paymentStatus&&(r.paymentStatus=o.paymentStatus),o.notes&&(r.notes=o.notes);let a=await l._.order.update({where:{id:i},data:r,include:{items:{include:{product:!0}},address:!0,user:!0}});m.kg.info("Order updated successfully",{orderId:i,adminId:t.user.id,previousStatus:e.status,newStatus:a.status,previousPaymentStatus:e.paymentStatus,newPaymentStatus:a.paymentStatus});try{if(o.status&&o.status!==e.status){let r={orderId:a.id,orderNumber:a.orderNumber};switch(o.status){case"PROCESSING":await f.aZ.orderProcessing(e.userId,r);break;case"SHIPPED":await f.aZ.orderShipped(e.userId,r);break;case"DELIVERED":await f.aZ.orderDelivered(e.userId,{...r,deliveredAt:new Date().toLocaleDateString()}),setTimeout(async()=>{try{let r=await l._.userPreference.findUnique({where:{userId:e.userId}});if(r?.reviewNotifications){let r=e.items.map(e=>e.productId),t=e.items.map(e=>e.product.name);await f.kg.reviewRequest(e.userId,{orderId:e.id,orderNumber:e.orderNumber,productIds:r,productNames:t}),m.kg.info("Review request notification sent after delivery",{orderId:e.id,userId:e.userId})}}catch(e){m.kg.error("Failed to send review request notification",e)}},2592e5);break;case"CANCELLED":await f.aZ.orderCancelled(e.userId,{...r,reason:o.notes})}m.kg.info("Order status notification sent",{orderId:i,userId:e.userId,status:a.status})}}catch(e){m.kg.error("Failed to send order status notification",e)}return n.NextResponse.json({success:!0,message:"Order updated successfully",order:a})}catch(e){throw m.kg.error("Failed to update order",e),e}}),I=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/orders/[id]/route",pathname:"/api/orders/[id]",filename:"route",bundlePath:"app/api/orders/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:R}=I,P="/api/orders/[id]/route";function v(){return(0,s.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:h})}},84875:(e,r,t)=>{t.d(r,{AY:()=>u,M_:()=>d,_7:()=>n,dR:()=>c,gz:()=>o,lm:()=>p,p8:()=>s});var i=t(87070),a=t(29489);class o extends Error{constructor(e,r=500,t="INTERNAL_ERROR",i){super(e),this.statusCode=r,this.code=t,this.details=i,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,o)}}class s extends o{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class n extends o{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends o{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends o{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends o{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends o{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function p(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){if(e instanceof o)return i.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof a.j){let r=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,details:r.details}},{status:r.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let r=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new u(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new n("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e);return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,...r.details&&{details:r.details}}},{status:r.statusCode})}return e instanceof Error&&e.message,i.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},89585:(e,r,t)=>{t.d(r,{$T:()=>d,aZ:()=>o,kg:()=>n,un:()=>s});var i=t(68602),a=t(53524);let o={orderPlaced:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.NotificationPriority.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:a.NotificationPriority.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:a.NotificationPriority.HIGH,sendEmail:!0})},s={itemAdded:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:a.NotificationPriority.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:a.NotificationPriority.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:a.NotificationPriority.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},n={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.B.createNotification({userId:e,type:a.NotificationType.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:a.NotificationPriority.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:a.NotificationPriority.LOW,sendEmail:!1})},d={adminMessage:async(e,r)=>await i.B.createNotification({userId:e,type:r.type||a.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||a.NotificationPriority.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?a.NotificationPriority.URGENT:"high"===r.severity?a.NotificationPriority.HIGH:a.NotificationPriority.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:a.NotificationPriority.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||a.NotificationPriority.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:a.NotificationType.BROADCAST,title:e.title,message:e.message,priority:e.priority||a.NotificationPriority.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:a.NotificationType.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:a.NotificationPriority.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},8149:(e,r,t)=>{t.d(r,{Ri:()=>o,Xw:()=>s,er:()=>d,jO:()=>n});var i=t(919);function a(e){let r=new i.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((i,a)=>{let o=r.get(t)||[0];0===o[0]&&r.set(t,o),o[0]+=1,o[0]>=e?a(Error("Rate limit exceeded")):i()})}}let o=a({interval:9e5,uniqueTokenPerInterval:500}),s=a({interval:6e4,uniqueTokenPerInterval:500}),n=a({interval:36e5,uniqueTokenPerInterval:500});async function d(e,r,t){let i=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,i)}catch(e){throw Error("Too many requests. Please try again later.")}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var n=a?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(i,o,n):i[o]=e[o]}return i.default=e,t&&t.set(e,i),i}(t(45609));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,9489,5245,5630,138,2125],()=>t(12401));module.exports=i})();