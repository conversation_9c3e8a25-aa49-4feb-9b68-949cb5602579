"use strict";(()=>{var e={};e.id=9146,e.ids=[9146],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},48404:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>O,requestAsyncStorage:()=>E,routeModule:()=>y,serverHooks:()=>R,staticGenerationAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>g});var a=r(49303),n=r(88716),i=r(60670),o=r(87070),u=r(65630),l=r(75571),c=r(95306),d=r(3474),p=r(84875),m=r(54211),f=r(8149);let h=u.Ry({page:u.Z_().optional().default("1"),limit:u.Z_().optional().default("10"),status:u.Km(["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"]).optional(),paymentStatus:u.Km(["PENDING","PAID","FAILED","REFUNDED"]).optional()}),g=(0,p.lm)(async e=>{m.kg.apiRequest("GET","/api/orders"),await (0,f.er)(e,f.Xw,30);let t=await (0,l.getServerSession)(c.L);if(!t?.user?.id)throw new p._7("Authentication required");let{searchParams:r}=new URL(e.url),s=Object.fromEntries(r.entries()),a=h.parse(s),n=parseInt(a.page),i=parseInt(a.limit),u=(n-1)*i,g="ADMIN"===t.user.role;try{let e={};g||(e.userId=t.user.id),a.status&&(e.status=a.status),a.paymentStatus&&(e.paymentStatus=a.paymentStatus);let[r,s]=await Promise.all([d._.order.findMany({where:e,include:{items:{include:{product:{select:{id:!0,name:!0,slug:!0,price:!0}}}},address:!0,user:!!g&&{select:{id:!0,name:!0,email:!0,phone:!0}}},orderBy:{createdAt:"desc"},skip:u,take:i}),d._.order.count({where:e})]),l=Math.ceil(s/i);return m.kg.info("Orders retrieved successfully",{userId:t.user.id,isAdmin:g,count:r.length,totalCount:s,page:n,filters:{status:a.status,paymentStatus:a.paymentStatus}}),o.NextResponse.json({success:!0,orders:r,pagination:{page:n,limit:i,totalCount:s,totalPages:l,hasNext:n<l,hasPrev:n>1}})}catch(e){throw m.kg.error("Failed to retrieve orders",e),e}}),y=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:E,staticGenerationAsyncStorage:w,serverHooks:R}=y,v="/api/orders/route";function O(){return(0,i.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:w})}},95306:(e,t,r)=>{r.d(t,{L:()=>u});var s=r(13539),a=r(77234),n=r(53797),i=r(98691),o=r(3474);let u={adapter:(0,s.N)(o._),providers:[(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await o._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await i.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await o._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>a});var s=r(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})},84875:(e,t,r)=>{r.d(t,{AY:()=>c,M_:()=>u,_7:()=>o,dR:()=>l,gz:()=>n,lm:()=>p,p8:()=>i});var s=r(87070),a=r(29489);class n extends Error{constructor(e,t=500,r="INTERNAL_ERROR",s){super(e),this.statusCode=t,this.code=r,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class i extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class o extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class u extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class l extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class d extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){if(e instanceof n)return s.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof a.j){let t=new i("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,details:t.details}},{status:t.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let t=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new c(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new o("Invalid user session");return new i("Invalid reference to related record");case"P2025":case"P2001":return new l;case"P2014":return new i("Missing required relationship");case"P2000":return new i("Input value is too long");case"P2004":return new i("Data constraint violation");default:return new d("Database operation failed",{code:e.code,message:e.message})}}(e);return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,...t.details&&{details:t.details}}},{status:t.statusCode})}return e instanceof Error&&e.message,s.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class a{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:a,context:n,error:i,userId:o,requestId:u}=e,l=s[r],c=`[${t}] ${l}: ${a}`;return o&&(c+=` | User: ${o}`),u&&(c+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(c+=` | Context: ${JSON.stringify(n)}`),i&&(c+=` | Error: ${i.message}`,this.isDevelopment&&i.stack&&(c+=`
Stack: ${i.stack}`)),c}log(e,t,r,s){if(!this.shouldLog(e))return;let a={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},n=this.formatMessage(a);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(a))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,a){this.info(`API ${e} ${t} - ${r}`,{...a,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,a){this.error(`API ${e} ${t} failed`,r,{...a,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new a},8149:(e,t,r)=>{r.d(t,{Ri:()=>n,Xw:()=>i,er:()=>u,jO:()=>o});var s=r(919);function a(e){let t=new s.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((s,a)=>{let n=t.get(r)||[0];0===n[0]&&t.set(r,n),n[0]+=1,n[0]>=e?a(Error("Rate limit exceeded")):s()})}}let n=a({interval:9e5,uniqueTokenPerInterval:500}),i=a({interval:6e4,uniqueTokenPerInterval:500}),o=a({interval:36e5,uniqueTokenPerInterval:500});async function u(e,t,r){let s=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,s)}catch(e){throw Error("Too many requests. Please try again later.")}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var a=r(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=a?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(s,n,o):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,6575,9489,5630,138],()=>r(48404));module.exports=s})();