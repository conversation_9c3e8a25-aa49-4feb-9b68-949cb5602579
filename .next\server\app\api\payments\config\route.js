"use strict";(()=>{var e={};e.id=4946,e.ids=[4946],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},98115:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>u});var o=s(49303),n=s(88716),a=s(60670),i=s(87070),c=s(54211);let u=(0,s(84875).lm)(async e=>{c.kg.apiRequest("GET","/api/payments/config");try{let e=["RAZORPAY_KEY_ID","RAZORPAY_KEY_SECRET","NEXT_PUBLIC_RAZORPAY_KEY_ID"].reduce((e,t)=>(e[t]=!!process.env[t],e),{}),t=Object.values(e).every(Boolean);return i.NextResponse.json({success:!0,configured:t,environment_variables:e,public_key:"rzp_test_H8VYcEtWS9hwc8",environment:"production",message:t?"Razorpay payment integration is properly configured":"Some environment variables are missing"})}catch(e){return c.kg.error("Payment config check failed",e),i.NextResponse.json({success:!1,configured:!1,error:"Failed to check payment configuration",details:e.message},{status:500})}}),l=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/config/route",pathname:"/api/payments/config",filename:"route",bundlePath:"app/api/payments/config/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\config\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=l,h="/api/payments/config/route";function g(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}},84875:(e,t,s)=>{s.d(t,{AY:()=>l,M_:()=>c,_7:()=>i,dR:()=>u,gz:()=>n,lm:()=>d,p8:()=>a});var r=s(87070),o=s(29489);class n extends Error{constructor(e,t=500,s="INTERNAL_ERROR",r){super(e),this.statusCode=t,this.code=s,this.details=r,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class a extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class i extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class c extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function d(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){if(e instanceof n)return r.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof o.j){let t=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return r.NextResponse.json({success:!1,error:{code:t.code,message:t.message,details:t.details}},{status:t.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let t=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new l(`${t} already exists`);case"P2003":let s=e.meta?.constraint;if(s?.includes("userId"))return new i("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e);return r.NextResponse.json({success:!1,error:{code:t.code,message:t.message,...t.details&&{details:t.details}}},{status:t.statusCode})}return e instanceof Error&&e.message,r.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},54211:(e,t,s)=>{var r;s.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(r||(r={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:s,message:o,context:n,error:a,userId:i,requestId:c}=e,u=r[s],l=`[${t}] ${u}: ${o}`;return i&&(l+=` | User: ${i}`),c&&(l+=` | Request: ${c}`),n&&Object.keys(n).length>0&&(l+=` | Context: ${JSON.stringify(n)}`),a&&(l+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(l+=`
Stack: ${a.stack}`)),l}log(e,t,s,r){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:s,error:r},n=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(o))}error(e,t,s){this.log(0,e,s,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,s,r){this.info(`API ${e} ${t}`,{...r,userId:s,type:"api_request"})}apiResponse(e,t,s,r,o){this.info(`API ${e} ${t} - ${s}`,{...o,statusCode:s,duration:r,type:"api_response"})}apiError(e,t,s,r,o){this.error(`API ${e} ${t} failed`,s,{...o,userId:r,type:"api_error"})}authSuccess(e,t,s){this.info("Authentication successful",{...s,userId:e,method:t,type:"auth_success"})}authFailure(e,t,s,r){this.warn("Authentication failed",{...r,email:e,method:t,reason:s,type:"auth_failure"})}dbQuery(e,t,s,r){this.debug(`DB ${e} on ${t}`,{...r,operation:e,table:t,duration:s,type:"db_query"})}dbError(e,t,s,r){this.error(`DB ${e} on ${t} failed`,s,{...r,operation:e,table:t,type:"db_error"})}securityEvent(e,t,s){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...s,severity:t,type:"security_event"})}rateLimitHit(e,t,s,r){this.warn("Rate limit exceeded",{...r,identifier:e,limit:t,window:s,type:"rate_limit"})}emailSent(e,t,s,r){this.info("Email sent",{...r,to:e,subject:t,template:s,type:"email_sent"})}emailError(e,t,s,r){this.error("Email failed to send",s,{...r,to:e,subject:t,type:"email_error"})}performance(e,t,s){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...s,operation:e,duration:t,type:"performance"})}}let n=new o}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972,9489],()=>s(98115));module.exports=r})();