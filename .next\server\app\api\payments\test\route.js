"use strict";(()=>{var e={};e.id=4411,e.ids=[4411],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},72595:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>_,patchFetch:()=>R,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>m});var a=r(49303),n=r(88716),i=r(60670),o=r(87070),c=r(75571),u=r(95306),l=r(62822),d=r(54211),p=r(84875);let m=(0,p.lm)(async e=>{d.kg.apiRequest("GET","/api/payments/test");let t=await (0,c.getServerSession)(u.L);if(!t?.user||"ADMIN"!==t.user.role)throw new p._7("Admin access required");try{let e=`TEST_${Date.now()}`,r=["RAZORPAY_KEY_ID","RAZORPAY_KEY_SECRET","NEXT_PUBLIC_RAZORPAY_KEY_ID"].filter(e=>!process.env[e]);if(r.length>0)return o.NextResponse.json({success:!1,error:"Missing environment variables",missingVars:r},{status:500});let s=(0,l.ml)(100),a=await (0,l.iP)({amount:100,currency:"INR",receipt:e,notes:{test:"true",userId:t.user.id,timestamp:new Date().toISOString()}});return d.kg.info("Payment test successful",{testOrderId:a.id,amount:100,receipt:e}),o.NextResponse.json({success:!0,message:"Payment integration test successful",test_results:{environment_variables:"OK",amount_validation:s?"OK":"FAILED",order_creation:"OK",razorpay_connection:"OK"},test_order:{id:a.id,amount:a.amount,currency:a.currency,receipt:a.receipt,status:a.status},configuration:{razorpay_key_id:"rzp_test_H8VYcEtWS9hwc8",environment:"production"}})}catch(e){return d.kg.error("Payment test failed",e),o.NextResponse.json({success:!1,error:"Payment integration test failed",details:e.message,test_results:{environment_variables:"UNKNOWN",amount_validation:"UNKNOWN",order_creation:"FAILED",razorpay_connection:"FAILED"}},{status:500})}}),g=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/test/route",pathname:"/api/payments/test",filename:"route",bundlePath:"app/api/payments/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\test\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:f}=g,_="/api/payments/test/route";function R(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},95306:(e,t,r)=>{r.d(t,{L:()=>c});var s=r(13539),a=r(77234),n=r(53797),i=r(98691),o=r(3474);let c={adapter:(0,s.N)(o._),providers:[(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await o._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await i.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await o._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>a});var s=r(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})},84875:(e,t,r)=>{r.d(t,{AY:()=>l,M_:()=>c,_7:()=>o,dR:()=>u,gz:()=>n,lm:()=>p,p8:()=>i});var s=r(87070),a=r(29489);class n extends Error{constructor(e,t=500,r="INTERNAL_ERROR",s){super(e),this.statusCode=t,this.code=r,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class i extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class o extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class c extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class d extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){if(e instanceof n)return s.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof a.j){let t=new i("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,details:t.details}},{status:t.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let t=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new l(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new o("Invalid user session");return new i("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new i("Missing required relationship");case"P2000":return new i("Input value is too long");case"P2004":return new i("Data constraint violation");default:return new d("Database operation failed",{code:e.code,message:e.message})}}(e);return s.NextResponse.json({success:!1,error:{code:t.code,message:t.message,...t.details&&{details:t.details}}},{status:t.statusCode})}return e instanceof Error&&e.message,s.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class a{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:a,context:n,error:i,userId:o,requestId:c}=e,u=s[r],l=`[${t}] ${u}: ${a}`;return o&&(l+=` | User: ${o}`),c&&(l+=` | Request: ${c}`),n&&Object.keys(n).length>0&&(l+=` | Context: ${JSON.stringify(n)}`),i&&(l+=` | Error: ${i.message}`,this.isDevelopment&&i.stack&&(l+=`
Stack: ${i.stack}`)),l}log(e,t,r,s){if(!this.shouldLog(e))return;let a={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},n=this.formatMessage(a);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(a))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,a){this.info(`API ${e} ${t} - ${r}`,{...a,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,a){this.error(`API ${e} ${t} failed`,r,{...a,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new a},62822:(e,t,r)=>{r.d(t,{FT:()=>u,My:()=>p,iP:()=>c,mT:()=>d,ml:()=>m,sS:()=>l});var s=r(41212),a=r.n(s),n=r(54211),i=r(84875);let o=new(a())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function c(e){try{n.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let t=await o.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return n.kg.info("Razorpay order created successfully",{orderId:t.id,amount:t.amount,receipt:t.receipt}),t}catch(e){throw n.kg.error("Failed to create Razorpay order",e),new i.gz("Failed to create payment order",500)}}function u(e){try{let t=r(84770).createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return n.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:t}),t}catch(e){return n.kg.error("Payment signature verification failed",e),!1}}async function l(e){try{n.kg.info("Fetching payment details",{paymentId:e});let t=await o.payments.fetch(e);return n.kg.info("Payment details fetched successfully",{paymentId:t.id,status:t.status,amount:t.amount}),t}catch(e){throw n.kg.error("Failed to fetch payment details",e),new i.gz("Failed to fetch payment details",500)}}function d(e){return Math.round(100*e)}function p(e="ORDER"){let t=Date.now(),r=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${t}_${r}`}function m(e){return e>=100&&e<=15e8&&Number.isInteger(e)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,6575,9489,656],()=>r(72595));module.exports=s})();