"use strict";(()=>{var e={};e.id=999,e.ids=[999],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},77949:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>A,requestAsyncStorage:()=>w,routeModule:()=>E,serverHooks:()=>P,staticGenerationAsyncStorage:()=>R});var i={};t.r(i),t.d(i,{POST:()=>h});var a=t(49303),o=t(88716),s=t(60670),n=t(87070),d=t(65630),c=t(75571),u=t(95306),p=t(3474),m=t(62822),y=t(84875),l=t(54211),f=t(8149),N=t(95921),I=t(89585);let g=d.Ry({razorpay_order_id:d.Z_(),razorpay_payment_id:d.Z_(),razorpay_signature:d.Z_(),order_id:d.Z_()}),h=(0,y.lm)(async e=>{l.kg.apiRequest("POST","/api/payments/verify"),await (0,f.er)(e,f.Xw,20);let r=await (0,c.getServerSession)(u.L);if(!r?.user?.id)throw new y._7("Authentication required");let t=await e.json(),{razorpay_order_id:i,razorpay_payment_id:a,razorpay_signature:o,order_id:s}=g.parse(t);l.kg.info("Verifying payment",{userId:r.user.id,orderId:s,razorpayOrderId:i,razorpayPaymentId:a});try{let e=await p._.order.findUnique({where:{id:s},include:{items:{include:{product:!0}},address:!0,user:!0}});if(!e)throw new y.p8("Order not found");if(e.userId!==r.user.id)throw new y._7("Unauthorized access to order");if(e.paymentId!==i)throw new y.p8("Order ID mismatch");if(!(0,m.FT)({razorpay_order_id:i,razorpay_payment_id:a,razorpay_signature:o}))throw l.kg.warn("Invalid payment signature",{orderId:s,razorpayOrderId:i,razorpayPaymentId:a,userId:r.user.id}),await p._.order.update({where:{id:s},data:{paymentStatus:"FAILED",status:"CANCELLED",notes:`Payment verification failed. Payment ID: ${a}`}}),new y.p8("Payment verification failed");let t=await (0,m.sS)(a),d=await p._.order.update({where:{id:s},data:{paymentStatus:"PAID",status:"CONFIRMED",paymentMethod:t.method||"razorpay",notes:`Payment successful. Payment ID: ${a}, Method: ${t.method||"unknown"}`},include:{items:{include:{product:!0}},address:!0}});l.kg.info("Payment verified successfully",{orderId:s,orderNumber:e.orderNumber,razorpayPaymentId:a,amount:e.total,userId:r.user.id,paymentMethod:t.method});try{if(e.user.email&&e.address){let r=e.items.map(e=>({name:e.product.name,quantity:e.quantity,price:e.price})),t=`${e.address.firstName} ${e.address.lastName}
${e.address.address1}
${e.address.address2?e.address.address2+"\n":""}${e.address.city}, ${e.address.state} ${e.address.postalCode}
${e.address.country}`;await (0,N.bn)(e.user.email,{orderId:e.orderNumber,items:r,total:e.total,shippingAddress:t}),l.kg.info("Order confirmation email sent",{orderId:s,email:e.user.email})}}catch(e){l.kg.error("Failed to send order confirmation email",e)}try{await I.aZ.orderConfirmed(e.userId,{orderId:e.id,orderNumber:e.orderNumber}),l.kg.info("Order confirmed notification sent",{orderId:s,userId:e.userId})}catch(e){l.kg.error("Failed to send order confirmed notification",e)}return n.NextResponse.json({success:!0,message:"Payment verified successfully",order:{id:d.id,orderNumber:d.orderNumber,status:d.status,paymentStatus:d.paymentStatus,total:d.total,paymentId:a}})}catch(e){if(l.kg.error("Payment verification failed",e),!(e instanceof y.p8)&&!(e instanceof y._7))try{await p._.order.update({where:{id:s},data:{paymentStatus:"FAILED",status:"CANCELLED",notes:`Payment verification error: ${e.message}`}})}catch(e){l.kg.error("Failed to update order status",e)}throw e}}),E=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/payments/verify/route",pathname:"/api/payments/verify",filename:"route",bundlePath:"app/api/payments/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\verify\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:w,staticGenerationAsyncStorage:R,serverHooks:P}=E,v="/api/payments/verify/route";function A(){return(0,s.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:R})}},84875:(e,r,t)=>{t.d(r,{AY:()=>u,M_:()=>d,_7:()=>n,dR:()=>c,gz:()=>o,lm:()=>m,p8:()=>s});var i=t(87070),a=t(29489);class o extends Error{constructor(e,r=500,t="INTERNAL_ERROR",i){super(e),this.statusCode=r,this.code=t,this.details=i,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,o)}}class s extends o{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class n extends o{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends o{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends o{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends o{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends o{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){if(e instanceof o)return i.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof a.j){let r=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,details:r.details}},{status:r.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let r=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new u(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new n("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e);return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,...r.details&&{details:r.details}}},{status:r.statusCode})}return e instanceof Error&&e.message,i.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},89585:(e,r,t)=>{t.d(r,{$T:()=>d,aZ:()=>o,kg:()=>n,un:()=>s});var i=t(68602),a=t(53524);let o={orderPlaced:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.NotificationPriority.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:a.NotificationPriority.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:a.NotificationPriority.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:a.NotificationPriority.HIGH,sendEmail:!0})},s={itemAdded:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:a.NotificationPriority.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:a.NotificationPriority.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:a.NotificationPriority.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},n={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.B.createNotification({userId:e,type:a.NotificationType.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:a.NotificationPriority.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:a.NotificationPriority.LOW,sendEmail:!1})},d={adminMessage:async(e,r)=>await i.B.createNotification({userId:e,type:r.type||a.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||a.NotificationPriority.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?a.NotificationPriority.URGENT:"high"===r.severity?a.NotificationPriority.HIGH:a.NotificationPriority.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:a.NotificationPriority.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.B.createNotification({userId:e,type:a.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||a.NotificationPriority.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:a.NotificationType.BROADCAST,title:e.title,message:e.message,priority:e.priority||a.NotificationPriority.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:a.NotificationType.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:a.NotificationPriority.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},62822:(e,r,t)=>{t.d(r,{FT:()=>c,My:()=>m,iP:()=>d,mT:()=>p,ml:()=>y,sS:()=>u});var i=t(41212),a=t.n(i),o=t(54211),s=t(84875);let n=new(a())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function d(e){try{o.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let r=await n.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return o.kg.info("Razorpay order created successfully",{orderId:r.id,amount:r.amount,receipt:r.receipt}),r}catch(e){throw o.kg.error("Failed to create Razorpay order",e),new s.gz("Failed to create payment order",500)}}function c(e){try{let r=t(84770).createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return o.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:r}),r}catch(e){return o.kg.error("Payment signature verification failed",e),!1}}async function u(e){try{o.kg.info("Fetching payment details",{paymentId:e});let r=await n.payments.fetch(e);return o.kg.info("Payment details fetched successfully",{paymentId:r.id,status:r.status,amount:r.amount}),r}catch(e){throw o.kg.error("Failed to fetch payment details",e),new s.gz("Failed to fetch payment details",500)}}function p(e){return Math.round(100*e)}function m(e="ORDER"){let r=Date.now(),t=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${r}_${t}`}function y(e){return e>=100&&e<=15e8&&Number.isInteger(e)}},8149:(e,r,t)=>{t.d(r,{Ri:()=>o,Xw:()=>s,er:()=>d,jO:()=>n});var i=t(919);function a(e){let r=new i.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((i,a)=>{let o=r.get(t)||[0];0===o[0]&&r.set(t,o),o[0]+=1,o[0]>=e?a(Error("Rate limit exceeded")):i()})}}let o=a({interval:9e5,uniqueTokenPerInterval:500}),s=a({interval:6e4,uniqueTokenPerInterval:500}),n=a({interval:36e5,uniqueTokenPerInterval:500});async function d(e,r,t){let i=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,i)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,9489,5245,5630,138,656,2125],()=>t(77949));module.exports=i})();