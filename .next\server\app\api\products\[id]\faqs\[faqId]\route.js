"use strict";(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},72033:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>f,routeModule:()=>l,serverHooks:()=>F,staticGenerationAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{DELETE:()=>p,GET:()=>i,PUT:()=>c});var o=t(49303),a=t(88716),n=t(60670),d=t(87070),u=t(3474);async function i(e,{params:r}){try{let e=await u._.productFAQ.findFirst({where:{id:r.faqId,productId:r.id}});if(!e)return d.NextResponse.json({success:!1,error:"FAQ not found"},{status:404});return d.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching FAQ:",e),d.NextResponse.json({success:!1,error:"Failed to fetch FAQ"},{status:500})}}async function c(e,{params:r}){try{let{question:t,answer:s,position:o,isActive:a}=await e.json(),n=await u._.productFAQ.update({where:{id:r.faqId},data:{...t&&{question:t},...s&&{answer:s},...void 0!==o&&{position:o},...void 0!==a&&{isActive:a}}});return d.NextResponse.json({success:!0,data:n,message:"FAQ updated successfully"})}catch(e){return console.error("Error updating FAQ:",e),d.NextResponse.json({success:!1,error:"Failed to update FAQ"},{status:500})}}async function p(e,{params:r}){try{return await u._.productFAQ.delete({where:{id:r.faqId}}),d.NextResponse.json({success:!0,message:"FAQ deleted successfully"})}catch(e){return console.error("Error deleting FAQ:",e),d.NextResponse.json({success:!1,error:"Failed to delete FAQ"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/products/[id]/faqs/[faqId]/route",pathname:"/api/products/[id]/faqs/[faqId]",filename:"route",bundlePath:"app/api/products/[id]/faqs/[faqId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\[faqId]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:q,serverHooks:F}=l,h="/api/products/[id]/faqs/[faqId]/route";function x(){return(0,n.patchFetch)({serverHooks:F,staticGenerationAsyncStorage:q})}},3474:(e,r,t)=>{t.d(r,{_:()=>o});var s=t(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(72033));module.exports=s})();