"use strict";(()=>{var e={};e.id=6948,e.ids=[6948],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},17888:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>m,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>d});var o=t(49303),a=t(88716),i=t(60670),n=t(87070),u=t(3474);async function c(e,{params:r}){try{let e=await u._.productFAQ.findMany({where:{productId:r.id,isActive:!0},orderBy:{position:"asc"}});return n.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching FAQs:",e),n.NextResponse.json({success:!1,error:"Failed to fetch FAQs"},{status:500})}}async function d(e,{params:r}){try{let{question:t,answer:s,position:o}=await e.json();if(!t||!s)return n.NextResponse.json({success:!1,error:"Question and answer are required"},{status:400});let a=o;if(void 0===a){let e=await u._.productFAQ.findFirst({where:{productId:r.id},orderBy:{position:"desc"}});a=e?e.position+1:0}let i=await u._.productFAQ.create({data:{question:t,answer:s,position:a,productId:r.id}});return n.NextResponse.json({success:!0,data:i,message:"FAQ created successfully"})}catch(e){return console.error("Error creating FAQ:",e),n.NextResponse.json({success:!1,error:"Failed to create FAQ"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/products/[id]/faqs/route",pathname:"/api/products/[id]/faqs",filename:"route",bundlePath:"app/api/products/[id]/faqs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:f,serverHooks:h}=p,x="/api/products/[id]/faqs/route";function m(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},3474:(e,r,t)=>{t.d(r,{_:()=>o});var s=t(53524);let o=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(17888));module.exports=s})();