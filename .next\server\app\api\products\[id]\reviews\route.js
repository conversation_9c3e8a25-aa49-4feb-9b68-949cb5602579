"use strict";(()=>{var e={};e.id=1659,e.ids=[1659],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},3921:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>w,routeModule:()=>N,serverHooks:()=>g,staticGenerationAsyncStorage:()=>E});var i={};t.r(i),t.d(i,{GET:()=>y,POST:()=>f});var o=t(49303),a=t(88716),s=t(60670),n=t(87070),d=t(75571),c=t(95306),u=t(3474),p=t(84875),l=t(54211),m=t(89585);let y=(0,p.lm)(async(e,{params:r})=>{let t=r.id;l.kg.apiRequest("GET",`/api/products/${t}/reviews`);let i=await u._.review.findMany({where:{productId:t,status:"APPROVED"},include:{user:{select:{id:!0,name:!0,avatar:!0}}},orderBy:{createdAt:"desc"}});return l.kg.info("Product reviews fetched",{productId:t,count:i.length}),n.NextResponse.json({success:!0,data:i})}),f=(0,p.lm)(async(e,{params:r})=>{let t,i;let o=r.id;l.kg.apiRequest("POST",`/api/products/${o}/reviews`);let a=await (0,d.getServerSession)(c.L);if(!a?.user)throw new p._7;let{rating:s,title:y,content:f}=await e.json();if(!s||s<1||s>5)throw new p.p8("Rating must be between 1 and 5");if(!a.user.id&&!a.user.email)throw new p._7("Invalid session. Please log out and log back in.");if(a.user.id?(t=await u._.user.findUnique({where:{id:a.user.id},select:{id:!0,email:!0,name:!0}}),i=a.user.id):a.user.email&&(t=await u._.user.findUnique({where:{email:a.user.email},select:{id:!0,email:!0,name:!0}}),i=t?.id),!t||!i)throw new p._7("User not found. Please log out and log back in.");if(!await u._.product.findUnique({where:{id:o},select:{id:!0,name:!0}}))throw new p.dR("Product");if(await u._.review.findUnique({where:{userId_productId:{userId:i,productId:o}}}))throw new p.AY("You have already reviewed this product");let N=await u._.order.findFirst({where:{userId:i,items:{some:{productId:o}},paymentStatus:"PAID"}}),w=await u._.review.create({data:{rating:s,title:y||null,content:f||null,isVerified:!!N,status:"PENDING",userId:i,productId:o},include:{user:{select:{id:!0,name:!0,avatar:!0}}}});l.kg.info("Review created",{reviewId:w.id,productId:o,userId:i,rating:s,isVerified:w.isVerified});try{let e=await u._.product.findUnique({where:{id:o},select:{name:!0}});e&&await m.kg.reviewSubmitted(i,{reviewId:w.id,productId:o,productName:e.name,rating:s})}catch(e){l.kg.error("Failed to send review submitted notification",e)}return n.NextResponse.json({success:!0,data:w,message:"Review submitted for approval"})}),N=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/products/[id]/reviews/route",pathname:"/api/products/[id]/reviews",filename:"route",bundlePath:"app/api/products/[id]/reviews/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\reviews\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:w,staticGenerationAsyncStorage:E,serverHooks:g}=N,v="/api/products/[id]/reviews/route";function h(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:E})}},84875:(e,r,t)=>{t.d(r,{AY:()=>u,M_:()=>d,_7:()=>n,dR:()=>c,gz:()=>a,lm:()=>l,p8:()=>s});var i=t(87070),o=t(29489);class a extends Error{constructor(e,r=500,t="INTERNAL_ERROR",i){super(e),this.statusCode=r,this.code=t,this.details=i,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,a)}}class s extends a{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class n extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends a{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends a{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends a{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends a{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function l(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){if(e instanceof a)return i.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof o.j){let r=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,details:r.details}},{status:r.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let r=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new u(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new n("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e);return i.NextResponse.json({success:!1,error:{code:r.code,message:r.message,...r.details&&{details:r.details}}},{status:r.statusCode})}return e instanceof Error&&e.message,i.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},89585:(e,r,t)=>{t.d(r,{$T:()=>d,aZ:()=>a,kg:()=>n,un:()=>s});var i=t(68602),o=t(53524);let a={orderPlaced:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:o.NotificationPriority.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:o.NotificationPriority.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:o.NotificationPriority.HIGH,sendEmail:!0})},s={itemAdded:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:o.NotificationPriority.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:o.NotificationPriority.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:o.NotificationPriority.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},n={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:o.NotificationPriority.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:o.NotificationPriority.LOW,sendEmail:!1})},d={adminMessage:async(e,r)=>await i.B.createNotification({userId:e,type:r.type||o.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||o.NotificationPriority.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?o.NotificationPriority.URGENT:"high"===r.severity?o.NotificationPriority.HIGH:o.NotificationPriority.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:o.NotificationPriority.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.B.createNotification({userId:e,type:o.NotificationType.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||o.NotificationPriority.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:o.NotificationType.BROADCAST,title:e.title,message:e.message,priority:e.priority||o.NotificationPriority.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:o.NotificationType.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:o.NotificationPriority.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var n=o?Object.getOwnPropertyDescriptor(e,a):null;n&&(n.get||n.set)?Object.defineProperty(i,a,n):i[a]=e[a]}return i.default=e,t&&t.set(e,i),i}(t(45609));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,9489,5245,2125],()=>t(3921));module.exports=i})();