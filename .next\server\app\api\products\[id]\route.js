"use strict";(()=>{var e={};e.id=3898,e.ids=[3898],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8592:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>y,staticGenerationAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{DELETE:()=>l,GET:()=>c,PATCH:()=>u,PUT:()=>p});var o=r(49303),s=r(88716),d=r(60670),i=r(87070),n=r(3474);async function c(e,{params:t}){try{let e=await n._.product.findUnique({where:{id:t.id},include:{category:!0,productCategories:{include:{category:!0}},images:{orderBy:{position:"asc"}},variants:!0,reviews:{include:{user:!0},orderBy:{createdAt:"desc"}}}});if(e||(e=await n._.product.findUnique({where:{slug:t.id},include:{category:!0,productCategories:{include:{category:!0}},images:{orderBy:{position:"asc"}},variants:!0,reviews:{include:{user:!0},orderBy:{createdAt:"desc"}}}})),!e)return i.NextResponse.json({success:!1,error:"Product not found"},{status:404});return i.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching product:",e),i.NextResponse.json({success:!1,error:"Failed to fetch product"},{status:500})}}async function u(e,{params:t}){try{let{name:r,slug:a,description:o,shortDescription:s,price:d,comparePrice:c,sku:u,quantity:p,isFeatured:l,isActive:g,categoryId:h,categoryIds:w,images:y,variations:m=[]}=await e.json(),v={...r&&{name:r},...a&&{slug:a},...o&&{description:o},...s&&{shortDescription:s},...d&&{price:d},...c&&{comparePrice:c},...u&&{sku:u},...void 0!==p&&{quantity:p},...void 0!==l&&{isFeatured:l},...void 0!==g&&{isActive:g},...h&&{categoryId:h}};w&&Array.isArray(w)&&(await n._.productCategory.deleteMany({where:{productId:t.id}}),w.length>0&&(v.productCategories={create:w.map(e=>({categoryId:e}))})),void 0!==y&&(await n._.productImage.deleteMany({where:{productId:t.id}}),y.length>0&&(v.images={create:y.map((e,t)=>({url:e.url,alt:e.alt||r||"Product image",position:t}))})),void 0!==m&&(await n._.productVariant.deleteMany({where:{productId:t.id}}),m.length>0&&(v.variants={create:m.map(e=>({name:e.name,value:e.value,price:e.price||null}))}));let f=await n._.product.update({where:{id:t.id},data:v,include:{category:!0,productCategories:{include:{category:!0}},images:{orderBy:{position:"asc"}},variants:!0}});return i.NextResponse.json({success:!0,data:f,message:"Product updated successfully"})}catch(e){return console.error("Error updating product:",e),i.NextResponse.json({success:!1,error:"Failed to update product"},{status:500})}}async function p(e,{params:t}){return u(e,{params:t})}async function l(e,{params:t}){try{let e=await n._.orderItem.findMany({where:{productId:t.id},include:{order:{select:{id:!0,status:!0}}}});if(e.length>0)return await n._.product.update({where:{id:t.id},data:{isActive:!1,name:`[DELETED] ${new Date().toISOString().split("T")[0]} - `+(await n._.product.findUnique({where:{id:t.id},select:{name:!0}}))?.name}}),i.NextResponse.json({success:!0,message:"Product has been deactivated (soft deleted) because it exists in order history. It will no longer appear in the store.",type:"soft_delete",orderCount:e.length});return await n._.$transaction(async e=>{await e.productImage.deleteMany({where:{productId:t.id}}),await e.productVariant.deleteMany({where:{productId:t.id}}),await e.productCategory.deleteMany({where:{productId:t.id}}),await e.review.deleteMany({where:{productId:t.id}}),await e.productFAQ.deleteMany({where:{productId:t.id}}),await e.wishlistItem.deleteMany({where:{productId:t.id}}),await e.product.delete({where:{id:t.id}})}),i.NextResponse.json({success:!0,message:"Product deleted successfully",type:"hard_delete"})}catch(e){if(console.error("Error deleting product:",e),e instanceof Error&&e.message.includes("Foreign key constraint"))return i.NextResponse.json({success:!1,error:"Cannot delete product because it is referenced in orders or other records. The product has been deactivated instead.",type:"constraint_error"},{status:409});return i.NextResponse.json({success:!1,error:"Failed to delete product"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:y}=g,m="/api/products/[id]/route";function v(){return(0,d.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:w})}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var a=r(53524);let o=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,5972],()=>r(8592));module.exports=a})();