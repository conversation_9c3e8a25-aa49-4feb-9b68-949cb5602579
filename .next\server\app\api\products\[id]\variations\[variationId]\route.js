"use strict";(()=>{var e={};e.id=9361,e.ids=[9361],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},11500:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>m,routeModule:()=>f,serverHooks:()=>w,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{DELETE:()=>c,PUT:()=>p});var i=t(49303),s=t(88716),n=t(60670),o=t(87070),u=t(75571),l=t(95306),d=t(3474);async function p(e,{params:r}){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:a,variationId:i}=r,{name:s,value:n,price:p,pricingMode:c}=await e.json();if(!s||!n)return o.NextResponse.json({error:"Name and value are required"},{status:400});let f=c&&["REPLACE","INCREMENT","FIXED"].includes(c)?c:"INCREMENT",m=await d._.productVariant.findUnique({where:{id:i}});if(!m)return o.NextResponse.json({error:"Variation not found"},{status:404});if(m.productId!==a)return o.NextResponse.json({error:"Variation does not belong to this product"},{status:400});if(await d._.productVariant.findFirst({where:{productId:a,name:s,value:n,id:{not:i}}}))return o.NextResponse.json({error:"Another variation with this name and value already exists"},{status:400});let v=null;if(null!=p&&""!==p){let e="string"==typeof p?parseFloat(p):Number(p);!isNaN(e)&&isFinite(e)&&(v=e)}let w={name:s.trim(),value:n.trim(),price:v,pricingMode:f},x=await d._.productVariant.update({where:{id:i},data:w});return o.NextResponse.json({success:!0,data:x,message:"Variation updated successfully"})}catch(e){return console.error("Error updating variation:",e),o.NextResponse.json({success:!1,error:"Failed to update variation"},{status:500})}}async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user||"ADMIN"!==e.user.role)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t,variationId:a}=r,i=await d._.productVariant.findUnique({where:{id:a}});if(!i)return o.NextResponse.json({error:"Variation not found"},{status:404});if(i.productId!==t)return o.NextResponse.json({error:"Variation does not belong to this product"},{status:400});return await d._.productVariant.delete({where:{id:a}}),o.NextResponse.json({success:!0,message:"Variation deleted successfully"})}catch(e){return console.error("Error deleting variation:",e),o.NextResponse.json({success:!1,error:"Failed to delete variation"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/[id]/variations/[variationId]/route",pathname:"/api/products/[id]/variations/[variationId]",filename:"route",bundlePath:"app/api/products/[id]/variations/[variationId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\[variationId]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:v,serverHooks:w}=f,x="/api/products/[id]/variations/[variationId]/route";function y(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:v})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var a=t(13539),i=t(77234),s=t(53797),n=t(98691),o=t(3474);let u={adapter:(0,a.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:a}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var a=t(53524);let i=globalThis.prisma??new a.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var o=i?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(a,s,o):a[s]=e[s]}return a.default=e,t&&t.set(e,a),a}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9276,5972,8691,6575],()=>t(11500));module.exports=a})();