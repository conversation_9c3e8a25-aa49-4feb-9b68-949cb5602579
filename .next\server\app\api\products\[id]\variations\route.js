"use strict";(()=>{var e={};e.id=1135,e.ids=[1135],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},47510:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>R,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>y,staticGenerationAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>m});var a=t(49303),i=t(88716),n=t(60670),o=t(87070),u=t(75571),c=t(95306),l=t(3474),d=t(84875),p=t(54211);let f=(0,d.lm)(async(e,{params:r})=>{let t=r.id;p.kg.apiRequest("GET",`/api/products/${t}/variations`);let s=await l._.productVariant.findMany({where:{productId:t},orderBy:[{name:"asc"},{value:"asc"}]});return p.kg.info("Product variations fetched",{productId:t,count:s.length}),o.NextResponse.json({success:!0,data:s})}),m=(0,d.lm)(async(e,{params:r})=>{let t=r.id;p.kg.apiRequest("POST",`/api/products/${t}/variations`);let s=await (0,u.getServerSession)(c.L);if(!s?.user)throw new d._7;if("ADMIN"!==s.user.role)throw new d.M_;let{name:a,value:i,price:n,pricingMode:f}=await e.json();if(!a||!i)throw new d.p8("Name and value are required");let m=f&&["REPLACE","INCREMENT","FIXED"].includes(f)?f:"REPLACE";if(!await l._.product.findUnique({where:{id:t}}))throw new d.dR("Product");if(await l._.productVariant.findFirst({where:{productId:t,name:a,value:i}}))throw new d.AY("Variation with this name and value already exists");let h=null;if(null!=n&&""!==n){let e="string"==typeof n?parseFloat(n):Number(n);!isNaN(e)&&isFinite(e)&&(h=e)}let g=await l._.productVariant.create({data:{name:a.trim(),value:i.trim(),price:h,pricingMode:m,productId:t}});return p.kg.info("Product variation created",{productId:t,variationId:g.id,name:a,value:i,userId:s.user.id}),o.NextResponse.json({success:!0,data:g,message:"Variation created successfully"})}),h=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/products/[id]/variations/route",pathname:"/api/products/[id]/variations",filename:"route",bundlePath:"app/api/products/[id]/variations/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:y}=h,v="/api/products/[id]/variations/route";function R(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:w})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),a=t(77234),i=t(53797),n=t(98691),o=t(3474);let u={adapter:(0,s.N)(o._),providers:[(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})},84875:(e,r,t)=>{t.d(r,{AY:()=>l,M_:()=>u,_7:()=>o,dR:()=>c,gz:()=>i,lm:()=>p,p8:()=>n});var s=t(87070),a=t(29489);class i extends Error{constructor(e,r=500,t="INTERNAL_ERROR",s){super(e),this.statusCode=r,this.code=t,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,i)}}class n extends i{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class o extends i{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class u extends i{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends i{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends i{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class d extends i{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function p(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){if(e instanceof i)return s.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof a.j){let r=new n("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return s.NextResponse.json({success:!1,error:{code:r.code,message:r.message,details:r.details}},{status:r.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let r=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new l(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new o("Invalid user session");return new n("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new n("Missing required relationship");case"P2000":return new n("Input value is too long");case"P2004":return new n("Data constraint violation");default:return new d("Database operation failed",{code:e.code,message:e.message})}}(e);return s.NextResponse.json({success:!1,error:{code:r.code,message:r.message,...r.details&&{details:r.details}}},{status:r.statusCode})}return e instanceof Error&&e.message,s.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},54211:(e,r,t)=>{var s;t.d(r,{kg:()=>i}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class a{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:r,level:t,message:a,context:i,error:n,userId:o,requestId:u}=e,c=s[t],l=`[${r}] ${c}: ${a}`;return o&&(l+=` | User: ${o}`),u&&(l+=` | Request: ${u}`),i&&Object.keys(i).length>0&&(l+=` | Context: ${JSON.stringify(i)}`),n&&(l+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(l+=`
Stack: ${n.stack}`)),l}log(e,r,t,s){if(!this.shouldLog(e))return;let a={timestamp:new Date().toISOString(),level:e,message:r,context:t,error:s},i=this.formatMessage(a);if(this.isDevelopment)switch(e){case 0:console.error(i);break;case 1:console.warn(i);break;case 2:console.info(i);break;case 3:console.debug(i)}else console.log(JSON.stringify(a))}error(e,r,t){this.log(0,e,t,r)}warn(e,r){this.log(1,e,r)}info(e,r){this.log(2,e,r)}debug(e,r){this.log(3,e,r)}apiRequest(e,r,t,s){this.info(`API ${e} ${r}`,{...s,userId:t,type:"api_request"})}apiResponse(e,r,t,s,a){this.info(`API ${e} ${r} - ${t}`,{...a,statusCode:t,duration:s,type:"api_response"})}apiError(e,r,t,s,a){this.error(`API ${e} ${r} failed`,t,{...a,userId:s,type:"api_error"})}authSuccess(e,r,t){this.info("Authentication successful",{...t,userId:e,method:r,type:"auth_success"})}authFailure(e,r,t,s){this.warn("Authentication failed",{...s,email:e,method:r,reason:t,type:"auth_failure"})}dbQuery(e,r,t,s){this.debug(`DB ${e} on ${r}`,{...s,operation:e,table:r,duration:t,type:"db_query"})}dbError(e,r,t,s){this.error(`DB ${e} on ${r} failed`,t,{...s,operation:e,table:r,type:"db_error"})}securityEvent(e,r,t){this.log("high"===r?0:"medium"===r?1:2,`Security event: ${e}`,{...t,severity:r,type:"security_event"})}rateLimitHit(e,r,t,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:r,window:t,type:"rate_limit"})}emailSent(e,r,t,s){this.info("Email sent",{...s,to:e,subject:r,template:t,type:"email_sent"})}emailError(e,r,t,s){this.error("Email failed to send",t,{...s,to:e,subject:r,type:"email_error"})}performance(e,r,t){this.log(r>5e3?1:3,`Performance: ${e} took ${r}ms`,{...t,operation:e,duration:r,type:"performance"})}}let i=new a},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575,9489],()=>t(47510));module.exports=s})();