"use strict";(()=>{var e={};e.id=3568,e.ids=[3568],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48080:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>v,requestAsyncStorage:()=>f,routeModule:()=>w,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{POST:()=>c});var s=r(49303),o=r(88716),u=r(60670),d=r(87070),n=r(3474);async function c(e){try{let{action:t,productIds:r}=await e.json();if(!t||!r||!Array.isArray(r)||0===r.length)return d.NextResponse.json({success:!1,error:"Invalid request. Action and productIds array are required."},{status:400});switch(t){case"delete":return await i(r);case"activate":return await p(r,!0);case"deactivate":return await p(r,!1);case"feature":return await l(r,!0);case"unfeature":return await l(r,!1);default:return d.NextResponse.json({success:!1,error:"Invalid action. Supported actions: delete, activate, deactivate, feature, unfeature"},{status:400})}}catch(e){return console.error("Error in bulk operation:",e),d.NextResponse.json({success:!1,error:"Failed to perform bulk operation"},{status:500})}}async function i(e){let t={success:0,softDeleted:0,failed:0,errors:[]};for(let r of e)try{if((await n._.orderItem.findMany({where:{productId:r},include:{order:{select:{id:!0,status:!0}}}})).length>0){let e=await n._.product.findUnique({where:{id:r},select:{name:!0}});e&&(await n._.product.update({where:{id:r},data:{isActive:!1,name:`[DELETED] ${new Date().toISOString().split("T")[0]} - ${e.name}`}}),t.softDeleted++)}else await n._.$transaction(async e=>{await e.productImage.deleteMany({where:{productId:r}}),await e.productVariant.deleteMany({where:{productId:r}}),await e.productCategory.deleteMany({where:{productId:r}}),await e.review.deleteMany({where:{productId:r}}),await e.productFAQ.deleteMany({where:{productId:r}}),await e.wishlistItem.deleteMany({where:{productId:r}}),await e.product.delete({where:{id:r}})}),t.success++}catch(e){console.error(`Error deleting product ${r}:`,e),t.failed++,t.errors.push(`Failed to delete product ${r}: ${e instanceof Error?e.message:"Unknown error"}`)}let r="Bulk delete completed. ";return t.success>0&&(r+=`${t.success} products deleted permanently. `),t.softDeleted>0&&(r+=`${t.softDeleted} products deactivated (had order history). `),t.failed>0&&(r+=`${t.failed} products failed to delete.`),d.NextResponse.json({success:!0,message:r.trim(),data:t})}async function p(e,t){try{let r=await n._.product.updateMany({where:{id:{in:e}},data:{isActive:t}});return d.NextResponse.json({success:!0,message:`${r.count} products ${t?"activated":"deactivated"} successfully`,data:{updated:r.count}})}catch(e){return console.error("Error in bulk activate/deactivate:",e),d.NextResponse.json({success:!1,error:`Failed to ${t?"activate":"deactivate"} products`},{status:500})}}async function l(e,t){try{let r=await n._.product.updateMany({where:{id:{in:e}},data:{isFeatured:t}});return d.NextResponse.json({success:!0,message:`${r.count} products ${t?"featured":"unfeatured"} successfully`,data:{updated:r.count}})}catch(e){return console.error("Error in bulk feature/unfeature:",e),d.NextResponse.json({success:!1,error:`Failed to ${t?"feature":"unfeature"} products`},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/products/bulk/route",pathname:"/api/products/bulk",filename:"route",bundlePath:"app/api/products/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\bulk\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:y}=w,m="/api/products/bulk/route";function v(){return(0,u.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},3474:(e,t,r)=>{r.d(t,{_:()=>s});var a=r(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,5972],()=>r(48080));module.exports=a})();