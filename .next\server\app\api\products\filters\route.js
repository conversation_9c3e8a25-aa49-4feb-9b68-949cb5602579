"use strict";(()=>{var e={};e.id=2330,e.ids=[2330],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19730:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>R,requestAsyncStorage:()=>d,routeModule:()=>n,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var i={};t.r(i),t.d(i,{GET:()=>u});var o=t(49303),s=t(88716),c=t(60670),a=t(87070),p=t(3474);async function u(e){try{let{searchParams:r}=new URL(e.url),t=r.get("category"),i={isActive:!0};t&&(i.OR=[{category:{slug:t}},{productCategories:{some:{category:{slug:t}}}}]);let[o,s,c]=await Promise.all([p._.product.aggregate({where:i,_min:{price:!0},_max:{price:!0}}),p._.$queryRaw`
        SELECT 
          c.id::text as id, 
          c.name, 
          c.slug, 
          c.description,
          COUNT(DISTINCT p.id)::int as product_count
        FROM "categories" c
        LEFT JOIN "products" p ON (
          p."categoryId" = c.id OR 
          EXISTS (
            SELECT 1 FROM "product_categories" pc 
            WHERE pc."categoryId" = c.id AND pc."productId" = p.id
          )
        ) AND p."isActive" = true
        WHERE c."isActive" = true
        GROUP BY c.id, c.name, c.slug, c.description
        ORDER BY c.name ASC
      `,p._.product.count({where:i})]),u=s.map(e=>({id:e.id,name:e.name,slug:e.slug,description:e.description,product_count:e.product_count}));return a.NextResponse.json({success:!0,data:{priceRange:{min:Number(o._min.price)||0,max:Number(o._max.price)||1e3},categories:u,totalProducts:Number(c)}})}catch(e){return console.error("Error fetching filter data:",e),a.NextResponse.json({success:!1,error:"Failed to fetch filter data"},{status:500})}}let n=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/filters/route",pathname:"/api/products/filters",filename:"route",bundlePath:"app/api/products/filters/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\filters\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g}=n,m="/api/products/filters/route";function R(){return(0,c.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}},3474:(e,r,t)=>{t.d(r,{_:()=>o});var i=t(53524);let o=globalThis.prisma??new i.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972],()=>t(19730));module.exports=i})();