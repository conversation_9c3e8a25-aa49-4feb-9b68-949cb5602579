"use strict";(()=>{var e={};e.id=5436,e.ids=[5436],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},23038:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>u});var a=t(49303),o=t(88716),i=t(60670),c=t(87070),n=t(3474);async function u(e){try{let{products:r}=await e.json();if(!r||!Array.isArray(r))return c.NextResponse.json({success:!1,error:"Invalid products data"},{status:400});let t={success:0,failed:0,errors:[]},s=await n._.category.findMany({where:{isActive:!0}}),a=new Map(s.map(e=>[e.name.toLowerCase(),e.id]));for(let e of(s.forEach(e=>{let r=e.name.toLowerCase();"skincare"===r?(a.set("skin care",e.id),a.set("skin",e.id)):"hair care"===r?(a.set("haircare",e.id),a.set("hair",e.id)):"body care"===r&&(a.set("bodycare",e.id),a.set("body",e.id))}),r))try{let{name:r,slug:s,description:o,shortDescription:i,price:c,comparePrice:u,category:p,categoryNames:d=[],isFeatured:l,isActive:g,variations:h=[]}=e;if(!r){t.failed++,t.errors.push("Product missing required name field");continue}let m=h;if("string"==typeof h)try{m=JSON.parse(h)}catch(e){m=[]}Array.isArray(m)||(m=[]);let f=c?parseFloat(c.toString()):null;if((!f||0===f)&&m&&m.length>0)f=0;else if(!f&&0!==f){if(m&&0!==m.length)f=0;else{t.failed++,t.errors.push(`Product "${r}" missing required price field or variations with pricing`);continue}}let y=[];p&&y.push(p),d&&d.length>0&&y.push(...d);let v=[];for(let e of y){let s=a.get(e.toLowerCase());!s&&(e.toLowerCase().includes("skin")?s=a.get("skincare"):e.toLowerCase().includes("hair")?s=a.get("hair care"):e.toLowerCase().includes("body")&&(s=a.get("body care"))),s?v.push(s):t.errors.push(`Category "${e}" not found for product "${r}"`)}if(0===v.length){t.failed++,t.errors.push(`Product "${r}" has no valid categories`);continue}let w=s||r.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"");if(await n._.product.findUnique({where:{slug:w}})){t.failed++,t.errors.push(`Product with slug "${w}" already exists`);continue}await n._.product.create({data:{name:r,slug:w,description:o||"",shortDescription:i||"",price:f,comparePrice:u?parseFloat(u.toString()):null,categoryId:v[0],isFeatured:!!l,isActive:!1!==g,variants:m.length>0?{create:m.map(e=>({name:e.name,value:e.value,price:e.price?parseFloat(e.price.toString()):null}))}:void 0,productCategories:{create:v.map(e=>({categoryId:e}))}},include:{category:!0,productCategories:{include:{category:!0}},variants:!0}}),t.success++}catch(r){t.failed++,t.errors.push(`Failed to create product "${e.name}": ${r}`),console.error("Error creating product:",r)}return c.NextResponse.json({success:!0,data:t,message:`Import completed: ${t.success} successful, ${t.failed} failed`})}catch(e){return console.error("Error importing products:",e),c.NextResponse.json({success:!1,error:"Failed to import products"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/products/import/route",pathname:"/api/products/import",filename:"route",bundlePath:"app/api/products/import/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\import\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g}=p,h="/api/products/import/route";function m(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(23038));module.exports=s})();