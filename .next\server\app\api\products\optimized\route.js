"use strict";(()=>{var e={};e.id=9754,e.ids=[9754],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55347:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>v,requestAsyncStorage:()=>u,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var a={};r.r(a),r.d(a,{GET:()=>p});var s=r(49303),i=r(88716),o=r(60670),c=r(87070),n=r(3474);async function p(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),s=t.get("category"),i=t.get("search"),o=t.get("sort")||"newest",p=t.get("priceMin"),d=t.get("priceMax"),u=(r-1)*a,l={isActive:!0};if(s&&"all"!==s&&(l.OR=[{category:{slug:s}},{productCategories:{some:{category:{slug:s}}}}]),i){let e=[{name:{contains:i,mode:"insensitive"}},{shortDescription:{contains:i,mode:"insensitive"}}];l.OR?(l.AND=[{OR:l.OR},{OR:e}],delete l.OR):l.OR=e}(p||d)&&(l.price={},p&&(l.price.gte=parseFloat(p)),d&&(l.price.lte=parseFloat(d)));let g={createdAt:"desc"};switch(o){case"name_asc":g={name:"asc"};break;case"name_desc":g={name:"desc"};break;case"price_asc":g={price:"asc"};break;case"price_desc":g={price:"desc"};break;case"newest":g={createdAt:"desc"};break;case"oldest":g={createdAt:"asc"}}let[m,v]=await Promise.all([n._.product.findMany({where:l,include:{category:!0,productCategories:{include:{category:!0}},images:{orderBy:{position:"asc"},take:1},variants:{orderBy:{price:"asc"},take:3},_count:{select:{reviews:!0}}},orderBy:g,skip:u,take:a}),n._.product.count({where:l})]),h=m.map(e=>({id:e.id,name:e.name,slug:e.slug,shortDescription:e.shortDescription,price:e.price,comparePrice:e.comparePrice,isFeatured:e.isFeatured,createdAt:e.createdAt,category:e.category,categories:[e.category,...e.productCategories.map(e=>e.category)].filter(Boolean),image:e.images[0]||null,reviewCount:e._count.reviews,variants:e.variants}));return c.NextResponse.json({success:!0,data:h,pagination:{page:r,limit:a,total:v,pages:Math.ceil(v/a)}})}catch(e){return console.error("Error fetching products:",e),c.NextResponse.json({success:!1,error:"Failed to fetch products"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/products/optimized/route",pathname:"/api/products/optimized",filename:"route",bundlePath:"app/api/products/optimized/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\optimized\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:l,serverHooks:g}=d,m="/api/products/optimized/route";function v(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}},3474:(e,t,r)=>{r.d(t,{_:()=>s});var a=r(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,5972],()=>r(55347));module.exports=a})();