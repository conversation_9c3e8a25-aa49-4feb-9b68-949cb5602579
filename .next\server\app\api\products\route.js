"use strict";(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4926:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>v,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>u,POST:()=>p});var s=t(49303),o=t(88716),i=t(60670),c=t(87070),n=t(3474);async function u(e){try{let r,t,a;let{searchParams:s}=new URL(e.url),o=parseInt(s.get("page")||"1"),i=parseInt(s.get("limit")||"10"),u=s.get("category"),p=s.get("search"),d=s.get("sort")||"random",l=(o-1)*i,g={isActive:!0},m=[];switch(u&&m.push({OR:[{category:{slug:u}},{productCategories:{some:{category:{slug:u}}}}]}),p&&m.push({OR:[{name:{contains:p,mode:"insensitive"}},{description:{contains:p,mode:"insensitive"}}]}),m.length>0&&(g.AND=m),d){case"name_asc":r={name:"asc"};break;case"name_desc":r={name:"desc"};break;case"price_asc":r={price:"asc"};break;case"price_desc":r={price:"desc"};break;case"newest":r={createdAt:"desc"};break;case"oldest":r={createdAt:"asc"};break;default:r=void 0}if("random"===d){a=await n._.product.count({where:g});let e=[...await n._.product.findMany({where:g,include:{category:!0,productCategories:{include:{category:!0}},images:!0,variants:!0,_count:{select:{reviews:!0}}}})];for(let r=e.length-1;r>0;r--){let t=Math.floor(Math.random()*(r+1));[e[r],e[t]]=[e[t],e[r]]}t=e.slice(l,l+i)}else[t,a]=await Promise.all([n._.product.findMany({where:g,include:{category:!0,productCategories:{include:{category:!0}},images:!0,variants:!0,_count:{select:{reviews:!0}}},orderBy:r,skip:l,take:i}),n._.product.count({where:g})]);return c.NextResponse.json({success:!0,data:t,pagination:{page:o,limit:i,total:a,pages:Math.ceil(a/i)}})}catch(e){return console.error("Error fetching products:",e),c.NextResponse.json({success:!1,error:"Failed to fetch products"},{status:500})}}async function p(e){try{let{name:r,slug:t,description:a,shortDescription:s,price:o,comparePrice:i,categoryId:u,categoryIds:p=[],images:d,isFeatured:l,variations:g=[]}=await e.json();if(!r)return c.NextResponse.json({success:!1,error:"Product name is required"},{status:400});let m=void 0!==o?parseFloat(o.toString()):null,h=null!==m&&m>=0,v=g&&g.length>0,y=[];if(!h&&!v)return c.NextResponse.json({success:!1,error:"Product must have either a base price (can be 0) or variations with pricing"},{status:400});0!==m||g&&0!==g.length||y.push("Product has zero base price and no variations. Consider adding variations for pricing."),0===m&&g&&g.length>0&&g.some(e=>!e.price||0===e.price)&&y.push("Some variations have zero price. Ensure all variations have valid pricing."),null===m&&(m=0);let f=p.length>0?p:u?[u]:[],x=await n._.product.create({data:{name:r,slug:t,description:a,shortDescription:s,price:m,comparePrice:i?parseFloat(i.toString()):null,categoryId:u,isFeatured:!!l,images:d?{create:d.map((e,t)=>({url:e.url,alt:e.alt||r,position:t}))}:void 0,variants:g.length>0?{create:g.map(e=>({name:e.name,value:e.value,price:e.price||null,pricingMode:e.pricingMode||"INCREMENT"}))}:void 0,productCategories:f.length>0?{create:f.map(e=>({categoryId:e}))}:void 0},include:{category:!0,productCategories:{include:{category:!0}},images:!0,variants:!0}});return c.NextResponse.json({success:!0,data:x,message:"Product created successfully",warnings:y.length>0?y:void 0})}catch(e){return console.error("Error creating product:",e),c.NextResponse.json({success:!1,error:"Failed to create product"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:m}=d,h="/api/products/route";function v(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},3474:(e,r,t)=>{t.d(r,{_:()=>s});var a=t(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9276,5972],()=>t(4926));module.exports=a})();