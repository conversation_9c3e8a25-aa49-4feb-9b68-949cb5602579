"use strict";(()=>{var e={};e.id=9407,e.ids=[9407],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},73571:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>x,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{DELETE:()=>p,PUT:()=>c});var i=s(49303),a=s(88716),o=s(60670),n=s(87070),l=s(45609),u=s(95306),d=s(3474);async function c(e,{params:r}){try{let s=await (0,l.getServerSession)(u.L);if(!s?.user?.email)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=await d._.user.findUnique({where:{email:s.user.email}});if(!t||"ADMIN"!==t.role)return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{name:i,content:a,rating:o,image:c,position:p,company:m,isActive:x,order:w}=await e.json(),g=await d._.testimonial.update({where:{id:r.id},data:{...void 0!==i&&{name:i},...void 0!==a&&{content:a},...void 0!==o&&{rating:o},...void 0!==c&&{image:c},...void 0!==p&&{position:p},...void 0!==m&&{company:m},...void 0!==x&&{isActive:x},...void 0!==w&&{order:w}}});return n.NextResponse.json({success:!0,data:g,message:"Testimonial updated successfully"})}catch(e){return console.error("Error updating testimonial:",e),n.NextResponse.json({success:!1,error:"Failed to update testimonial"},{status:500})}}async function p(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user?.email)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=await d._.user.findUnique({where:{email:e.user.email}});if(!s||"ADMIN"!==s.role)return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});return await d._.testimonial.delete({where:{id:r.id}}),n.NextResponse.json({success:!0,message:"Testimonial deleted successfully"})}catch(e){return console.error("Error deleting testimonial:",e),n.NextResponse.json({success:!1,error:"Failed to delete testimonial"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/testimonials/[id]/route",pathname:"/api/testimonials/[id]",filename:"route",bundlePath:"app/api/testimonials/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:x,staticGenerationAsyncStorage:w,serverHooks:g}=m,v="/api/testimonials/[id]/route";function h(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:w})}},95306:(e,r,s)=>{s.d(r,{L:()=>l});var t=s(13539),i=s(77234),a=s(53797),o=s(98691),n=s(3474);let l={adapter:(0,t.N)(n._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await n._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await o.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:s}){if(r&&(e.sub=r.id,e.role=r.role),s&&e.email)try{let r=await n._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let s=await n._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(s)return{...e,user:{...e.user,id:s.id,role:s.role,email:s.email,name:s.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:s,isNewUser:t}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,s)=>{s.d(r,{_:()=>i});var t=s(53524);let i=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,5972,8691,6575],()=>s(73571));module.exports=t})();