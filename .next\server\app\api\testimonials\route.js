"use strict";(()=>{var e={};e.id=5382,e.ids=[5382],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},18509:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>q,requestAsyncStorage:()=>x,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var i=t(49303),a=t(88716),n=t(60670),o=t(87070),l=t(45609),u=t(95306),c=t(3474);async function p(e){try{let{searchParams:r}=new URL(e.url),t="true"===r.get("active"),s=await c._.testimonial.findMany({where:t?{isActive:!0}:{},orderBy:[{order:"asc"},{createdAt:"desc"}]});return o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error fetching testimonials:",e),o.NextResponse.json({success:!1,error:"Failed to fetch testimonials"},{status:500})}}async function d(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user?.email)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=await c._.user.findUnique({where:{email:r.user.email}});if(!t||"ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{name:s,content:i,rating:a,image:n,position:p,company:d,isActive:m,order:x}=await e.json();if(!s||!i)return o.NextResponse.json({success:!1,error:"Name and content are required"},{status:400});let g=await c._.testimonial.create({data:{name:s,content:i,rating:a||5,image:n,position:p,company:d,isActive:void 0===m||m,order:x||0}});return o.NextResponse.json({success:!0,data:g,message:"Testimonial created successfully"})}catch(e){return console.error("Error creating testimonial:",e),o.NextResponse.json({success:!1,error:"Failed to create testimonial"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/testimonials/route",pathname:"/api/testimonials",filename:"route",bundlePath:"app/api/testimonials/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:g,serverHooks:w}=m,h="/api/testimonials/route";function q(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}},95306:(e,r,t)=>{t.d(r,{L:()=>l});var s=t(13539),i=t(77234),a=t(53797),n=t(98691),o=t(3474);let l={adapter:(0,s.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(18509));module.exports=s})();