"use strict";(()=>{var e={};e.id=5757,e.ids=[5757],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},13719:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>w,patchFetch:()=>y,requestAsyncStorage:()=>v,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>l,PATCH:()=>p});var i=t(49303),o=t(88716),n=t(60670),a=t(87070),u=t(3474),d=t(75571),c=t(95306);async function l(e,{params:r}){try{let e=await (0,d.getServerSession)(c.L);if(!e?.user)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=e.user?.role==="ADMIN",s=e.user.id===r.id;if(!t&&!s)return a.NextResponse.json({success:!1,error:"Forbidden"},{status:403});let i=await u._.userPreference.findUnique({where:{userId:r.id}});return i||(i=await u._.userPreference.create({data:{userId:r.id}})),a.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Error fetching user preferences:",e),a.NextResponse.json({success:!1,error:"Failed to fetch user preferences"},{status:500})}}async function p(e,{params:r}){try{let t=await (0,d.getServerSession)(c.L);if(!t?.user)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=t.user?.role==="ADMIN",i=t.user.id===r.id;if(!s&&!i)return a.NextResponse.json({success:!1,error:"Forbidden"},{status:403});let{language:o,theme:n,orderUpdates:l,promotions:p,newsletter:f,smsNotifications:v,emailNotifications:m,inAppNotifications:g,orderNotifications:w,wishlistNotifications:y,reviewNotifications:x,priceDropAlerts:h,adminMessages:b,broadcastMessages:j}=await e.json(),_=await u._.userPreference.upsert({where:{userId:r.id},update:{...void 0!==o&&{language:o},...void 0!==n&&{theme:n},...void 0!==l&&{orderUpdates:l},...void 0!==p&&{promotions:p},...void 0!==f&&{newsletter:f},...void 0!==v&&{smsNotifications:v},...void 0!==m&&{emailNotifications:m},...void 0!==g&&{inAppNotifications:g},...void 0!==w&&{orderNotifications:w},...void 0!==y&&{wishlistNotifications:y},...void 0!==x&&{reviewNotifications:x},...void 0!==h&&{priceDropAlerts:h},...void 0!==b&&{adminMessages:b},...void 0!==j&&{broadcastMessages:j}},create:{userId:r.id,language:o||"en-US",theme:n||"light",orderUpdates:void 0===l||l,promotions:void 0!==p&&p,newsletter:void 0===f||f,smsNotifications:void 0!==v&&v,emailNotifications:void 0===m||m,inAppNotifications:void 0===g||g,orderNotifications:void 0===w||w,wishlistNotifications:void 0===y||y,reviewNotifications:void 0===x||x,priceDropAlerts:void 0!==h&&h,adminMessages:void 0===b||b,broadcastMessages:void 0===j||j}});return a.NextResponse.json({success:!0,data:_,message:"Preferences updated successfully"})}catch(e){return console.error("Error updating user preferences:",e),a.NextResponse.json({success:!1,error:"Failed to update user preferences"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/users/[id]/preferences/route",pathname:"/api/users/[id]/preferences",filename:"route",bundlePath:"app/api/users/[id]/preferences/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\preferences\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:m,serverHooks:g}=f,w="/api/users/[id]/preferences/route";function y(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),i=t(77234),o=t(53797),n=t(98691),a=t(3474);let u={adapter:(0,s.N)(a._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await a._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await a._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(13719));module.exports=s})();