"use strict";(()=>{var e={};e.id=1380,e.ids=[1380],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},6810:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>w,patchFetch:()=>g,requestAsyncStorage:()=>x,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var t={};s.r(t),s.d(t,{GET:()=>p,PATCH:()=>c});var i=s(49303),a=s(88716),n=s(60670),o=s(87070),u=s(45609),l=s(95306),d=s(3474);async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=r.id;if(e.user.id!==s&&"ADMIN"!==e.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let t=await d._.user.findUnique({where:{id:s},select:{id:!0,name:!0,email:!0,phone:!0,role:!0,createdAt:!0,addresses:{select:{id:!0,firstName:!0,lastName:!0,company:!0,address1:!0,address2:!0,city:!0,state:!0,postalCode:!0,country:!0,phone:!0,isDefault:!0},orderBy:{isDefault:"desc"}}}});if(!t)return o.NextResponse.json({error:"User not found"},{status:404});return o.NextResponse.json({success:!0,data:t})}catch(e){return console.error("Error fetching user:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,{params:r}){try{let s=await (0,u.getServerSession)(l.L);if(!s?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=r.id;if(s.user.id!==t&&"ADMIN"!==s.user.role)return o.NextResponse.json({error:"Forbidden"},{status:403});let{name:i,email:a,phone:n}=await e.json();if(!i?.trim())return o.NextResponse.json({error:"Name is required"},{status:400});if(!a?.trim())return o.NextResponse.json({error:"Email is required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))return o.NextResponse.json({error:"Invalid email format"},{status:400});if(await d._.user.findFirst({where:{email:a,NOT:{id:t}}}))return o.NextResponse.json({error:"Email is already taken"},{status:400});if(n&&n.trim()&&!/^\+?[\d\s\-\(\)]+$/.test(n))return o.NextResponse.json({error:"Invalid phone number format"},{status:400});let p=await d._.user.update({where:{id:t},data:{name:i.trim(),email:a.trim(),phone:n?.trim()||null},select:{id:!0,name:!0,email:!0,phone:!0,role:!0,createdAt:!0}});return o.NextResponse.json({success:!0,data:p,message:"Profile updated successfully"})}catch(e){return console.error("Error updating user:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:x,staticGenerationAsyncStorage:f,serverHooks:h}=m,w="/api/users/[id]/route";function g(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},95306:(e,r,s)=>{s.d(r,{L:()=>u});var t=s(13539),i=s(77234),a=s(53797),n=s(98691),o=s(3474);let u={adapter:(0,t.N)(o._),providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:s}){if(r&&(e.sub=r.id,e.role=r.role),s&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let s=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(s)return{...e,user:{...e.user,id:s.id,role:s.role,email:s.email,name:s.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:s,isNewUser:t}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,s)=>{s.d(r,{_:()=>i});var t=s(53524);let i=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,5972,8691,6575],()=>s(6810));module.exports=t})();