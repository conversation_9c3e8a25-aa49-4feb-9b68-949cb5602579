"use strict";(()=>{var e={};e.id=9424,e.ids=[9424],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},54737:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>h,requestAsyncStorage:()=>f,routeModule:()=>p,serverHooks:()=>w,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(49303),i=t(88716),n=t(60670),o=t(87070),u=t(3474),l=t(75571),d=t(95306);async function c(e,{params:r}){try{let e=await (0,l.getServerSession)(d.L);if(!e?.user)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=e.user?.role==="ADMIN",s=e.user.id===r.id;if(!t&&!s)return o.NextResponse.json({success:!1,error:"Forbidden"},{status:403});let a=await u._.user.findUnique({where:{id:r.id},select:{id:!0,name:!0,email:!0,createdAt:!0,role:!0}});if(!a)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let i=await u._.order.aggregate({where:{userId:r.id,paymentStatus:"PAID"},_count:{id:!0},_sum:{total:!0}}),[n,c,p]=await Promise.all([u._.review.count({where:{userId:r.id}}),u._.wishlistItem.count({where:{userId:r.id}}),u._.address.count({where:{userId:r.id}})]),f=await u._.order.findMany({where:{userId:r.id},orderBy:{createdAt:"desc"},take:5,select:{id:!0,orderNumber:!0,status:!0,total:!0,createdAt:!0}}),m={user:{id:a.id,name:a.name,email:a.email,joinDate:a.createdAt,role:a.role},orders:{total:i._count.id||0,totalSpent:i._sum.total||0,recent:f},counts:{reviews:n,wishlist:c,addresses:p},accountStatus:"ACTIVE"};return o.NextResponse.json({success:!0,data:m})}catch(e){return console.error("Error fetching user stats:",e),o.NextResponse.json({success:!1,error:"Failed to fetch user statistics"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/users/[id]/stats/route",pathname:"/api/users/[id]/stats",filename:"route",bundlePath:"app/api/users/[id]/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\stats\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:w}=p,y="/api/users/[id]/stats/route";function h(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:m})}},95306:(e,r,t)=>{t.d(r,{L:()=>u});var s=t(13539),a=t(77234),i=t(53797),n=t(98691),o=t(3474);let u={adapter:(0,s.N)(o._),providers:[(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.password||!await n.qu(e.password,r.password))throw Error("Invalid credentials");return{id:r.id,email:r.email,name:r.name,role:r.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.sub=r.id,e.role=r.role),t&&e.email)try{let r=await o._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});r&&(e.sub=r.id,e.role=r.role)}catch(e){}return e},async session({session:e,token:r}){if(r.email)try{let t=await o._.user.findUnique({where:{email:r.email},select:{id:!0,role:!0,email:!0,name:!0}});if(t)return{...e,user:{...e.user,id:t.id,role:t.role,email:t.email,name:t.name}}}catch(e){}return e.user&&r.sub?{...e,user:{...e.user,id:r.sub,role:r.role}}:e},redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r},events:{async signIn({user:e,account:r,profile:t,isNewUser:s}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,r,t)=>{t.d(r,{_:()=>a});var s=t(53524);let a=globalThis.prisma??new s.PrismaClient({log:["error"]})},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,6575],()=>t(54737));module.exports=s})();