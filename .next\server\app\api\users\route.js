"use strict";(()=>{var e={};e.id=5701,e.ids=[5701],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27273:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>p});var a=s(49303),n=s(88716),o=s(60670),i=s(87070),u=s(3474);async function c(e){try{let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),t=parseInt(r.get("limit")||"10"),a=r.get("role"),n=r.get("search"),o=(s-1)*t,c={};a&&(c.role=a),n&&(c.OR=[{name:{contains:n,mode:"insensitive"}},{email:{contains:n,mode:"insensitive"}}]);let[p,l]=await Promise.all([u._.user.findMany({where:c,skip:o,take:t,orderBy:{createdAt:"desc"},include:{_count:{select:{orders:!0}}}}),u._.user.count({where:c})]);return i.NextResponse.json({success:!0,data:p,pagination:{page:s,limit:t,total:l,pages:Math.ceil(l/t)}})}catch(e){return console.error("Error fetching users:",e),i.NextResponse.json({success:!1,error:"Failed to fetch users"},{status:500})}}async function p(e){try{let{name:r,email:s,phone:t,role:a="CUSTOMER"}=await e.json(),n=await u._.user.create({data:{name:r,email:s,phone:t,role:a},include:{_count:{select:{orders:!0}}}});return i.NextResponse.json({success:!0,data:n,message:"User created successfully"})}catch(e){return console.error("Error creating user:",e),i.NextResponse.json({success:!1,error:"Failed to create user"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:d,staticGenerationAsyncStorage:m,serverHooks:g}=l,h="/api/users/route";function x(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},3474:(e,r,s)=>{s.d(r,{_:()=>a});var t=s(53524);let a=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,5972],()=>s(27273));module.exports=t})();