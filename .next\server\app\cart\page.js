(()=>{var e={};e.id=5311,e.ids=[5311],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13266:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d}),t(25176),t(36944),t(35866);var a=t(23191),r=t(88716),l=t(37922),n=t.n(l),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25176)),"C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx"],x="/cart/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},67729:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,60405))},60405:(e,s,t)=>{"use strict";t.d(s,{default:()=>Z});var a=t(10326),r=t(17577),l=t(90434),n=t(46226),i=t(34565),c=t(24230),d=t(98091),o=t(76557);let x=(0,o.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var m=t(83855),p=t(94494),h=t(77109);let u=(0,o.Z)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var g=t(71821);let y=(0,o.Z)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var j=t(40765),f=t(1572),b=t(54659),N=t(94019),v=t(87888),w=t(48998),k=t(43810);let C=({cartItems:e,subtotal:s,userId:t,onCouponApply:l,onCouponRemove:n,appliedCoupons:i})=>{let[c,d]=(0,r.useState)([]),[o,x]=(0,r.useState)([]),[m,p]=(0,r.useState)(""),[h,C]=(0,r.useState)(!1),[Z,E]=(0,r.useState)(""),[P,M]=(0,r.useState)(!1),[S,A]=(0,r.useState)(!0);(0,r.useEffect)(()=>{U(),T()},[e,t]);let U=async()=>{try{A(!0);let e=new URLSearchParams({active:"true",limit:"20"});t&&e.append("userId",t);let s=await fetch(`/api/coupons?${e}`);if(s.ok){let e=await s.json(),t=I(e.coupons,!0);d(t)}}catch(e){console.error("Error fetching coupons:",e)}finally{A(!1)}},T=async()=>{try{let e=new URLSearchParams({active:"true",showInModule:"true",limit:"10"});t&&e.append("userId",t);let s=await fetch(`/api/coupons?${e}`);if(s.ok){let e=await s.json(),t=I(e.coupons,!1);x(t)}}catch(e){console.error("Error fetching featured coupons:",e)}},I=(t,a=!1)=>t.filter(t=>{if(i.some(e=>e.coupon.id===t.id)||a&&t.showInModule||t.minimumAmount&&s<t.minimumAmount)return!1;let r=e.map(e=>e.product.id),l=e.flatMap(e=>e.product.categories?.map(e=>e.id)||[]);switch(t.type){case"PRODUCT_SPECIFIC":return t.applicableProducts.some(e=>r.includes(e));case"CATEGORY_SPECIFIC":return t.applicableCategories.some(e=>l.includes(e));case"MINIMUM_PURCHASE":return s>=(t.minimumAmount||0);default:return!0}}),D=async a=>{if(a.trim()){C(!0),E("");try{let r=await fetch("/api/coupons/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({couponCode:a,cartItems:e,userId:t,subtotal:s})}),n=await r.json();if(n.isValid&&n.coupon){let e={coupon:n.coupon,discountAmount:n.discountAmount,isValid:!0};l(e),p(""),E("Coupon applied successfully!"),U(),T()}else E(n.errorMessage||"Invalid coupon code")}catch(e){E("Error validating coupon")}finally{C(!1)}}},_=async e=>{await D(e.code)},F=e=>{navigator.clipboard.writeText(e)},q=e=>{switch(e){case"PERCENTAGE":return a.jsx(u,{className:"w-4 h-4"});case"FIXED_AMOUNT":return a.jsx(g.Z,{className:"w-4 h-4"});case"FREE_SHIPPING":return a.jsx(y,{className:"w-4 h-4"});default:return a.jsx(j.Z,{className:"w-4 h-4"})}},O=e=>{switch(e.discountType){case"PERCENTAGE":return`${e.discountValue}% OFF`;case"FIXED_AMOUNT":return`₹${e.discountValue} OFF`;case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},R=e=>{if(!e.validUntil)return!1;let s=new Date(e.validUntil),t=new Date,a=Math.ceil((s.getTime()-t.getTime())/864e5);return a<=3&&a>0};if(S)return a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"h-16 bg-gray-200 rounded"}),a.jsx("div",{className:"h-16 bg-gray-200 rounded"})]})]})});let V=(()=>{let e=c.filter(e=>("PERCENTAGE"===e.discountType?e.discountValue>=10:e.discountValue>=50)||R(e));return e.length>=3?e.slice(0,5):c.slice(0,Math.min(5,c.length))})(),G=P?c:V;return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[a.jsx(f.Z,{className:"w-5 h-5 text-green-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Available Coupons"})]}),i.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-600 mb-3",children:"Applied Coupons"}),a.jsx("div",{className:"space-y-2",children:i.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(b.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium text-green-800",children:e.coupon.code}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["-₹",e.discountAmount.toFixed(2)]})]})]}),a.jsx("button",{onClick:()=>n(e.coupon.id),className:"p-1 text-green-600 hover:bg-green-100 rounded-full transition-colors",children:a.jsx(N.Z,{className:"w-4 h-4"})})]},e.coupon.id))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("input",{type:"text",value:m,onChange:e=>p(e.target.value.toUpperCase()),placeholder:"Enter coupon code",className:"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&D(m)}),a.jsx("button",{onClick:()=>D(m),disabled:h||!m.trim(),className:"px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?"Applying...":"Apply"})]}),Z&&(0,a.jsxs)("div",{className:`mt-2 flex items-center space-x-2 text-sm ${Z.includes("successfully")?"text-green-600":"text-red-600"}`,children:[Z.includes("successfully")?a.jsx(b.Z,{className:"w-4 h-4"}):a.jsx(v.Z,{className:"w-4 h-4"}),a.jsx("span",{children:Z})]})]}),o.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-600 mb-4 flex items-center space-x-2",children:[a.jsx(f.Z,{className:"w-4 h-4 text-yellow-500"}),a.jsx("span",{children:"Featured Offers"})]}),a.jsx("div",{className:"space-y-3",children:o.map(e=>(0,a.jsxs)("div",{className:"border-2 border-gradient-to-r from-yellow-200 to-orange-200 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 relative overflow-hidden",children:[a.jsx("div",{className:"absolute top-0 right-0 bg-yellow-400 text-yellow-900 px-2 py-1 text-xs font-bold rounded-bl-lg",children:"FEATURED"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 pr-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:"p-1 rounded bg-yellow-100 text-yellow-600",children:q(e.discountType)}),a.jsx("span",{className:"font-bold text-lg text-gray-800",children:O(e)}),R(e)&&a.jsx("span",{className:"px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium",children:"Expires Soon"})]}),a.jsx("h5",{className:"font-semibold text-gray-800 mb-1",children:e.name}),e.description&&a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(j.Z,{className:"w-3 h-3"}),a.jsx("span",{className:"font-medium",children:e.code})]}),e.validUntil&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(w.Z,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:["Until ",new Date(e.validUntil).toLocaleDateString()]})]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),a.jsx("button",{onClick:()=>_(e),className:"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm rounded-lg font-bold hover:from-yellow-600 hover:to-orange-600 transition-all transform hover:scale-105 shadow-lg",children:"Use It!"})]})]},e.id))})]}),G.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-600",children:P?"All Available Coupons":"Recommended for You"}),c.length>G.length&&a.jsx("button",{onClick:()=>M(!P),className:"text-sm text-green-600 hover:text-green-700 font-medium",children:P?"Show Less":`View All (${c.length})`})]}),a.jsx("div",{className:"space-y-3",children:G.map(e=>a.jsx("div",{className:`border rounded-lg p-4 transition-all hover:shadow-md ${R(e)?"border-orange-200 bg-orange-50":"border-gray-200 hover:border-green-300"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:`p-1 rounded ${R(e)?"bg-orange-100 text-orange-600":"bg-green-100 text-green-600"}`,children:q(e.discountType)}),a.jsx("span",{className:"font-semibold text-gray-800",children:O(e)}),R(e)&&a.jsx("span",{className:"px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full font-medium",children:"Expires Soon"})]}),a.jsx("h5",{className:"font-medium text-gray-800 mb-1",children:e.name}),e.description&&a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(j.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.code}),a.jsx("button",{onClick:()=>F(e.code),className:"p-1 hover:bg-gray-100 rounded",children:a.jsx(k.Z,{className:"w-3 h-3"})})]}),e.validUntil&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(w.Z,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:["Until ",new Date(e.validUntil).toLocaleDateString()]})]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),a.jsx("button",{onClick:()=>_(e),className:"ml-4 px-4 py-2 bg-green-600 text-white text-sm rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Apply"})]})},e.id))})]}),0===c.length&&!S&&(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(y,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),a.jsx("p",{className:"text-gray-500",children:"No coupons available for your current cart"})]})]})},Z=()=>{let{state:e,dispatch:s}=(0,p.j)(),{data:t}=(0,h.useSession)(),r=(e,t)=>{t<=0?s({type:"REMOVE_ITEM",payload:e}):s({type:"UPDATE_QUANTITY",payload:{id:e,quantity:t}})},o=e=>{s({type:"REMOVE_ITEM",payload:e})},u=e=>{s({type:"APPLY_COUPON",payload:e})},g=e=>{s({type:"REMOVE_COUPON",payload:e})};return 0===e.items.length?(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[a.jsx("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-8 text-center",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(i.Z,{className:"w-12 h-12 text-gray-400"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your cart is empty"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Add some products to get started"}),(0,a.jsxs)(l.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:["Start Shopping",a.jsx(c.Z,{className:"ml-2 w-4 h-4"})]})]})}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-16 text-center",children:[a.jsx("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:a.jsx(i.Z,{className:"w-16 h-16 text-gray-400"})}),a.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Discover our amazing products and start shopping"}),(0,a.jsxs)(l.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-colors text-lg",children:["Start Shopping",a.jsx(c.Z,{className:"ml-3 w-5 h-5"})]})]})})]}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[a.jsx("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Shopping Cart"}),a.jsx("div",{className:"space-y-4 mb-6",children:e.items.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:a.jsx(n.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"80px"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-1 line-clamp-1",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&a.jsx("div",{className:"flex flex-wrap gap-1 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),a.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.product.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.product.price]}),a.jsx("button",{onClick:()=>o(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:a.jsx(d.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(x,{className:"w-4 h-4"})}),a.jsx("span",{className:"font-medium text-gray-800 w-8 text-center",children:e.quantity}),a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(m.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]})]},e.variantKey||e.product.id))}),a.jsx("div",{className:"mb-6",children:a.jsx(C,{cartItems:e.items,subtotal:e.subtotal,userId:t?.user?.id,onCouponApply:u,onCouponRemove:g,appliedCoupons:e.coupons.appliedCoupons})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-4",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",e.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[a.jsx("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",e.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[a.jsx("span",{children:"Shipping"}),a.jsx("span",{children:"Free"})]}),a.jsx("div",{className:"border-t border-gray-200 pt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[a.jsx("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.finalTotal.toFixed(2)]})]})})]})]}),(0,a.jsxs)(l.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[a.jsx("span",{children:"Proceed to Checkout"}),a.jsx(c.Z,{className:"w-5 h-5"})]})]})}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shopping Cart"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-2",children:[a.jsx("div",{className:"space-y-6",children:e.items.map(e=>a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex space-x-6",children:[a.jsx("div",{className:"w-32 h-32 relative rounded-xl overflow-hidden flex-shrink-0",children:a.jsx(n.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"128px"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&a.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),a.jsx("p",{className:"text-gray-600",children:e.product.shortDescription})]}),a.jsx("button",{onClick:()=>o(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:a.jsx(d.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity-1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(x,{className:"w-5 h-5"})}),a.jsx("span",{className:"font-medium text-gray-800 w-12 text-center text-lg",children:e.quantity}),a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity+1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(m.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₹",e.product.price," each"]})]})]})]})]})},e.variantKey||e.product.id))}),a.jsx("div",{className:"mt-6",children:a.jsx(C,{cartItems:e.items,subtotal:e.subtotal,userId:t?.user?.id,onCouponApply:u,onCouponRemove:g,appliedCoupons:e.coupons.appliedCoupons})})]}),a.jsx("div",{className:"col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",e.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[a.jsx("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",e.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[a.jsx("span",{children:"Shipping"}),a.jsx("span",{className:"text-green-600 font-medium",children:"Free"})]}),a.jsx("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-xl",children:[a.jsx("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.finalTotal.toFixed(2)]})]})})]}),(0,a.jsxs)(l.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 text-lg",children:[a.jsx("span",{children:"Proceed to Checkout"}),a.jsx(c.Z,{className:"w-5 h-5"})]}),a.jsx(l.default,{href:"/shop",className:"w-full mt-3 border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center",children:"Continue Shopping"})]})})]})]})})]})}},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},24230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},43810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},71821:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1572:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},40765:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94019:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},25176:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Cart.tsx#default`);function n(){return a.jsx(r.Z,{children:a.jsx(l,{})})}},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,8571,3599,899,2842],()=>t(13266));module.exports=a})();