(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},70703:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),r(38932),r(36944),r(35866);var t=r(23191),a=r(88716),n=r(37922),l=r.n(n),i=r(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(s,c);let d=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38932)),"C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx"],x="/contact/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78135:(e,s,r)=>{Promise.resolve().then(r.bind(r,87697)),Promise.resolve().then(r.bind(r,63775))},63775:(e,s,r)=>{"use strict";r.d(s,{default:()=>o});var t=r(10326),a=r(17577),n=r(5932),l=r(42887),i=r(77636),c=r(48998),d=r(69436);function o(){let[e,s]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[r,o]=(0,a.useState)(!1),[x,m]=(0,a.useState)("idle"),h=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))},u=async e=>{e.preventDefault(),o(!0);try{await new Promise(e=>setTimeout(e,1500)),m("success"),s({name:"",email:"",subject:"",message:""})}catch(e){m("error")}finally{o(!1)}};return t.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[t.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Contact Us"}),t.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Have questions or need assistance? We're here to help. Reach out to us through any of the channels below."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[t.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Get in Touch"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(n.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Email"}),t.jsx("p",{className:"text-gray-600",children:"<EMAIL>"}),t.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(l.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Phone"}),t.jsx("p",{className:"text-gray-600",children:"+91 99878 10707"}),t.jsx("p",{className:"text-gray-600",children:"WhatsApp Available"})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(i.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Address"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["123 Commerce Street",t.jsx("br",{}),"Suite 100",t.jsx("br",{}),"New York, NY 10001"]})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(c.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Business Hours"}),t.jsx("p",{className:"text-gray-600",children:"Monday - Friday: 9AM - 6PM"}),t.jsx("p",{className:"text-gray-600",children:"Saturday: 10AM - 4PM"}),t.jsx("p",{className:"text-gray-600",children:"Sunday: Closed"})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-6 bg-green-50 rounded-2xl p-6",children:[t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Need Quick Answers?"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Check out our FAQ section for answers to common questions."}),t.jsx("a",{href:"/faq",className:"text-green-600 font-medium hover:text-green-700",children:"Visit FAQ →"})]})]}),t.jsx("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[t.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Send us a Message"}),(0,t.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),t.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:h,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"John Doe"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),t.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:h,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,t.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:h,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[t.jsx("option",{value:"",children:"Select a subject"}),t.jsx("option",{value:"general",children:"General Inquiry"}),t.jsx("option",{value:"order",children:"Order Support"}),t.jsx("option",{value:"product",children:"Product Information"}),t.jsx("option",{value:"technical",children:"Technical Support"}),t.jsx("option",{value:"partnership",children:"Partnership Opportunities"}),t.jsx("option",{value:"other",children:"Other"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),t.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:h,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",placeholder:"Tell us how we can help you..."})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:r,className:"w-full md:w-auto px-8 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:r?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),t.jsx("span",{children:"Sending..."})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(d.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Send Message"})]})})}),"success"===x&&t.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:t.jsx("p",{className:"text-green-800",children:"Thank you for your message! We'll get back to you within 24 hours."})}),"error"===x&&t.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:t.jsx("p",{className:"text-red-800",children:"Something went wrong. Please try again later or contact us directly."})})]})]})})]}),(0,t.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(n.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Email Support"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Get a response within 24 hours"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(l.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Phone Support"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Mon-Fri 9AM-6PM EST"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(i.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Visit Our Store"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Open 7 days a week"})]})]})]})})}},48998:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5932:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},77636:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},42887:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},69436:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},40304:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},38932:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(19510),a=r(40304);let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Contact.tsx#default`);function l(){return t.jsx(a.Z,{children:t.jsx(n,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[9276,8571,3599,899,2842],()=>r(70703));module.exports=t})();