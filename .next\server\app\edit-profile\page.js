(()=>{var e={};e.id=819,e.ids=[819],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26900:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(61012),s(36944),s(35866);var a=s(23191),r=s(88716),i=s(37922),o=s.n(i),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d=["",{children:["edit-profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61012)),"C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx"],u="/edit-profile/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/edit-profile/page",pathname:"/edit-profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},15075:(e,t,s)=>{Promise.resolve().then(s.bind(s,94494)),Promise.resolve().then(s.bind(s,52807)),Promise.resolve().then(s.bind(s,67520))},99133:(e,t,s)=>{Promise.resolve().then(s.bind(s,23082))},10138:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},94494:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>m,j:()=>x});var a=s(10326),r=s(17577);let i=(0,r.createContext)(null),o=e=>{},n=()=>null,l=(e,t)=>{if(!t||0===t.length)return e;let s=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>`${e.name}:${e.value}`).join("|");return`${e}__${s}`},d=e=>e.variantKey||e.product?.id||e.id,c=()=>n()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},u=(e,t)=>{let s=e.reduce((e,t)=>e+t.product.price*t.quantity,0),a=e.reduce((e,t)=>e+t.quantity,0),r=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:s,itemCount:a,total:s,finalTotal:s-r,totalDiscount:r}},p=(e,t)=>{let s;switch(t.type){case"ADD_ITEM":{let a;let r=l(t.payload.id,t.selectedVariants);if(e.items.find(e=>d(e)===r))a=e.items.map(e=>d(e)===r?{...e,quantity:e.quantity+1,variantKey:r}:e);else{let s={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:r};a=[...e.items,s]}let i=u(a,e.coupons.appliedCoupons);s={...e,items:a,...i,coupons:{...e.coupons,totalDiscount:i.totalDiscount}};break}case"REMOVE_ITEM":{let a=e.items.filter(e=>d(e)!==t.payload),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"UPDATE_QUANTITY":{let a=e.items.map(e=>d(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let a=[...e.coupons.appliedCoupons,t.payload],r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"REMOVE_COUPON":{let a=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"CLEAR_COUPONS":{let t=u(e.items,[]);s={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":s={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return o(s),s},m=({children:e})=>{let[t,s]=(0,r.useReducer)(p,c());return a.jsx(i.Provider,{value:{state:t,dispatch:s},children:e})},x=()=>{let e=(0,r.useContext)(i);if(!e)throw Error("useCart must be used within a CartProvider");return e}},52807:(e,t,s)=>{"use strict";s.d(t,{NotificationProvider:()=>l,z:()=>n});var a=s(10326),r=s(17577),i=s(77109);let o=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(o);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},l=({children:e})=>{let{data:t,status:s}=(0,i.useSession)(),[n,l]=(0,r.useState)([]),[d,c]=(0,r.useState)(0),[u,p]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null),h=(0,r.useCallback)(async(e={})=>{if(t?.user?.id)try{p(!0),x(null);let t=new URLSearchParams({page:(e.page||1).toString(),limit:(e.limit||10).toString(),...e.unreadOnly&&{unreadOnly:"true"}}),s=await fetch(`/api/notifications?${t}`),a=await s.json();a.success?(l(a.data.notifications),c(a.data.unreadCount)):x(a.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),x("Failed to fetch notifications")}finally{p(!1)}},[t?.user?.id]),f=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&c(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[t?.user?.id]),y=(0,r.useCallback)(async e=>{if(t?.user?.id)try{let t=await fetch(`/api/notifications/${e}/read`,{method:"POST"}),s=await t.json();s.success?(l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),c(e=>Math.max(0,e-1))):x(s.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),x("Failed to mark notification as read")}},[t?.user?.id]),g=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(l(e=>e.map(e=>({...e,isRead:!0}))),c(0)):x(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),x("Failed to mark all notifications as read")}},[t?.user?.id]);return(0,r.useEffect)(()=>{"authenticated"===s&&t?.user?.id&&(h({limit:5}),f())},[s,t?.user?.id,h,f]),(0,r.useEffect)(()=>{if(!t?.user?.id)return;let e=setInterval(()=>{f()},3e4);return()=>clearInterval(e)},[t?.user?.id,f]),a.jsx(o.Provider,{value:{notifications:n,unreadCount:d,loading:u,error:m,fetchNotifications:h,markAsRead:y,markAllAsRead:g,refreshUnreadCount:f},children:e})}},67520:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var a=s(10326),r=s(77109);function i({children:e}){return a.jsx(r.SessionProvider,{children:e})}},23082:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(10326),r=s(17577),i=s(35047),o=s(77109),n=s(86333),l=s(79635),d=s(5932),c=s(42887),u=s(31215),p=s(77636),m=s(83855),x=s(69508),h=s(98091);let f=()=>{let e=(0,i.useRouter)(),{data:t,update:s}=(0,o.useSession)(),[f,y]=(0,r.useState)(!0),[g,b]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),[N,k]=(0,r.useState)([]),[w,C]=(0,r.useState)({});(0,r.useEffect)(()=>{(async()=>{if(t?.user?.id)try{let e=await fetch(`/api/users/${t.user.id}`);if(e.ok){let t=await e.json();j({id:t.data.id,name:t.data.name||"",email:t.data.email,phone:t.data.phone||""}),k(t.data.addresses||[])}}catch(e){console.error("Error fetching user data:",e)}finally{y(!1)}})()},[t?.user?.id]);let P=(e,t)=>{v&&(j({...v,[e]:t}),w[e]&&C(t=>({...t,[e]:""})))},S=()=>{let e={};return v?.name?.trim()||(e.name="Name is required"),v?.email?.trim()?/\S+@\S+\.\S+/.test(v.email)||(e.email="Please enter a valid email"):e.email="Email is required",v?.phone&&!/^\+?[\d\s\-\(\)]+$/.test(v.phone)&&(e.phone="Please enter a valid phone number"),C(e),0===Object.keys(e).length},E=async()=>{if(S()&&v){b(!0);try{(await fetch(`/api/users/${v.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:v.name,email:v.email,phone:v.phone||null})})).ok?(await s({...t,user:{...t?.user,name:v.name,email:v.email}}),e.push("/profile")):C({general:"Failed to update profile"})}catch(e){C({general:"An error occurred while saving"})}finally{b(!1)}}},D=async e=>{if(confirm("Are you sure you want to delete this address?"))try{k(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Error deleting address:",e)}},Z=async e=>{try{k(t=>t.map(t=>({...t,isDefault:t.id===e})))}catch(e){console.error("Error setting default address:",e)}};return f?a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-8"}),a.jsx("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:a.jsx("div",{className:"space-y-4",children:[1,2,3].map(e=>a.jsx("div",{className:"h-16 bg-gray-200 rounded"},e))})})]})})}):a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[a.jsx("div",{className:"flex items-center mb-8",children:(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[a.jsx(n.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Back"})]})}),a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Edit Profile"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:a.jsx(l.Z,{className:"w-6 h-6 text-green-600"})}),a.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Personal Information"})]}),w.general&&a.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:w.general}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),a.jsx("input",{type:"text",value:v?.name||"",onChange:e=>P("name",e.target.value),className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${w.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter your full name"}),w.name&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:w.name})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(d.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"email",value:v?.email||"",onChange:e=>P("email",e.target.value),className:`w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${w.email?"border-red-500":"border-gray-300"}`,placeholder:"Enter your email address"})]}),w.email&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:w.email})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"tel",value:v?.phone||"",onChange:e=>P("phone",e.target.value),className:`w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${w.phone?"border-red-500":"border-gray-300"}`,placeholder:"Enter your phone number (optional)"})]}),w.phone&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:w.phone})]}),a.jsx("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:E,disabled:g,className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:[a.jsx(u.Z,{className:"w-5 h-5"}),a.jsx("span",{children:g?"Saving...":"Save Changes"})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:a.jsx(p.Z,{className:"w-6 h-6 text-green-600"})}),a.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Shipping Addresses"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Add Address"})]})]}),0===N.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(p.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"No addresses added yet"}),a.jsx("button",{className:"mt-4 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Add Your First Address"})]}):a.jsx("div",{className:"space-y-4",children:N.map(e=>a.jsx("div",{className:"border border-gray-200 rounded-xl p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&a.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&a.jsx("p",{className:"font-medium",children:e.company}),a.jsx("p",{children:e.address1}),e.address2&&a.jsx("p",{children:e.address2}),(0,a.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),a.jsx("p",{children:e.country}),e.phone&&a.jsx("p",{className:"font-medium",children:e.phone})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&a.jsx("button",{onClick:()=>Z(e.id),className:"px-3 py-1 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),a.jsx("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:a.jsx(x.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>D(e.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:a.jsx(h.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]})]})})}},76557:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(17577),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let s=(0,a.forwardRef)(({color:s="currentColor",size:o=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:d="",children:c,...u},p)=>(0,a.createElement)("svg",{ref:p,...r,width:o,height:o,stroke:s,strokeWidth:l?24*Number(n)/Number(o):n,className:["lucide",`lucide-${i(e)}`,d].join(" "),...u},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return s.displayName=`${e}`,s}},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},77636:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},42887:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},69508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},79635:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},61012:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\edit-profile\page.tsx#default`)},36944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>c,viewport:()=>u});var a=s(19510),r=s(77366),i=s.n(r);s(67272);var o=s(68570);let n=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let d=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let c={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},u={width:"device-width",initialScale:1,themeColor:"#16a34a"};function p({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:i().className,children:a.jsx(l,{children:a.jsx(d,{children:a.jsx(n,{children:e})})})})})}},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,8571],()=>s(26900));module.exports=a})();