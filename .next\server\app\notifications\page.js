(()=>{var e={};e.id=5193,e.ids=[5193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},69165:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o}),t(90834),t(36944),t(35866);var r=t(23191),a=t(88716),i=t(37922),l=t.n(i),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90834)),"C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx"],x="/notifications/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},30518:(e,s,t)=>{Promise.resolve().then(t.bind(t,20376))},20376:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(10326),a=t(17577),i=t(35047),l=t(77109),n=t(34565),c=t(67427),o=t(33734),d=t(40617),x=t(6507),u=t(75290),p=t(86333),g=t(41137);let h=(0,t(76557).Z)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);var m=t(87888),y=t(48998),j=t(32933),f=t(87697),b=t(52807);let N=()=>{let e=(0,i.useRouter)(),{data:s,status:t}=(0,l.useSession)(),{notifications:N,unreadCount:v,loading:R,error:E,fetchNotifications:w,markAsRead:D,markAllAsRead:k}=(0,b.z)(),[_,A]=(0,a.useState)(1),[C,S]=(0,a.useState)(1),[P,Z]=(0,a.useState)(0),[M,I]=(0,a.useState)({type:"",priority:"",isRead:"",search:""}),[O,L]=(0,a.useState)(!1);(0,a.useEffect)(()=>{"unauthenticated"===t&&e.push("/login")},[t,e]);let T=async(e=1)=>{try{let s=new URLSearchParams({page:e.toString(),limit:"20",...M.type&&{type:M.type},..."unread"===M.isRead&&{unreadOnly:"true"}}),t=await fetch(`/api/notifications?${s}`),r=await t.json();r.success&&(S(r.data.pagination.totalPages),Z(r.data.pagination.totalCount),A(e))}catch(e){console.error("Error fetching notifications:",e)}};(0,a.useEffect)(()=>{s?.user?.id&&T(1)},[s?.user?.id,M]);let U=e=>{switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"ORDER_SHIPPED":case"ORDER_DELIVERED":return r.jsx(n.Z,{className:"w-5 h-5"});case"WISHLIST_ADDED":case"WISHLIST_REMOVED":case"PRICE_DROP_ALERT":return r.jsx(c.Z,{className:"w-5 h-5"});case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return r.jsx(o.Z,{className:"w-5 h-5"});case"ADMIN_MESSAGE":case"BROADCAST":return r.jsx(d.Z,{className:"w-5 h-5"});default:return r.jsx(x.Z,{className:"w-5 h-5"})}},H=(e,s)=>{if("URGENT"===s)return"text-red-600 bg-red-100";if("HIGH"===s)return"text-orange-600 bg-orange-100";switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":return"text-blue-600 bg-blue-100";case"ORDER_SHIPPED":case"ORDER_DELIVERED":return"text-green-600 bg-green-100";case"PRICE_DROP_ALERT":return"text-purple-600 bg-purple-100";case"ADMIN_MESSAGE":case"BROADCAST":return"text-indigo-600 bg-indigo-100";default:return"text-gray-600 bg-gray-100"}},G=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)}m ago`:t<86400?`${Math.floor(t/3600)}h ago`:t<604800?`${Math.floor(t/86400)}d ago`:s.toLocaleDateString()},q=async e=>{e.isRead||await D(e.id)},W=(e,s)=>{I(t=>({...t,[e]:s})),A(1)};return"loading"===t?r.jsx(f.default,{children:r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(u.Z,{className:"w-8 h-8 animate-spin text-green-600"})})}):s?.user?r.jsx(f.default,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:r.jsx(p.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),(0,r.jsxs)("p",{className:"text-gray-600",children:[P," total • ",v," unread"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>L(!O),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[r.jsx(g.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Filter"})]}),v>0&&(0,r.jsxs)("button",{onClick:k,className:"flex items-center space-x-2 px-3 py-2 bg-green-600 text-white hover:bg-green-700 rounded-lg transition-colors",children:[r.jsx(h,{className:"w-4 h-4"}),r.jsx("span",{children:"Mark all read"})]})]})]}),O&&r.jsx("div",{className:"bg-white rounded-lg border border-gray-200 p-4 mb-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:M.type,onChange:e=>W("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[r.jsx("option",{value:"",children:"All types"}),r.jsx("option",{value:"ORDER_PLACED",children:"Order Updates"}),r.jsx("option",{value:"WISHLIST_ADDED",children:"Wishlist"}),r.jsx("option",{value:"PRICE_DROP_ALERT",children:"Price Alerts"}),r.jsx("option",{value:"REVIEW_REQUEST",children:"Reviews"}),r.jsx("option",{value:"ADMIN_MESSAGE",children:"Admin Messages"}),r.jsx("option",{value:"BROADCAST",children:"Announcements"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:M.isRead,onChange:e=>W("isRead",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[r.jsx("option",{value:"",children:"All notifications"}),r.jsx("option",{value:"unread",children:"Unread only"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Priority"}),(0,r.jsxs)("select",{value:M.priority,onChange:e=>W("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[r.jsx("option",{value:"",children:"All priorities"}),r.jsx("option",{value:"URGENT",children:"Urgent"}),r.jsx("option",{value:"HIGH",children:"High"}),r.jsx("option",{value:"NORMAL",children:"Normal"}),r.jsx("option",{value:"LOW",children:"Low"})]})]}),r.jsx("div",{className:"flex items-end",children:r.jsx("button",{onClick:()=>{I({type:"",priority:"",isRead:"",search:""}),A(1)},className:"w-full px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:"Clear filters"})})]})}),r.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:R?(0,r.jsxs)("div",{className:"p-8 text-center",children:[r.jsx(u.Z,{className:"w-8 h-8 animate-spin text-green-600 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-600",children:"Loading notifications..."})]}):E?(0,r.jsxs)("div",{className:"p-8 text-center",children:[r.jsx(m.Z,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),r.jsx("p",{className:"text-red-600 mb-4",children:E}),r.jsx("button",{onClick:()=>T(_),className:"text-red-600 hover:text-red-800 underline",children:"Try again"})]}):0===N.length?(0,r.jsxs)("div",{className:"p-8 text-center",children:[r.jsx(x.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500 mb-2",children:"No notifications found"}),r.jsx("p",{className:"text-gray-400 text-sm",children:"We'll notify you when something happens"})]}):r.jsx("div",{className:"divide-y divide-gray-200",children:N.map(e=>r.jsx("div",{onClick:()=>q(e),className:`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${e.isRead?"":"bg-blue-50"}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[r.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${H(e.type,e.priority)}`,children:U(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[r.jsx("h3",{className:`text-lg font-medium ${e.isRead?"text-gray-700":"text-gray-900"} mb-1`,children:e.title}),!e.isRead&&r.jsx("div",{className:"w-3 h-3 bg-blue-600 rounded-full ml-4 mt-1 flex-shrink-0"})]}),r.jsx("p",{className:"text-gray-600 mb-3 leading-relaxed",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500 flex items-center space-x-1",children:[r.jsx(y.Z,{className:"w-4 h-4"}),r.jsx("span",{children:G(e.createdAt)})]}),r.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${H(e.type,e.priority)}`,children:e.type.replace("_"," ")}),"NORMAL"!==e.priority&&r.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"URGENT"===e.priority?"bg-red-100 text-red-800":"HIGH"===e.priority?"bg-orange-100 text-orange-800":"bg-gray-100 text-gray-800"}`,children:e.priority})]}),e.isRead&&r.jsx(j.Z,{className:"w-4 h-4 text-green-500"})]})]})]})},e.id))})}),C>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(_-1)*20+1," to ",Math.min(20*_,P)," of ",P," notifications"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>T(_-1),disabled:1===_,className:"px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsxs)("span",{className:"px-3 py-2 text-gray-900",children:["Page ",_," of ",C]}),r.jsx("button",{onClick:()=>T(_+1),disabled:_===C,className:"px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})}):null}},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},32933:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41137:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},67427:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},75290:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},40617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},33734:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},90834:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\notifications\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8571,3599,899,2842],()=>t(69165));module.exports=r})();