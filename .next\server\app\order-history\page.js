(()=>{var e={};e.id=7947,e.ids=[7947],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},82142:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(71447),t(36944),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["order-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71447)),"C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx"],x="/order-history/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/order-history/page",pathname:"/order-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24455:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,7922))},7922:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var r=t(10326),a=t(17577),l=t(35047),i=t(48998),n=t(14228),d=t(54659),c=t(86333),o=t(12714),x=t(54689),m=t(48705);let h=()=>{let e=(0,l.useRouter)(),[s,t]=(0,a.useState)("all"),h=[{id:"ORD-2024-001",date:"2024-01-15",status:"delivered",total:89.97,items:[{name:"Botanical Cleanser",quantity:1,price:28.99},{name:"Hydrating Serum",quantity:1,price:45.99},{name:"Nourishing Moisturizer",quantity:1,price:35.99}]},{id:"ORD-2024-002",date:"2024-01-20",status:"shipped",total:62.98,items:[{name:"Rejuvenating Face Mask",quantity:1,price:32.99},{name:"Gentle Exfoliator",quantity:1,price:29.99}]},{id:"ORD-2024-003",date:"2024-01-25",status:"processing",total:74.98,items:[{name:"Eye Care Cream",quantity:1,price:39.99},{name:"Botanical Cleanser",quantity:1,price:28.99}]}],p={processing:{icon:i.Z,color:"text-green-600",bg:"bg-green-100",label:"Processing"},shipped:{icon:n.Z,color:"text-green-600",bg:"bg-green-100",label:"Shipped"},delivered:{icon:d.Z,color:"text-green-600",bg:"bg-green-100",label:"Delivered"}},g=[{id:"all",label:"All Orders",count:h.length},{id:"processing",label:"Processing",count:h.filter(e=>"processing"===e.status).length},{id:"shipped",label:"Shipped",count:h.filter(e=>"shipped"===e.status).length},{id:"delivered",label:"Delivered",count:h.filter(e=>"delivered"===e.status).length}],u="all"===s?h:h.filter(e=>e.status===s),y=h.reduce((e,s)=>e+s.total,0);return(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,r.jsxs)("div",{className:"lg:hidden",children:[r.jsx("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:r.jsx(c.Z,{className:"w-5 h-5"})}),r.jsx("h1",{className:"text-xl font-bold text-gray-800",children:"Order History"})]})}),r.jsx("div",{className:"px-4 py-6 bg-gradient-to-br from-green-50 to-green-100",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-green-600",children:h.length}),r.jsx("div",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₹",y.toFixed(2)]}),r.jsx("div",{className:"text-sm text-gray-600",children:"Total Spent"})]})]})}),r.jsx("div",{className:"px-4 py-4 bg-white border-b",children:r.jsx("div",{className:"flex space-x-2 overflow-x-auto",children:g.map(e=>(0,r.jsxs)("button",{onClick:()=>t(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${s===e.id?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[r.jsx("span",{children:e.label}),r.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs ${s===e.id?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:e.count})]},e.id))})}),r.jsx("div",{className:"px-4 py-6",children:u.length>0?r.jsx("div",{className:"space-y-4",children:u.map(e=>{let s=p[e.status].icon,t=p[e.status];return(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-800",children:e.id}),r.jsx("p",{className:"text-sm text-gray-600",children:new Date(e.date).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-1 rounded-full ${t.bg}`,children:[r.jsx(s,{className:`w-4 h-4 ${t.color}`}),r.jsx("span",{className:`text-sm font-medium ${t.color}`,children:t.label})]})]}),r.jsx("div",{className:"space-y-2 mb-4",children:e.items.map((e,s)=>(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.name]}),(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},s))}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["Total: ₹",e.total.toFixed(2)]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{className:"flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors",children:[r.jsx(o.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Details"})]}),"delivered"===e.status&&(0,r.jsxs)("button",{className:"flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:[r.jsx(x.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No orders found"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"No orders match the selected filter"}),r.jsx("button",{onClick:()=>t("all"),className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"View All Orders"})]})})]}),r.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[r.jsx("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[r.jsx(c.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back"})]})}),r.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Order History"}),r.jsx("div",{className:"bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8 mb-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl font-bold text-green-600 mb-2",children:h.length}),r.jsx("div",{className:"text-gray-600",children:"Total Orders"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:["₹",y.toFixed(2)]}),r.jsx("div",{className:"text-gray-600",children:"Total Spent"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl font-bold text-green-600 mb-2",children:h.filter(e=>"delivered"===e.status).length}),r.jsx("div",{className:"text-gray-600",children:"Completed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:["₹",(y/h.length).toFixed(2)]}),r.jsx("div",{className:"text-gray-600",children:"Average Order"})]})]})}),r.jsx("div",{className:"flex space-x-4 mb-8",children:g.map(e=>(0,r.jsxs)("button",{onClick:()=>t(e.id),className:`flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ${s===e.id?"bg-green-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"}`,children:[r.jsx("span",{children:e.label}),r.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${s===e.id?"bg-green-500 text-white":"bg-gray-100 text-gray-600"}`,children:e.count})]},e.id))}),u.length>0?r.jsx("div",{className:"space-y-6",children:u.map(e=>{let s=p[e.status].icon,t=p[e.status];return(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:e.id}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Ordered on ",new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,r.jsxs)("div",{className:`flex items-center space-x-3 px-4 py-2 rounded-xl ${t.bg}`,children:[r.jsx(s,{className:`w-5 h-5 ${t.color}`}),r.jsx("span",{className:`font-medium ${t.color}`,children:t.label})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"Items Ordered"}),r.jsx("div",{className:"space-y-2",children:e.items.map((e,s)=>(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.name]}),(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},s))})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Shipping"}),r.jsx("span",{children:"Free"})]}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200",children:[r.jsx("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]})]})]})]}),r.jsx("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex space-x-3 ml-auto",children:[(0,r.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors",children:[r.jsx(o.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"View Details"})]}),"delivered"===e.status&&(0,r.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors",children:[r.jsx(x.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Reorder"})]})]})})]},e.id)})}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx(m.Z,{className:"w-24 h-24 text-gray-300 mx-auto mb-6"}),r.jsx("h3",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"No orders found"}),r.jsx("p",{className:"text-gray-600 mb-8",children:"No orders match the selected filter"}),r.jsx("button",{onClick:()=>t("all"),className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors",children:"View All Orders"})]})]})})]})}},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},48705:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},54689:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},14228:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},71447:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\OrderHistory.tsx#default`);function i(){return r.jsx(a.Z,{children:r.jsx(l,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8571,3599,899,2842],()=>t(82142));module.exports=r})();