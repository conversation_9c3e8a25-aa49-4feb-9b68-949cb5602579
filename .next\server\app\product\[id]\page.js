(()=>{var e={};e.id=188,e.ids=[188],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},56105:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(73622),t(36944),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73622)),"C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx"],x="/product/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85841:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,54256))},75793:(e,s,t)=>{"use strict";t.d(s,{Z:()=>x});var a=t(10326);t(17577);var r=t(90434),l=t(53080),i=t(1572),n=t(67427);let c=(0,t(76557).Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var d=t(3634),o=t(12714);let x=({product:e,showAsLinks:s=!1,className:t="",maxCategories:x})=>{let m=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(e),h=x?m.slice(0,x):m;if(0===h.length)return null;let u=e=>({Skincare:a.jsx(l.Z,{className:"w-3 h-3"}),"Hair Care":a.jsx(i.Z,{className:"w-3 h-3"}),"Body Care":a.jsx(n.Z,{className:"w-3 h-3"}),cleanser:a.jsx(c,{className:"w-3 h-3"}),serum:a.jsx(d.Z,{className:"w-3 h-3"}),moisturizer:a.jsx(c,{className:"w-3 h-3"}),mask:a.jsx(o.Z,{className:"w-3 h-3"}),exfoliator:a.jsx(i.Z,{className:"w-3 h-3"}),"eye-care":a.jsx(o.Z,{className:"w-3 h-3"})})[e]||a.jsx(l.Z,{className:"w-3 h-3"});return a.jsx("div",{className:`flex flex-wrap gap-3 ${t}`,children:h.map(e=>{let t=u(e.name);return s?(0,a.jsxs)(r.default,{href:`/shop?category=${e.slug}`,className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600 hover:text-black transition-colors",children:[t,a.jsx("span",{children:e.name})]},e.id):(0,a.jsxs)("span",{className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600",children:[t,a.jsx("span",{children:e.name})]},e.id)})})}},54256:(e,s,t)=>{"use strict";t.d(s,{default:()=>q});var a=t(10326),r=t(17577),l=t(35047),i=t(77109),n=t(86333),c=t(67427),d=t(76557);let o=(0,d.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var x=t(33734),m=t(32933),h=t(57671),u=t(94494);let g=(0,d.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var p=t(941);let j=({productId:e})=>{let[s,t]=(0,r.useState)([]),[l,i]=(0,r.useState)(!0),[n,c]=(0,r.useState)(null);(0,r.useEffect)(()=>{d()},[e]);let d=async()=>{try{i(!0);let s=await fetch(`/api/products/${e}/faqs`),a=await s.json();a.success&&t(a.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{i(!1)}},o=e=>{c(n===e?null:e)};return l?a.jsx("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded mb-2"}),a.jsx("div",{className:"h-4 bg-gray-100 rounded"})]},e))}):0===s.length?a.jsx("div",{className:"text-center py-8",children:a.jsx("div",{className:"text-gray-500",children:"No frequently asked questions available for this product."})}):(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Frequently Asked Questions"}),a.jsx("div",{className:"space-y-3",children:s.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[a.jsx("button",{onClick:()=>o(e.id),className:"w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h4",{className:"font-medium text-gray-900 pr-4",children:e.question}),a.jsx("div",{className:"flex-shrink-0",children:n===e.id?a.jsx(g,{className:"w-5 h-5 text-gray-500"}):a.jsx(p.Z,{className:"w-5 h-5 text-gray-500"})})]})}),n===e.id&&a.jsx("div",{className:"px-6 pb-4 bg-gray-50",children:a.jsx("div",{className:"pt-2 text-gray-700 whitespace-pre-wrap leading-relaxed",children:e.answer})})]},e.id))}),a.jsx("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[a.jsx("strong",{children:"Have a question that's not answered here?"})," Feel free to contact our customer support team for more information about this product."]})})]})};var b=t(9891);let y=({productId:e,basePrice:s,onVariationChange:t})=>{let[l,i]=(0,r.useState)([]),[n,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)({});(0,r.useEffect)(()=>{x()},[e]),(0,r.useEffect)(()=>{if(l.length>0&&0===Object.keys(d).length){let e=l.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),s={};Object.entries(e).forEach(([e,t])=>{let a=[...t].sort((e,s)=>{let t=e.price??0;return(s.price??0)-t});s[e]=a[0]}),o(s);let a=h(Object.values(s)),r=1===Object.keys(s).length?Object.values(s)[0]:null;t&&t(r,a)}},[l,s,t,d]);let x=async()=>{try{c(!0);let s=await fetch(`/api/products/${e}/variations`),t=await s.json();t.success&&i(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{c(!1)}},m=l.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),h=e=>{let t=s??0;if(0===e.length)return t;if(e.some(e=>"INCREMENT"===e.pricingMode&&void 0!==e.price&&null!==e.price))t=(s??0)+e.reduce((e,s)=>e+(s.price??0),0);else{let s=e.filter(e=>void 0!==e.price&&null!==e.price&&e.price>0);s.length>0&&(t=s.reduce((e,s)=>(s.price??0)>(e.price??0)?s:e).price??t)}return Math.max(0,t)},u=(e,s)=>{let a={...d,[e]:s};o(a);let r=h(Object.values(a)),l=1===Object.keys(a).length?Object.values(a)[0]:null;t&&t(l,r)},g=e=>!0;if(n)return a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),a.jsx("div",{className:"flex space-x-2",children:[1,2,3].map(e=>a.jsx("div",{className:"h-10 w-16 bg-gray-200 rounded"},e))})]})});if(0===l.length)return null;let p=(()=>{let e=Object.values(d);return 0===e.length?null:{totalPrice:h(e),selectedValues:e}})();return(0,a.jsxs)("div",{className:"space-y-6",children:[Object.entries(m).map(([e,s])=>(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:e}),d[e]&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Selected: ",d[e].value]})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:s.map(s=>{let t=d[e]?.id===s.id,r=g(s);return a.jsx("button",{onClick:()=>r&&u(e,s),disabled:!r,className:`
                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors
                    ${t?"border-green-500 bg-green-50 text-green-700":r?"border-gray-300 bg-white text-gray-700 hover:border-gray-400":"border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed"}
                  `,children:a.jsx("span",{children:s.value})},s.id)})})]},e)),p&&a.jsx("div",{className:"p-3 bg-gray-50 rounded-lg",children:a.jsx("div",{className:"text-lg font-semibold text-gray-900",children:(0,b.T4)(p.totalPrice)})}),Object.keys(m).length>0&&0===Object.keys(d).length&&a.jsx("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:a.jsx("p",{className:"text-sm text-yellow-800",children:"Please select all required options before adding to cart."})})]})},f=(0,d.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),v=(0,d.Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var N=t(39183),w=t(94019);let k=({images:e,productName:s})=>{let[t,l]=(0,r.useState)(0),[i,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!0),[o,x]=(0,r.useState)(null),[m,h]=(0,r.useState)(null),u=[...e].sort((e,s)=>e.position-s.position);(0,r.useEffect)(()=>{u.length>0&&d(!1)},[u]);let g=e=>{l(e)},p=()=>{l(e=>0===e?u.length-1:e-1)},j=()=>{l(e=>e===u.length-1?0:e+1)},b=()=>{n(!1)},y=e=>{i&&("Escape"===e.key&&b(),"ArrowLeft"===e.key&&p(),"ArrowRight"===e.key&&j())};if((0,r.useEffect)(()=>(document.addEventListener("keydown",y),()=>document.removeEventListener("keydown",y)),[i]),!u||0===u.length)return a.jsx("div",{className:"w-full",children:a.jsx("div",{className:"aspect-square bg-gray-200 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[a.jsx("div",{className:"w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2"}),a.jsx("p",{className:"text-sm",children:"No images available"})]})})});let k=u[t];return(0,a.jsxs)("div",{className:"w-full",children:[a.jsx("div",{className:"relative mb-4 group",children:(0,a.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-lg bg-gray-100",onTouchStart:e=>{h(null),x(e.targetTouches[0].clientX)},onTouchMove:e=>{h(e.targetTouches[0].clientX)},onTouchEnd:()=>{if(!o||!m)return;let e=o-m;e>50&&u.length>1&&j(),e<-50&&u.length>1&&p()},children:[c&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse rounded-lg"}),a.jsx("img",{src:k.url,alt:k.alt||`${s} - Image ${t+1}`,className:"w-full h-full object-cover cursor-zoom-in transition-transform duration-300 hover:scale-105 select-none",onClick:()=>{n(!0)},onLoad:()=>d(!1),draggable:!1}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:a.jsx("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:a.jsx("div",{className:"bg-white bg-opacity-90 p-2 rounded-full",children:a.jsx(f,{className:"w-6 h-6 text-gray-700"})})})}),u.length>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:p,className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:a.jsx(v,{className:"w-5 h-5 text-gray-700"})}),a.jsx("button",{onClick:j,className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:a.jsx(N.Z,{className:"w-5 h-5 text-gray-700"})})]}),u.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:[t+1," / ",u.length]})]})}),u.length>1&&(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"hidden md:flex space-x-2 overflow-x-auto pb-2",children:u.map((e,r)=>a.jsx("button",{onClick:()=>g(r),className:`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${r===t?"border-green-500 ring-2 ring-green-200":"border-gray-200 hover:border-gray-300"}`,children:a.jsx("img",{src:e.url,alt:e.alt||`${s} thumbnail ${r+1}`,className:"w-full h-full object-cover"})},e.id||r))}),a.jsx("div",{className:"md:hidden flex space-x-2 overflow-x-auto pb-2",children:u.map((e,r)=>a.jsx("button",{onClick:()=>g(r),className:`flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ${r===t?"border-green-500 ring-1 ring-green-200":"border-gray-200"}`,children:a.jsx("img",{src:e.url,alt:e.alt||`${s} thumbnail ${r+1}`,className:"w-full h-full object-cover"})},e.id||r))}),(0,a.jsxs)("div",{className:"md:hidden flex justify-center space-x-4",children:[a.jsx("button",{onClick:p,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:a.jsx(v,{className:"w-5 h-5 text-gray-700"})}),a.jsx("button",{onClick:j,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:a.jsx(N.Z,{className:"w-5 h-5 text-gray-700"})})]})]}),i&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[a.jsx("button",{onClick:b,className:"absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full z-10",children:a.jsx(w.Z,{className:"w-6 h-6"})}),u.length>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:p,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:a.jsx(v,{className:"w-6 h-6"})}),a.jsx("button",{onClick:j,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:a.jsx(N.Z,{className:"w-6 h-6"})})]}),a.jsx("img",{src:k.url,alt:k.alt||`${s} - Image ${t+1}`,className:"max-w-full max-h-full object-contain"}),a.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-center",children:[t+1," of ",u.length,k.alt&&` - ${k.alt}`]})})]})})]})};var Z=t(75793),C=t(79635),S=t(75290);let $=({productId:e,onReviewSubmitted:s})=>{let{data:t}=(0,i.useSession)(),[l,n]=(0,r.useState)(0),[c,d]=(0,r.useState)(""),[o,m]=(0,r.useState)(""),[h,u]=(0,r.useState)(!1),[g,p]=(0,r.useState)(null),[j,b]=(0,r.useState)(!1),y=async a=>{if(a.preventDefault(),!t?.user){p("Please sign in to leave a review");return}if(0===l){p("Please select a rating");return}u(!0),p(null);try{let t=await fetch(`/api/products/${e}/reviews`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({rating:l,title:c,content:o})}),a=await t.json();a.success?(b(!0),n(0),d(""),m(""),s?.()):p(a.error||"Failed to submit review")}catch(e){p("Failed to submit review")}finally{u(!1)}};return t?.user?j?(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 text-center",children:[a.jsx("div",{className:"text-green-600 mb-2",children:"✓"}),a.jsx("p",{className:"text-green-800 font-medium",children:"Review submitted for approval!"}),a.jsx("p",{className:"text-green-600 text-sm mt-1",children:"Your review will appear once approved by our team."})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Write a Review"}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rating *"}),a.jsx("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>a.jsx("button",{type:"button",onClick:()=>n(e),className:"p-1 hover:scale-110 transition-transform",children:a.jsx(x.Z,{className:`w-6 h-6 ${e<=l?"text-yellow-400 fill-current":"text-gray-300"}`})},e))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Title"}),a.jsx("input",{type:"text",value:c,onChange:e=>d(e.target.value),placeholder:"Summarize your experience",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:100})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Details"}),a.jsx("textarea",{value:o,onChange:e=>m(e.target.value),placeholder:"Tell us about your experience with this product",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:1e3})]}),g&&a.jsx("div",{className:"text-red-600 text-sm",children:g}),a.jsx("button",{type:"submit",disabled:h||0===l,className:"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx(S.Z,{className:"w-4 h-4 inline mr-2 animate-spin"}),"Submitting..."]}):"Submit Review"})]})]}):a.jsx("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:a.jsx("p",{className:"text-gray-600",children:"Please sign in to leave a review"})})},P=({productId:e})=>{let[s,t]=(0,r.useState)([]),[l,i]=(0,r.useState)(!0),[n,c]=(0,r.useState)(null),[d,o]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m()},[e]);let m=async()=>{try{i(!0);let s=await fetch(`/api/products/${e}/reviews`),a=await s.json();a.success?t(a.data):c(a.error||"Failed to fetch reviews")}catch(e){c("Failed to fetch reviews")}finally{i(!1)}},h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),u=s.length>0?(s.reduce((e,s)=>e+s.rating,0)/s.length).toFixed(1):0;return l?a.jsx("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900",children:["Customer Reviews (",s.length,")"]}),a.jsx("button",{onClick:()=>o(!d),className:"text-green-600 hover:text-green-700 font-medium",children:"Write a Review"})]}),s.length>0&&a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(e=>a.jsx(x.Z,{className:`w-5 h-5 ${e<=Math.round(Number(u))?"text-yellow-400 fill-current":"text-gray-300"}`},e))}),a.jsx("span",{className:"text-lg font-semibold",children:u}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",s.length," reviews)"]})]})}),d&&a.jsx($,{productId:e,onReviewSubmitted:()=>{o(!1),m()}}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(x.Z,{className:"w-12 h-12 mx-auto"})}),a.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews yet"}),a.jsx("button",{onClick:()=>o(!0),className:"text-green-600 hover:text-green-700 font-medium",children:"Be the first to write a review"})]}):a.jsx("div",{className:"space-y-4",children:s.map(e=>a.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:a.jsx("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.user?.avatar?a.jsx("img",{src:e.user.avatar,alt:e.user.name||"User",className:"w-10 h-10 rounded-full"}):a.jsx(C.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("span",{className:"font-medium text-gray-900",children:e.user?.name||"Anonymous"}),e.isVerified&&a.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Verified Purchase"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(s=>a.jsx(x.Z,{className:`w-4 h-4 ${s<=e.rating?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),a.jsx("span",{className:"text-sm text-gray-500",children:h(e.createdAt)})]}),e.title&&a.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:e.title}),e.content&&a.jsx("p",{className:"text-gray-700 text-sm",children:e.content})]})]})})},e.id))})]})};var E=t(11417);let M=e=>{let s=e.reviews||[],t=s.length>0?s.reduce((e,s)=>e+s.rating,0)/s.length:0,a=[],r=[];if(e.description){e.description.toLowerCase();let s=e.description.match(/ingredients?:\s*([^.]+)/i);s&&a.push(...s[1].split(",").map(e=>e.trim()).filter(e=>e));let t=e.description.match(/benefits?:\s*([^.]+)/i);t&&r.push(...t[1].split(",").map(e=>e.trim()).filter(e=>e))}return 0===a.length&&a.push("Natural extracts","Essential oils","Vitamins"),0===r.length&&r.push("Hydrates skin","Reduces fine lines","Improves skin texture"),{id:e.id,name:e.name,description:e.description||"",shortDescription:e.shortDescription||"",price:e.price||0,image:e.images[0]?.url||"/images/default-product.jpg",images:e.images.map(s=>({id:s.id,url:s.url,alt:s.alt||e.name,position:s.position||0})),category:e.category?.slug||"skincare",featured:e.isFeatured,ingredients:a,benefits:r,rating:Math.round(10*t)/10,reviews:s.length,_raw:e}},q=({id:e})=>{let s=(0,l.useRouter)(),{data:t}=(0,i.useSession)(),{dispatch:d}=(0,u.j)(),[g,p]=(0,r.useState)("description"),[b,f]=(0,r.useState)(!1),[v,N]=(0,r.useState)(null),[w,C]=(0,r.useState)(1),[S,$]=(0,r.useState)(null),[q,D]=(0,r.useState)(!0),[A,F]=(0,r.useState)(null),[L,I]=(0,r.useState)(0),[T,_]=(0,r.useState)(!1),[z,R]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let s=async()=>{try{D(!0);let s=await fetch(`/api/products/${e}`),t=await s.json();if(t.success){let e=M(t.data);$(e),I(e.price)}else F("Product not found")}catch(e){console.error("Error fetching product:",e),F("Failed to load product")}finally{D(!1)}};e&&s()},[e]),(0,r.useEffect)(()=>{(async()=>{if(t?.user?.id&&S?.id)try{let e=await fetch("/api/wishlist");if(e.ok){let s=(await e.json()).items.some(e=>e.id===S.id);_(s)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[t,S]);let O=(e,s)=>{N(e),I(s)},U=()=>{S&&(d({type:"ADD_ITEM",payload:{...S,price:L},selectedVariants:v?[{id:v.id,name:v.name,value:v.value,price:v.price}]:void 0}),f(!0),setTimeout(()=>f(!1),2e3))},B=async()=>{if(!t?.user?.id){s.push("/login");return}if(S?.id){R(!0);try{T?(await fetch(`/api/wishlist?productId=${S.id}`,{method:"DELETE"})).ok&&_(!1):(await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:S.id})})).ok&&_(!0)}catch(e){console.error("Error updating wishlist:",e)}finally{R(!1)}}},G=async()=>{let e={title:S?.name||"Check out this product",text:S?.shortDescription||"Amazing product from Herbalicious",url:window.location.href};try{navigator.share&&navigator.canShare(e)?await navigator.share(e):(await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!"))}catch(e){console.error("Error sharing:",e);try{await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!")}catch(e){console.error("Clipboard error:",e),alert("Unable to share. Please copy the URL manually.")}}};return q?a.jsx(E.QW,{}):A||!S?a.jsx("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-red-600 mb-4",children:A||"Product not found"}),a.jsx("button",{onClick:()=>s.back(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Go Back"})]})}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,a.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[a.jsx("button",{onClick:()=>s.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:B,disabled:z,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(c.Z,{className:`w-5 h-5 ${T?"text-red-500 fill-current":"text-gray-600"}`})}),a.jsx("button",{onClick:G,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(o,{className:"w-5 h-5 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"relative p-4",children:[a.jsx(k,{images:S.images||[],productName:S.name}),a.jsx("div",{className:"absolute top-8 right-8 bg-white rounded-full p-2 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(x.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:S.rating})]})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:S.name}),a.jsx("p",{className:"text-gray-600 mb-3",children:S.shortDescription}),a.jsx("div",{className:"mb-4",children:a.jsx(Z.Z,{product:S._raw||S,showAsLinks:!0})}),a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:"text-3xl font-bold text-gray-900",children:v?`₹${L}`:S?.variants&&S.variants.length>0?`₹${Math.min(...S.variants.map(e=>e.price??0))}-${Math.max(...S.variants.map(e=>e.price??0))}`:`₹${S?.price||0}`}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>a.jsx(x.Z,{className:`w-4 h-4 ${s<Math.floor(S?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",S?.reviews||0," reviews)"]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("div",{className:"flex border-b border-gray-200",children:[{id:"description",label:"Description"},{id:"ingredients",label:"Ingredients"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>a.jsx("button",{onClick:()=>p(e.id),className:`px-4 py-2 text-sm font-medium transition-colors ${g===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"}`,children:e.label},e.id))}),(0,a.jsxs)("div",{className:"py-4",children:["description"===g&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-600 mb-4",children:S.description}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:"Benefits:"}),a.jsx("ul",{className:"space-y-1",children:S.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4 text-green-600"}),a.jsx("span",{className:"text-sm text-gray-600",children:e})]},s))})]})]}),"ingredients"===g&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:"Key Ingredients:"}),a.jsx("div",{className:"space-y-2",children:S.ingredients.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-2 h-2 bg-green-600 rounded-full"}),a.jsx("span",{className:"text-sm text-gray-600",children:e})]},s))})]}),"reviews"===g&&a.jsx("div",{children:a.jsx(P,{productId:S.id})}),"faqs"===g&&a.jsx("div",{children:a.jsx(j,{productId:S.id})})]})]}),a.jsx("div",{className:"mb-6",children:a.jsx(y,{productId:S.id,basePrice:S.price,onVariationChange:O})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>C(Math.max(1,w-1)),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"-"}),a.jsx("span",{className:"w-10 text-center font-medium",children:w}),a.jsx("button",{onClick:()=>C(w+1),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"+"})]}),a.jsx("button",{onClick:U,disabled:b,className:`flex-1 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${b?"bg-green-500 text-white":"bg-green-600 text-white hover:bg-green-700"}`,children:b?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Add to Cart"})]})})]})]})]}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("button",{onClick:()=>s.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mb-8",children:[a.jsx(n.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Back to Products"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(k,{images:S.images||[],productName:S.name}),a.jsx("div",{className:"absolute top-6 right-6 bg-white rounded-full p-3 shadow-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(x.Z,{className:"w-5 h-5 text-yellow-400 fill-current"}),a.jsx("span",{className:"font-medium text-gray-700",children:S.rating})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:S.name}),a.jsx("p",{className:"text-xl text-gray-600 mb-6",children:S.shortDescription}),a.jsx("div",{className:"mb-6",children:a.jsx(Z.Z,{product:S._raw||S,showAsLinks:!0})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("span",{className:"text-4xl font-bold text-gray-900",children:v?`₹${L}`:S?.variants&&S.variants.length>0?`₹${Math.min(...S.variants.map(e=>e.price??0))}-${Math.max(...S.variants.map(e=>e.price??0))}`:`₹${S?.price||0}`}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:B,disabled:z,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:a.jsx(c.Z,{className:`w-6 h-6 ${T?"text-red-500 fill-current":"text-gray-600"}`})}),a.jsx("button",{onClick:G,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:a.jsx(o,{className:"w-6 h-6 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-8",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>a.jsx(x.Z,{className:`w-5 h-5 ${s<Math.floor(S?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",S?.reviews||0," reviews)"]})]}),a.jsx("div",{className:"mb-8",children:a.jsx(y,{productId:S.id,basePrice:S.price,onVariationChange:O})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>C(Math.max(1,w-1)),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg",children:"-"}),a.jsx("span",{className:"w-12 text-center font-medium text-lg",children:w}),a.jsx("button",{onClick:()=>C(w+1),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg",children:"+"})]}),a.jsx("button",{onClick:U,disabled:b,className:`flex-1 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-3 text-lg ${b?"bg-green-500 text-white":"bg-green-600 text-white hover:bg-green-700"}`,children:b?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"w-6 h-6"}),a.jsx("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"w-6 h-6"}),a.jsx("span",{children:"Add to Cart"})]})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"flex border-b border-gray-200 mb-6",children:[{id:"description",label:"Description"},{id:"ingredients",label:"Ingredients"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>a.jsx("button",{onClick:()=>p(e.id),className:`px-6 py-3 font-medium transition-colors ${g===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"}`,children:e.label},e.id))}),(0,a.jsxs)("div",{children:["description"===g&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:S.description}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Benefits:"}),a.jsx("ul",{className:"space-y-3",children:S.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-3",children:[a.jsx(m.Z,{className:"w-5 h-5 text-green-600"}),a.jsx("span",{className:"text-gray-600",children:e})]},s))})]})]}),"ingredients"===g&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Key Ingredients:"}),a.jsx("div",{className:"space-y-3",children:S.ingredients.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-3 h-3 bg-green-600 rounded-full"}),a.jsx("span",{className:"text-gray-600",children:e})]},s))})]}),"reviews"===g&&a.jsx("div",{children:a.jsx(P,{productId:S.id})}),"faqs"===g&&a.jsx("div",{children:a.jsx(j,{productId:S.id})})]})]})]})]})]})})]})}},9891:(e,s,t)=>{"use strict";function a(e){return function(e,s=!0){if(isNaN(e))return"₹0";let t=new Intl.NumberFormat("en-IN",{minimumFractionDigits:s?2:0,maximumFractionDigits:s?2:0}).format(e);return`₹${t}`}(e,!0)}function r(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,s){return(e?r(e):r(s))||"product"}t.d(s,{GD:()=>r,T4:()=>a,w:()=>l})},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},32933:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},941:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},39183:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53080:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1572:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},73622:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\ProductDetail.tsx#default`);function i({params:e}){return a.jsx(r.Z,{children:a.jsx(l,{id:e.id})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,8571,3599,899,2842,7333],()=>t(56105));module.exports=a})();