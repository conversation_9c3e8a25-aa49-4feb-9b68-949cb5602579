(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},413:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(22603),t(36944),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22603)),"C:\\Users\\<USER>\\Desktop\\project\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\profile\\page.tsx"],x="/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},80815:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,30132))},30132:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var r=t(10326),a=t(17577),l=t(90434),i=t(77109),n=t(48705),d=t(67427),c=t(79635),o=t(58038),x=t(39183),m=t(71810);let h=()=>{let{data:e}=(0,i.useSession)(),[s,t]=(0,a.useState)(null),[h,p]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{if(e?.user?.id)try{let s=await fetch(`/api/users/${e.user.id}/stats`);if(s.ok){let e=await s.json();t(e.data)}}catch(e){console.error("Error fetching user stats:",e)}finally{p(!1)}})()},[e?.user?.id]);let g={name:e?.user?.name||"User",email:e?.user?.email||"",avatar:e?.user?.image||"",joinDate:s?.user?.joinDate?new Date(s.user.joinDate).toLocaleDateString("en-US",{year:"numeric",month:"long"}):"Loading...",totalOrders:s?.orders?.total||0,totalSpent:s?.orders?.totalSpent||0,isAdmin:e?.user?.role==="ADMIN",accountStatus:s?.accountStatus||"Loading..."},u=async()=>{try{await (0,i.signOut)({redirect:!1,callbackUrl:"/"}),window.location.replace("/")}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},j=[{icon:n.Z,title:"Order History",description:"View your past orders",href:"/order-history",color:"bg-green-100 text-green-600"},{icon:d.Z,title:"Wishlist",description:"Your saved items",href:"/wishlist",color:"bg-green-100 text-green-600"},{icon:c.Z,title:"Edit Profile",description:"Update your profile details",href:"/edit-profile",color:"bg-green-100 text-green-600"}],f=[{icon:o.Z,title:"Admin Panel",description:"Manage store (Admin only)",href:"/admin",color:"bg-green-100 text-green-600"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 grid grid-cols-1 lg:grid-cols-12 gap-8",children:[r.jsx("div",{className:"lg:hidden col-span-12",children:(0,r.jsxs)("div",{className:"px-4 py-6",children:[r.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center",children:g.avatar?r.jsx("img",{src:g.avatar,alt:g.name,className:"w-16 h-16 rounded-full"}):r.jsx(c.Z,{className:"w-8 h-8 text-white"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h1",{className:"text-xl font-bold text-gray-800",children:g.name}),r.jsx("p",{className:"text-gray-600",children:g.email}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",g.joinDate]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center",children:[r.jsx("p",{className:"text-2xl font-bold text-gray-800",children:g.totalOrders}),r.jsx("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center",children:[(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-800",children:["₹",g.totalSpent]}),r.jsx("p",{className:"text-sm text-gray-600",children:"Total Spent"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[j.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[r.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${e.color}`,children:r.jsx(e.icon,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-gray-800",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600",children:e.description})]}),r.jsx(x.Z,{className:"w-5 h-5 text-gray-400"})]},s)),g.isAdmin&&f.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[r.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${e.color}`,children:r.jsx(e.icon,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-gray-800",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600",children:e.description})]}),r.jsx(x.Z,{className:"w-5 h-5 text-gray-400"})]},`admin-${s}`))]}),r.jsx("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:u,className:"w-full flex items-center justify-center space-x-2 p-4 bg-red-50 text-red-600 rounded-2xl font-medium hover:bg-red-100 transition-colors",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Sign Out"})]})})]})}),r.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-12",children:"Profile"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[r.jsx("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4",children:g.avatar?r.jsx("img",{src:g.avatar,alt:g.name,className:"w-24 h-24 rounded-full"}):r.jsx(c.Z,{className:"w-12 h-12 text-white"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-1",children:g.name}),r.jsx("p",{className:"text-gray-600 mb-1",children:g.email}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",g.joinDate]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Account Stats"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Total Orders"}),r.jsx("span",{className:"font-semibold text-gray-800",children:g.totalOrders})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Total Spent"}),(0,r.jsxs)("span",{className:"font-semibold text-gray-800",children:["₹",g.totalSpent]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Account Status"}),r.jsx("span",{className:"font-semibold text-green-600",children:g.accountStatus})]})]})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Quick Actions"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:j.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-100",children:[r.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${e.color}`,children:r.jsx(e.icon,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-medium text-gray-800",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Account Actions"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsxs)(l.default,{href:"/edit-profile",className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors",children:[r.jsx(c.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Edit Profile"})]}),g.isAdmin&&(0,r.jsxs)(l.default,{href:"/admin",className:"flex items-center space-x-2 px-6 py-3 bg-green-700 text-white rounded-xl font-medium hover:bg-green-800 transition-colors",children:[r.jsx(o.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Admin Panel"})]}),(0,r.jsxs)("button",{onClick:u,className:"flex items-center space-x-2 px-6 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors ml-auto",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Sign Out"})]})]})]})]})]})]})})]})}},39183:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},67427:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},71810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},48705:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},22603:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Profile.tsx#default`);function i(){return r.jsx(a.Z,{children:r.jsx(l,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8571,3599,899,2842],()=>t(413));module.exports=r})();