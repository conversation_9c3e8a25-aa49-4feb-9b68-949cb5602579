(()=>{var e={};e.id=3021,e.ids=[3021],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17322:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o}),t(15392),t(36944),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["shop",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15392)),"C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx"],x="/shop/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/shop/page",pathname:"/shop",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},86890:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,13448))},75793:(e,s,t)=>{"use strict";t.d(s,{Z:()=>x});var a=t(10326);t(17577);var r=t(90434),l=t(53080),i=t(1572),n=t(67427);let c=(0,t(76557).Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var o=t(3634),d=t(12714);let x=({product:e,showAsLinks:s=!1,className:t="",maxCategories:x})=>{let m=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(e),h=x?m.slice(0,x):m;if(0===h.length)return null;let u=e=>({Skincare:a.jsx(l.Z,{className:"w-3 h-3"}),"Hair Care":a.jsx(i.Z,{className:"w-3 h-3"}),"Body Care":a.jsx(n.Z,{className:"w-3 h-3"}),cleanser:a.jsx(c,{className:"w-3 h-3"}),serum:a.jsx(o.Z,{className:"w-3 h-3"}),moisturizer:a.jsx(c,{className:"w-3 h-3"}),mask:a.jsx(d.Z,{className:"w-3 h-3"}),exfoliator:a.jsx(i.Z,{className:"w-3 h-3"}),"eye-care":a.jsx(d.Z,{className:"w-3 h-3"})})[e]||a.jsx(l.Z,{className:"w-3 h-3"});return a.jsx("div",{className:`flex flex-wrap gap-3 ${t}`,children:h.map(e=>{let t=u(e.name);return s?(0,a.jsxs)(r.default,{href:`/shop?category=${e.slug}`,className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600 hover:text-black transition-colors",children:[t,a.jsx("span",{children:e.name})]},e.id):(0,a.jsxs)("span",{className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600",children:[t,a.jsx("span",{children:e.name})]},e.id)})})}},13448:(e,s,t)=>{"use strict";t.d(s,{default:()=>M});var a=t(10326),r=t(17577),l=t(35047),i=t(88307),n=t(76557);let c=(0,n.Z)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var o=t(94019),d=t(924),x=t(29389);let m=(0,n.Z)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var h=t(90434),u=t(46226),g=t(67427),p=t(33734),y=t(83855),f=t(57671),j=t(94494);function v(e){return!e||e.includes("placeholder")||e.includes("api/placeholder")?"/images/default-product.jpg":e}var b=t(75793);let N=({product:e,featured:s=!1,viewMode:t="grid"})=>{let{dispatch:l}=(0,j.j)(),[i,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(!1),d=s=>{s.preventDefault(),s.stopPropagation();let t=e.price||0,a=e.variants;if(0===e.price&&a&&a.length>0){let e=a.map(e=>e.price??0).filter(e=>e>0);e.length>0&&(t=Math.max(...e))}l({type:"ADD_ITEM",payload:{...e,price:t}})},x=e=>{e.preventDefault(),e.stopPropagation(),n(!i)},m=e.variants,N=`₹${e.price||0}`;if(m&&m.length>0){let s=[e.price||0,...m.map(e=>e.price??0)].filter(e=>e>0);if(s.length>0){let e=Math.min(...s),t=Math.max(...s);e!==t&&(N=`₹${e} - ₹${t}`)}}return"list"===t?a.jsx(h.default,{href:`/product/${e.slug||e.id}`,className:"block group",children:a.jsx("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-green-200",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"relative w-28 h-28 sm:w-40 sm:h-40 flex-shrink-0",children:(0,a.jsxs)("div",{className:"w-full h-full relative overflow-hidden rounded-l-2xl",children:[!c&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),a.jsx(u.default,{src:v(e.image),alt:e.name,fill:!0,className:`object-cover group-hover:scale-105 transition-transform duration-300 ${c?"opacity-100":"opacity-0"}`,sizes:"(max-width: 640px) 112px, 160px",onLoad:()=>o(!0)})]})}),(0,a.jsxs)("div",{className:"flex-1 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-1 line-clamp-1 text-base sm:text-lg",children:e.name}),a.jsx("div",{className:"mb-2",children:a.jsx(b.Z,{product:e._raw||e,maxCategories:1})})]}),a.jsx("button",{onClick:x,className:`p-1.5 sm:p-2 rounded-full transition-colors ml-2 ${i?"text-red-500 bg-red-50 hover:bg-red-100":"text-gray-400 hover:text-red-500 hover:bg-red-50"}`,children:a.jsx(g.Z,{className:`w-3.5 h-3.5 sm:w-4 sm:h-4 ${i?"fill-current":""}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:N}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(p.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-xs text-gray-600 ml-1",children:e.rating})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.reviews," reviews)"]})]})]}),(0,a.jsxs)("button",{onClick:d,className:"bg-green-600 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center space-x-1.5 sm:space-x-2",children:[a.jsx(y.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:"Add"})]})]})]})]})})}):a.jsx(h.default,{href:`/product/${e.slug||e.id}`,className:"block group",children:(0,a.jsxs)("div",{className:`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-green-200 ${s?"w-72 flex-shrink-0 lg:w-80":"w-full"}`,children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-full aspect-square relative overflow-hidden",children:[!c&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),a.jsx(u.default,{src:v(e.image),alt:e.name,fill:!0,className:`object-cover group-hover:scale-105 transition-transform duration-300 ${c?"opacity-100":"opacity-0"}`,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>o(!0)})]}),a.jsx("div",{className:"absolute top-3 right-3 flex flex-col space-y-2",children:a.jsx("button",{onClick:x,className:`p-2 rounded-full shadow-sm transition-all duration-200 ${i?"bg-red-500 text-white":"bg-white text-gray-600 hover:text-red-500 hover:bg-red-50"}`,children:a.jsx(g.Z,{className:`w-4 h-4 ${i?"fill-current":""}`})})}),a.jsx("div",{className:"absolute top-3 left-3 bg-white rounded-full px-2 py-1 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(p.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-xs font-medium text-gray-700",children:e.rating})]})})]}),(0,a.jsxs)("div",{className:"p-4 lg:p-5",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-1 text-base lg:text-lg leading-tight",children:e.name}),a.jsx("div",{className:"mb-4",children:a.jsx(b.Z,{product:e._raw||e,className:"mb-2",maxCategories:1})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("span",{className:"text-base lg:text-lg font-bold text-gray-900 mb-1",children:N}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.reviews," reviews"]})]}),a.jsx("button",{onClick:d,className:"bg-green-600 text-white p-2.5 lg:p-3 rounded-full hover:bg-green-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105 active:scale-95",children:a.jsx(f.Z,{className:"w-4 h-4 lg:w-5 lg:h-5"})})]})]})]})})};var w=t(75290);let k=({hasMore:e,loading:s,onLoadMore:t,threshold:l=100,children:i})=>{let n=(0,r.useRef)(null),c=(0,r.useRef)(!1),o=(0,r.useCallback)(a=>{let[r]=a;r.isIntersecting&&e&&!s&&!c.current&&(c.current=!0,t(),setTimeout(()=>{c.current=!1},1e3))},[e,s,t]);return(0,r.useEffect)(()=>{let e=n.current;if(!e)return;let s=new IntersectionObserver(o,{threshold:.1,rootMargin:`${l}px`});return s.observe(e),()=>{e&&s.unobserve(e)}},[o,l]),(0,a.jsxs)("div",{children:[i,(0,a.jsxs)("div",{ref:n,className:"w-full py-8",children:[s&&a.jsx("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[a.jsx(w.Z,{className:"w-5 h-5 animate-spin"}),a.jsx("span",{className:"text-sm font-medium",children:"Loading more products..."})]})}),!e&&!s&&a.jsx("div",{className:"text-center py-8",children:a.jsx("div",{className:"inline-flex items-center px-4 py-2 bg-gray-100 rounded-full",children:a.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"You've reached the end of our collection"})})})]})]})};var Z=t(11417);let C=e=>{let s=e.category?.slug||"skincare";if(e.price,e.variants&&e.variants.length>0){let s=[e.price||0,...e.variants.map(e=>e.price??0)].filter(e=>e>0);s.length>0&&([...s],[...s])}return{id:e.id,slug:e.slug,name:e.name,description:e.description,shortDescription:e.shortDescription,price:e.price||0,image:e.images[0]?.url||"/placeholde.jpg",images:e.images.map(e=>({id:e.id,url:e.url,alt:e.alt,position:e.position||0})),category:s,featured:e.isFeatured,ingredients:[],benefits:[],rating:0,reviews:0,variants:e.variants,_raw:e}},M=()=>{let e=(0,l.useSearchParams)(),[s,t]=(0,r.useState)("all"),[n,h]=(0,r.useState)("random"),[u,g]=(0,r.useState)(!1),[p,y]=(0,r.useState)([]),[f,j]=(0,r.useState)([]),[v,b]=(0,r.useState)([]),[w,M]=(0,r.useState)([]),[S,P]=(0,r.useState)(!0),[_,$]=(0,r.useState)(!1),[L,A]=(0,r.useState)(null),[D,q]=(0,r.useState)(""),[E,z]=(0,r.useState)("grid"),[F,U]=(0,r.useState)([0,1e4]),[R,I]=(0,r.useState)(!1),[T,H]=(0,r.useState)(1),[G,O]=(0,r.useState)(!0),B=(0,r.useRef)(null);(0,r.useEffect)(()=>{let s=e.get("category");s&&t(s)},[e]),(0,r.useEffect)(()=>{(async()=>{try{P(!0);let e=new URLSearchParams({limit:"1000",sort:n}),[s,t]=await Promise.all([fetch(`/api/products?${e.toString()}`),fetch("/api/categories")]),[a,r]=await Promise.all([s.json(),t.json()]);if(a.success&&r.success){let e=a.data.filter(e=>e.isActive).map(e=>({...C(e),_raw:e}));if(y(e),j(e),0===w.length){let e=[{id:"all",name:"All Products"},...r.data.map(e=>({id:e.slug,name:e.name}))];M(e)}}else A("Failed to fetch data")}catch(e){console.error("Error fetching data:",e),A("Failed to load products")}finally{P(!1)}})()},[n]),(0,r.useEffect)(()=>{let e=p;if("all"!==s&&(e=e.filter(e=>{let t=e._raw;return t.category?.slug===s||(t.productCategories?.some(e=>e.category.slug===s)??!1)})),D.trim()){let s=D.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s)||e.shortDescription.toLowerCase().includes(s))}j(e=e.filter(e=>{let s=e.price||0;return s>=F[0]&&s<=F[1]})),H(1),O(e.length>12)},[s,p,D,F]),(0,r.useEffect)(()=>{let e=12*T;b(f.slice(0,e)),O(e<f.length)},[f,T]);let V=()=>{!_&&G&&($(!0),setTimeout(()=>{H(e=>e+1),$(!1)},500))};(0,r.useEffect)(()=>{R&&B.current&&B.current.focus()},[R]);let W=e=>{t(e)};return S?a.jsx(Z.sW,{}):L?a.jsx("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-red-600 mb-4",children:L}),a.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Try Again"})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:[(0,a.jsxs)("div",{className:"px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Shop"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>I(!R),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("button",{onClick:()=>g(!u),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[a.jsx(c,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm font-medium",children:"Filters"})]})]})]}),a.jsx("div",{className:`transition-all duration-300 ${R?"max-h-20 opacity-100 mb-3":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx("input",{ref:B,type:"text",placeholder:"Search products...",value:D,onChange:e=>q(e.target.value),className:"w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),D&&a.jsx("button",{onClick:()=>q(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.jsx(o.Z,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[f.length," products found"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>z("grid"),className:`p-1.5 rounded ${"grid"===E?"bg-green-100 text-green-600":"text-gray-400"}`,children:a.jsx(d.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>z("list"),className:`p-1.5 rounded ${"list"===E?"bg-green-100 text-green-600":"text-gray-400"}`,children:a.jsx(x.Z,{className:"w-4 h-4"})})]})]})]}),a.jsx("div",{className:`border-t bg-white transition-all duration-300 ${u?"max-h-screen opacity-100":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,a.jsxs)("div",{className:"px-4 py-4 space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Sort by"}),(0,a.jsxs)("select",{value:n,onChange:e=>h(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-sm",children:[a.jsx("option",{value:"random",children:"Random (Default)"}),a.jsx("option",{value:"name_asc",children:"Name (A-Z)"}),a.jsx("option",{value:"name_desc",children:"Name (Z-A)"}),a.jsx("option",{value:"price_asc",children:"Price (Low to High)"}),a.jsx("option",{value:"price_desc",children:"Price (High to Low)"}),a.jsx("option",{value:"newest",children:"Newest First"}),a.jsx("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Price Range"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:F[0],onChange:e=>U([parseInt(e.target.value)||0,F[1]]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),a.jsx("span",{className:"text-gray-400",children:"-"}),a.jsx("input",{type:"number",placeholder:"Max",value:F[1],onChange:e=>U([F[0],parseInt(e.target.value)||1e4]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",F[0]," - ₹",F[1]]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Categories"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:w.map(e=>a.jsx("button",{onClick:()=>W(e.id),className:`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${s===e.id?"bg-green-600 text-white shadow-md scale-105":"bg-gray-100 text-gray-700 hover:bg-gray-200 active:scale-95"}`,children:e.name},e.id))})]}),a.jsx("button",{onClick:()=>{t("all"),q(""),U([0,1e4]),h("random")},className:"w-full py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors",children:"Clear All Filters"})]})})]}),a.jsx("div",{className:"px-4 py-6",children:0===f.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(i.Z,{className:"w-8 h-8 text-gray-400"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),a.jsx("p",{className:"text-gray-500 mb-4",children:"Try adjusting your search or filters"}),a.jsx("button",{onClick:()=>{t("all"),q(""),U([0,1e4])},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):a.jsx(k,{hasMore:G,loading:_,onLoadMore:V,children:a.jsx("div",{className:`${"grid"===E?"grid grid-cols-2 gap-4":"space-y-4"}`,children:v.map(e=>a.jsx(N,{product:e,viewMode:E},e.id))})})})]}),(0,a.jsxs)("div",{className:"hidden lg:block",children:[a.jsx("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Our Products"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover our complete collection of natural skincare products, carefully crafted with botanical ingredients"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[a.jsx(i.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",placeholder:"Search products...",value:D,onChange:e=>q(e.target.value),className:"w-full pl-12 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),D&&a.jsx("button",{onClick:()=>q(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.jsx(o.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(m,{className:"w-5 h-5 text-gray-600"}),(0,a.jsxs)("select",{value:n,onChange:e=>h(e.target.value),className:"px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",children:[a.jsx("option",{value:"random",children:"Random (Default)"}),a.jsx("option",{value:"name_asc",children:"Name (A-Z)"}),a.jsx("option",{value:"name_desc",children:"Name (Z-A)"}),a.jsx("option",{value:"price_asc",children:"Price (Low to High)"}),a.jsx("option",{value:"price_desc",children:"Price (High to Low)"}),a.jsx("option",{value:"newest",children:"Newest First"}),a.jsx("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[a.jsx("button",{onClick:()=>z("grid"),className:`p-2 rounded ${"grid"===E?"bg-white shadow-sm text-green-600":"text-gray-500"}`,children:a.jsx(d.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>z("list"),className:`p-2 rounded ${"list"===E?"bg-white shadow-sm text-green-600":"text-gray-500"}`,children:a.jsx(x.Z,{className:"w-4 h-4"})})]})]})]}),a.jsx("div",{className:"flex items-center justify-center gap-6 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Price Range:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:F[0],onChange:e=>U([parseInt(e.target.value)||0,F[1]]),className:"w-20 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),a.jsx("span",{className:"text-gray-400",children:"-"}),a.jsx("input",{type:"number",placeholder:"Max",value:F[1],onChange:e=>U([F[0],parseInt(e.target.value)||1e4]),className:"w-20 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]})]})}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:w.map(e=>a.jsx("button",{onClick:()=>W(e.id),className:`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${s===e.id?"bg-green-600 text-white shadow-md scale-105":"text-gray-700 hover:bg-gray-100 active:scale-95"}`,children:e.name},e.id))})})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-gray-600",children:[f.length," products found"]}),a.jsx("button",{onClick:()=>{t("all"),q(""),U([0,1e4]),h("random")},className:"text-sm text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),0===f.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[a.jsx("div",{className:"w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(i.Z,{className:"w-10 h-10 text-gray-400"})}),a.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"No products found"}),a.jsx("p",{className:"text-gray-500 mb-6",children:"Try adjusting your search or filters"}),a.jsx("button",{onClick:()=>{t("all"),q(""),U([0,1e4])},className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):a.jsx(k,{hasMore:G,loading:_,onLoadMore:V,children:a.jsx("div",{className:`${"grid"===E?"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6":"grid grid-cols-1 lg:grid-cols-2 gap-6"}`,children:v.map(e=>a.jsx(N,{product:e,viewMode:E},e.id))})})]})]})]})}},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},924:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},53080:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},29389:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},75290:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1572:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},15392:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Shop.tsx#default`);function i(){return a.jsx(r.Z,{children:a.jsx(l,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,8571,3599,899,2842,7333],()=>t(17322));module.exports=a})();