"use strict";(()=>{var e={};e.id=4090,e.ids=[4090],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},87814:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>j,patchFetch:()=>m,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>c});var o=r(49303),a=r(88716),n=r(60670),p=r(87070),u=r(92048),i=r(55315);async function c(e){try{let e=(0,i.join)(process.cwd(),"public","sw.js"),t=(0,u.readFileSync)(e,"utf8");return new p.NextResponse(t,{status:200,headers:{"Content-Type":"application/javascript","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return new p.NextResponse("Service worker not found",{status:404})}}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/sw.js/route",pathname:"/sw.js",filename:"route",bundlePath:"app/sw.js/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\sw.js\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:h,serverHooks:x}=d,j="/sw.js/route";function m(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:h})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972],()=>r(87814));module.exports=s})();