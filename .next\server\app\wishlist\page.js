(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63689:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o}),t(84699),t(36944),t(35866);var r=t(23191),l=t(88716),a=t(37922),i=t.n(a),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84699)),"C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36944)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx"],x="/wishlist/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16785:(e,s,t)=>{Promise.resolve().then(t.bind(t,87697)),Promise.resolve().then(t.bind(t,80084))},80084:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var r=t(10326),l=t(17577),a=t(35047),i=t(77109),n=t(90434),c=t(46226),o=t(86333),d=t(94019),x=t(67427),m=t(57671),h=t(33734),p=t(94494),g=t(11417);let u=()=>{let e=(0,a.useRouter)(),{data:s}=(0,i.useSession)(),{dispatch:t}=(0,p.j)(),[u,j]=(0,l.useState)([]),[f,b]=(0,l.useState)(!0),[v,N]=(0,l.useState)(null),[w,y]=(0,l.useState)([]);(0,l.useEffect)(()=>{(async()=>{if(!s?.user?.id){b(!1);return}try{let e=await fetch("/api/wishlist");if(!e.ok)throw Error("Failed to fetch wishlist");let s=await e.json();j(s.items||[])}catch(e){console.error("Error fetching wishlist:",e),N("Failed to load wishlist")}finally{b(!1)}})()},[s]),(0,l.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/products?limit=4");if(e.ok){let s=await e.json();y(s.data||[])}}catch(e){console.error("Error fetching recommendations:",e)}})()},[]);let k=async e=>{try{if(!(await fetch(`/api/wishlist?productId=${e}`,{method:"DELETE"})).ok)throw Error("Failed to remove from wishlist");j(s=>s.filter(s=>s.id!==e))}catch(e){console.error("Error removing from wishlist:",e)}},C=e=>{t({type:"ADD_ITEM",payload:{id:e.id,name:e.name,description:e.shortDescription,shortDescription:e.shortDescription,price:e.price,image:e.image,category:"general",featured:!1,ingredients:[],benefits:[],rating:e.rating,reviews:e.reviews}})},Z=()=>{u.forEach(e=>{t({type:"ADD_ITEM",payload:{id:e.id,name:e.name,description:e.shortDescription,shortDescription:e.shortDescription,price:e.price,image:e.image,category:"general",featured:!1,ingredients:[],benefits:[],rating:e.rating,reviews:e.reviews}})})};return(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,r.jsxs)("div",{className:"lg:hidden",children:[r.jsx("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:r.jsx(o.Z,{className:"w-5 h-5"})}),r.jsx("h1",{className:"text-xl font-bold text-gray-800",children:"Wishlist"}),r.jsx("div",{className:"ml-auto",children:(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[u.length," items"]})})]})}),r.jsx("div",{className:"px-4 py-6",children:f?r.jsx(g.Gw,{}):v?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx(d.Z,{className:"w-12 h-12 text-red-500"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Error loading wishlist"}),r.jsx("p",{className:"text-gray-600 mb-6",children:v}),r.jsx("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Try Again"})]}):s?u.length>0?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("button",{onClick:Z,className:"w-full bg-green-600 text-white py-3 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Add All to Cart"})]})}),r.jsx("div",{className:"space-y-4",children:u.map(e=>r.jsx("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex space-x-4",children:[r.jsx(n.default,{href:`/product/${e.id}`,className:"block",children:r.jsx("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:r.jsx(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"80px"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[r.jsx(n.default,{href:`/product/${e.id}`,children:r.jsx("h3",{className:"font-semibold text-gray-800 line-clamp-1",children:e.name})}),r.jsx("button",{onClick:()=>k(e.id),className:"p-1 text-red-500 hover:bg-red-50 rounded-full transition-colors ml-2",children:r.jsx(d.Z,{className:"w-4 h-4"})})]}),r.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),r.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.rating})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.reviews," reviews)"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.price]}),(0,r.jsxs)("button",{onClick:()=>C(e),className:"bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-green-700 transition-colors flex items-center space-x-1",children:[r.jsx(m.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add to Cart"})]})]})]})]})},e.id))}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"You might also like"}),r.jsx("div",{className:"grid grid-cols-2 gap-4",children:w.slice(0,4).map(e=>r.jsx(n.default,{href:`/product/${e.id}`,className:"block",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[r.jsx("div",{className:"w-full h-32 relative rounded-xl overflow-hidden mb-3",children:r.jsx(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 50vw, 25vw"})}),r.jsx("h3",{className:"font-medium text-gray-800 text-sm line-clamp-1 mb-1",children:e.name}),r.jsx("p",{className:"text-xs text-gray-600 line-clamp-1 mb-2",children:e.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",e.price]}),r.jsx("button",{className:"p-1 text-gray-400 hover:text-red-500 transition-colors",children:r.jsx(x.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx(x.Z,{className:"w-12 h-12 text-gray-400"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your wishlist is empty"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"Save products you love for later"}),r.jsx(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Start Shopping"})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx(x.Z,{className:"w-12 h-12 text-gray-400"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Sign in to view wishlist"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"Create an account to save your favorite products"}),r.jsx(n.default,{href:"/login",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Sign In"})]})})]}),r.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[r.jsx("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[r.jsx(o.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back"})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-800",children:"My Wishlist"}),(0,r.jsxs)("span",{className:"text-gray-600",children:[u.length," items saved"]})]}),f?r.jsx(g.Gw,{}):v?(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8",children:r.jsx(d.Z,{className:"w-16 h-16 text-red-500"})}),r.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Error loading wishlist"}),r.jsx("p",{className:"text-xl text-gray-600 mb-8",children:v}),r.jsx("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Try Again"})]}):s?u.length>0?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"mb-8",children:(0,r.jsxs)("button",{onClick:Z,className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center space-x-3 text-lg",children:[r.jsx(m.Z,{className:"w-6 h-6"}),r.jsx("span",{children:"Add All to Cart"})]})}),r.jsx("div",{className:"grid grid-cols-3 gap-6 mb-12",children:u.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[r.jsx(n.default,{href:`/product/${e.id}`,children:r.jsx("div",{className:"w-full h-48 relative rounded-xl overflow-hidden",children:r.jsx(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300",sizes:"(max-width: 1200px) 50vw, 33vw"})})}),r.jsx("button",{onClick:()=>k(e.id),className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-red-500 hover:bg-red-50 transition-colors",children:r.jsx(d.Z,{className:"w-4 h-4"})})]}),r.jsx(n.default,{href:`/product/${e.id}`,children:r.jsx("h3",{className:"font-semibold text-gray-800 mb-2 hover:text-green-600 transition-colors",children:e.name})}),r.jsx("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),r.jsx("span",{className:"font-medium text-gray-700",children:e.rating})]}),(0,r.jsxs)("span",{className:"text-gray-500",children:["(",e.reviews," reviews)"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:["₹",e.price]}),(0,r.jsxs)("button",{onClick:()=>C(e),className:"bg-green-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-green-700 transition-colors flex items-center space-x-2",children:[r.jsx(m.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add to Cart"})]})]})]},e.id))}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"You might also like"}),r.jsx("div",{className:"grid grid-cols-4 gap-6",children:w.slice(0,4).map(e=>r.jsx(n.default,{href:`/product/${e.id}`,className:"block group",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[r.jsx("div",{className:"w-full h-40 relative rounded-xl overflow-hidden",children:r.jsx(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 1200px) 50vw, 25vw"})}),r.jsx("button",{className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-gray-400 hover:text-red-500 transition-colors",children:r.jsx(x.Z,{className:"w-4 h-4"})})]}),r.jsx("h3",{className:"font-medium text-gray-800 mb-2 group-hover:text-green-600 transition-colors line-clamp-1",children:e.name}),r.jsx("p",{className:"text-gray-600 text-sm line-clamp-2 mb-3",children:e.shortDescription}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",e.price]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[r.jsx(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),r.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.rating})]})]})]})},e.id))})]})]}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:r.jsx(x.Z,{className:"w-16 h-16 text-gray-400"})}),r.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Your wishlist is empty"}),r.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Save products you love for later and never lose track of them"}),r.jsx(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Start Shopping"})]}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:r.jsx(x.Z,{className:"w-16 h-16 text-gray-400"})}),r.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Sign in to view wishlist"}),r.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Create an account to save your favorite products"}),r.jsx(n.default,{href:"/login",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Sign In"})]})]})})]})}},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},84699:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),l=t(40304);let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Wishlist.tsx#default`);function i(){return r.jsx(l.Z,{children:r.jsx(a,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8571,3599,899,2842,7333],()=>t(63689));module.exports=r})();