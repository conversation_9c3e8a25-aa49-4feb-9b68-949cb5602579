"use strict";exports.id=2125,exports.ids=[2125],exports.modules={95306:(e,t,i)=>{i.d(t,{L:()=>l});var r=i(13539),o=i(77234),a=i(53797),s=i(98691),n=i(3474);let l={adapter:(0,r.N)(n._),providers:[(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await n._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await s.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:i}){if(t&&(e.sub=t.id,e.role=t.role),i&&e.email)try{let t=await n._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let i=await n._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(i)return{...e,user:{...e.user,id:i.id,role:i.role,email:i.email,name:i.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:i,isNewUser:r}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,i)=>{i.d(t,{_:()=>o});var r=i(53524);let o=globalThis.prisma??new r.PrismaClient({log:["error"]})},95921:(e,t,i)=>{i.d(t,{Cz:()=>l,LS:()=>d,Pi:()=>c,bn:()=>p,jC:()=>u});var r=i(55245),o=i(54211);async function a(e){let t=process.env.BREVO_API_KEY;if(!t)throw Error("BREVO_API_KEY is not configured");let i={sender:{name:process.env.FROM_NAME||"Herbalicious",email:process.env.FROM_EMAIL||"<EMAIL>"},to:[{email:e.to}],subject:e.subject,htmlContent:e.html},r=await fetch("https://api.brevo.com/v3/smtp/email",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","api-key":t},body:JSON.stringify(i)});if(!r.ok){let t=await r.text();throw o.kg.emailError(e.to,e.subject,Error(`Brevo API error: ${r.status} - ${t}`)),Error(`Failed to send email via Brevo API: ${r.status}`)}o.kg.emailSent(e.to,e.subject,"brevo-api")}let s=()=>{if(process.env.SMTP_HOST)return r.createTransport({host:process.env.SMTP_HOST,port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}});throw Error("No email configuration found")};async function n(e){let t=s(),i={from:e.from||process.env.FROM_EMAIL||"<EMAIL>",to:e.to,subject:e.subject,html:e.html};await t.sendMail(i),o.kg.emailSent(e.to,e.subject,"smtp")}async function l(e){try{if(process.env.BREVO_API_KEY){await a(e);return}await n(e)}catch(t){throw o.kg.emailError(e.to,e.subject,t),t}}async function d(e,t){let i=`${process.env.NEXTAUTH_URL}/reset-password?token=${t}`,r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Password Reset Request</h2>
      <p>You have requested to reset your password for your Herbalicious account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${i}" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${i}</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Password Reset Request - Herbalicious",html:r})}async function c(e,t){let i=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Welcome to Herbalicious, ${t}!</h2>
      <p>Thank you for joining our community of natural health enthusiasts.</p>
      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL}/shop" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Shopping
        </a>
      </div>
      <p>If you have any questions, feel free to contact our support team.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Welcome to Herbalicious!",html:i})}async function p(e,t){let i=t.items.map(e=>`
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${e.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${e.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${e.price.toFixed(2)}</td>
    </tr>
  `).join(""),r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Confirmation</h2>
      <p>Thank you for your order! Here are the details:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0;">Order #${t.orderId}</h3>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${i}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px; font-weight: bold; border-top: 2px solid #ddd;">Total:</td>
            <td style="padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;">₹${t.total.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div style="margin: 20px 0;">
        <h4>Shipping Address:</h4>
        <p style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">${t.shippingAddress}</p>
      </div>
      
      <p>We'll send you another email when your order ships.</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:`Order Confirmation - ${t.orderId}`,html:r})}async function u(e){let t=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your Brevo integration is working properly!</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        Sent at: ${new Date().toISOString()}
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious for testing purposes.
      </p>
    </div>
  `;await l({to:e,subject:"Email Configuration Test - Herbalicious",html:t})}},54211:(e,t,i)=>{var r;i.d(t,{kg:()=>a}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(r||(r={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:i,message:o,context:a,error:s,userId:n,requestId:l}=e,d=r[i],c=`[${t}] ${d}: ${o}`;return n&&(c+=` | User: ${n}`),l&&(c+=` | Request: ${l}`),a&&Object.keys(a).length>0&&(c+=` | Context: ${JSON.stringify(a)}`),s&&(c+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(c+=`
Stack: ${s.stack}`)),c}log(e,t,i,r){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:i,error:r},a=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(a);break;case 1:console.warn(a);break;case 2:console.info(a);break;case 3:console.debug(a)}else console.log(JSON.stringify(o))}error(e,t,i){this.log(0,e,i,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,i,r){this.info(`API ${e} ${t}`,{...r,userId:i,type:"api_request"})}apiResponse(e,t,i,r,o){this.info(`API ${e} ${t} - ${i}`,{...o,statusCode:i,duration:r,type:"api_response"})}apiError(e,t,i,r,o){this.error(`API ${e} ${t} failed`,i,{...o,userId:r,type:"api_error"})}authSuccess(e,t,i){this.info("Authentication successful",{...i,userId:e,method:t,type:"auth_success"})}authFailure(e,t,i,r){this.warn("Authentication failed",{...r,email:e,method:t,reason:i,type:"auth_failure"})}dbQuery(e,t,i,r){this.debug(`DB ${e} on ${t}`,{...r,operation:e,table:t,duration:i,type:"db_query"})}dbError(e,t,i,r){this.error(`DB ${e} on ${t} failed`,i,{...r,operation:e,table:t,type:"db_error"})}securityEvent(e,t,i){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...i,severity:t,type:"security_event"})}rateLimitHit(e,t,i,r){this.warn("Rate limit exceeded",{...r,identifier:e,limit:t,window:i,type:"rate_limit"})}emailSent(e,t,i,r){this.info("Email sent",{...r,to:e,subject:t,template:i,type:"email_sent"})}emailError(e,t,i,r){this.error("Email failed to send",i,{...r,to:e,subject:t,type:"email_error"})}performance(e,t,i){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...i,operation:e,duration:t,type:"performance"})}}let a=new o},68602:(e,t,i)=>{i.d(t,{B:()=>l});var r=i(3474),o=i(95921),a=i(54211),s=i(53524);class n{async createNotification(e){try{let t=await r._.userPreference.findUnique({where:{userId:e.userId}});if(!this.shouldSendNotification(e.type,t))return a.kg.info(`Notification skipped for user ${e.userId} due to preferences`),null;let i=await r._.notification.create({data:{userId:e.userId,type:e.type,title:e.title,message:e.message,data:e.data||{},priority:e.priority||s.NotificationPriority.NORMAL,expiresAt:e.expiresAt,templateId:e.templateId},include:{user:!0,template:!0}});return e.sendEmail&&t?.emailNotifications!==!1&&await this.sendEmailNotification(i),a.kg.info(`Notification created: ${i.id} for user ${e.userId}`),i}catch(e){throw a.kg.error("Error creating notification:",e),e}}async sendBroadcast(e){try{let t;t=e.userIds&&e.userIds.length>0?await r._.user.findMany({where:{id:{in:e.userIds}},include:{preferences:!0}}):await r._.user.findMany({include:{preferences:!0},where:{OR:[{preferences:{broadcastMessages:!0}},{preferences:null}]}});let i=[];for(let o of t)if(this.shouldSendNotification(e.type,o.preferences)){let t=await r._.notification.create({data:{userId:o.id,type:e.type,title:e.title,message:e.message,data:e.data||{},priority:e.priority||s.NotificationPriority.NORMAL,expiresAt:e.expiresAt,templateId:e.templateId}});i.push(t),e.sendEmail&&o.preferences?.emailNotifications!==!1&&await this.sendEmailNotification({...t,user:o,template:null})}return a.kg.info(`Broadcast sent to ${i.length} users`),i}catch(e){throw a.kg.error("Error sending broadcast:",e),e}}async sendEmailNotification(e){try{let t=e.user;if(!t?.email)throw Error("User email not found");let i=e.template?.emailSubject||e.title,s=e.template?.emailTemplate||this.generateEmailContent(e);await (0,o.Cz)({to:t.email,subject:i,html:s}),await r._.notification.update({where:{id:e.id},data:{emailSent:!0,emailSentAt:new Date}}),a.kg.info(`Email notification sent to ${t.email}`)}catch(t){await r._.notification.update({where:{id:e.id},data:{emailError:t instanceof Error?t.message:"Unknown error"}}),a.kg.error("Error sending email notification:",t)}}shouldSendNotification(e,t){if(!t)return!0;if(!t.inAppNotifications)return!1;switch(e){case s.NotificationType.ORDER_PLACED:case s.NotificationType.ORDER_CONFIRMED:case s.NotificationType.ORDER_PROCESSING:case s.NotificationType.ORDER_SHIPPED:case s.NotificationType.ORDER_DELIVERED:case s.NotificationType.ORDER_CANCELLED:return t.orderNotifications;case s.NotificationType.WISHLIST_ADDED:case s.NotificationType.WISHLIST_REMOVED:return t.wishlistNotifications;case s.NotificationType.PRICE_DROP_ALERT:return t.priceDropAlerts;case s.NotificationType.REVIEW_REQUEST:case s.NotificationType.REVIEW_SUBMITTED:return t.reviewNotifications;case s.NotificationType.ADMIN_MESSAGE:return t.adminMessages;case s.NotificationType.BROADCAST:case s.NotificationType.PROMOTIONAL:return t.broadcastMessages;default:return!0}}generateEmailContent(e){let t=process.env.NEXTAUTH_URL||"http://localhost:3000";return`
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${e.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${e.message}</p>
        </div>
        
        ${e.data&&Object.keys(e.data).length>0?`
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(e.data)}
          </div>
        `:""}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${t}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `}formatNotificationData(e){let t='<ul style="color: #666; line-height: 1.6;">';return e.orderNumber&&(t+=`<li><strong>Order Number:</strong> ${e.orderNumber}</li>`),e.productName&&(t+=`<li><strong>Product:</strong> ${e.productName}</li>`),e.amount&&e.currency&&(t+=`<li><strong>Amount:</strong> ${e.currency} ${e.amount}</li>`),t+="</ul>"}async markAsRead(e,t){try{return await r._.notification.update({where:{id:e,userId:t},data:{isRead:!0}})}catch(e){throw a.kg.error("Error marking notification as read:",e),e}}async markAllAsRead(e){try{return await r._.notification.updateMany({where:{userId:e,isRead:!1},data:{isRead:!0}})}catch(e){throw a.kg.error("Error marking all notifications as read:",e),e}}async getUserNotifications(e,t={}){try{let{page:i=1,limit:o=20,unreadOnly:a=!1,type:s}=t,n={userId:e,OR:[{expiresAt:null},{expiresAt:{gt:new Date}}]};a&&(n.isRead=!1),s&&(n.type=s);let[l,d]=await Promise.all([r._.notification.findMany({where:n,orderBy:{createdAt:"desc"},skip:(i-1)*o,take:o,include:{template:!0}}),r._.notification.count({where:n})]);return{notifications:l,total:d,page:i,limit:o,totalPages:Math.ceil(d/o)}}catch(e){throw a.kg.error("Error getting user notifications:",e),e}}async getUnreadCount(e){try{return await r._.notification.count({where:{userId:e,isRead:!1,OR:[{expiresAt:null},{expiresAt:{gt:new Date}}]}})}catch(e){throw a.kg.error("Error getting unread count:",e),e}}async cleanupOldNotifications(e=30){try{let t=new Date;t.setDate(t.getDate()-e);let i=await r._.notification.deleteMany({where:{createdAt:{lt:t},isRead:!0}});return a.kg.info(`Cleaned up ${i.count} old notifications`),i}catch(e){throw a.kg.error("Error cleaning up notifications:",e),e}}}let l=new n}};