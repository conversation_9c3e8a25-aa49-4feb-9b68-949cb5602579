exports.id=2842,exports.ids=[2842],exports.modules={15075:(e,t,s)=>{Promise.resolve().then(s.bind(s,94494)),Promise.resolve().then(s.bind(s,52807)),Promise.resolve().then(s.bind(s,67520))},10138:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},87697:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var a=s(10326),r=s(17577),o=s(90434),i=s(46226),n=s(35047),l=s(77109),c=s(90748),d=s(6507),u=s(57671),x=s(79635),p=s(95920),m=s(34565),h=s(94494),f=s(52807);let g=({children:e})=>{let t=(0,n.usePathname)(),{data:s}=(0,l.useSession)(),{state:g}=(0,h.j)(),{unreadCount:y}=(0,f.z)(),[j,b]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{b(!0)},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[a.jsx("header",{className:"bg-white shadow-sm sticky top-0 z-40",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto lg:px-8",children:[(0,a.jsxs)("div",{className:"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between",children:[a.jsx("button",{className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(o.default,{href:"/settings",children:a.jsx(c.Z,{className:"w-5 h-5 text-gray-600"})})}),a.jsx(o.default,{href:"/",className:"flex items-center",children:a.jsx(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[40px] w-auto"})}),(0,a.jsxs)(o.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[a.jsx(d.Z,{className:"w-5 h-5 text-gray-600"}),j&&s?.user&&y>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:y>99?"99+":y})]})]}),(0,a.jsxs)("div",{className:"hidden lg:flex px-4 py-3 items-center justify-between",children:[a.jsx(o.default,{href:"/",className:"flex items-center",children:a.jsx(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[60px] w-auto"})}),(0,a.jsxs)("nav",{className:"flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2",children:[a.jsx(o.default,{href:"/",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Home"}),a.jsx(o.default,{href:"/shop",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Shop"}),a.jsx(o.default,{href:"/about",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"About"}),a.jsx(o.default,{href:"/contact",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Contact"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[a.jsx(d.Z,{className:"w-5 h-5 text-gray-600"}),j&&s?.user&&y>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:y>99?"99+":y})]}),(0,a.jsxs)(o.default,{href:"/cart",className:"relative p-2 rounded-full hover:bg-gray-100 transition-colors",children:[a.jsx(u.Z,{className:"w-5 h-5 text-gray-600"}),j&&g.itemCount>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:g.itemCount})]}),a.jsx(o.default,{href:"/profile",className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(x.Z,{className:"w-5 h-5 text-gray-600"})})]})]})]})}),a.jsx("main",{className:"flex-1 pb-20 lg:pb-8",children:a.jsx("div",{className:"max-w-7xl mx-auto lg:px-8",children:a.jsx("div",{className:"max-w-md mx-auto lg:max-w-none",children:e})})}),a.jsx("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden",children:a.jsx("div",{className:"max-w-md mx-auto px-4 py-2",children:(0,a.jsxs)("div",{className:"flex justify-around",children:[(0,a.jsxs)(o.default,{href:"/",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(p.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Home"})]}),(0,a.jsxs)(o.default,{href:"/shop",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/shop"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(m.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Shop"})]}),(0,a.jsxs)(o.default,{href:"/cart",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ${"/cart"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(u.Z,{className:"w-6 h-6 mb-1"}),j&&g.itemCount>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:g.itemCount}),a.jsx("span",{className:"text-xs font-medium",children:"Cart"})]}),(0,a.jsxs)(o.default,{href:"/profile",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/profile"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(x.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Profile"})]})]})})})]})}},94494:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>p,j:()=>m});var a=s(10326),r=s(17577);let o=(0,r.createContext)(null),i=e=>{},n=()=>null,l=(e,t)=>{if(!t||0===t.length)return e;let s=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>`${e.name}:${e.value}`).join("|");return`${e}__${s}`},c=e=>e.variantKey||e.product?.id||e.id,d=()=>n()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},u=(e,t)=>{let s=e.reduce((e,t)=>e+t.product.price*t.quantity,0),a=e.reduce((e,t)=>e+t.quantity,0),r=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:s,itemCount:a,total:s,finalTotal:s-r,totalDiscount:r}},x=(e,t)=>{let s;switch(t.type){case"ADD_ITEM":{let a;let r=l(t.payload.id,t.selectedVariants);if(e.items.find(e=>c(e)===r))a=e.items.map(e=>c(e)===r?{...e,quantity:e.quantity+1,variantKey:r}:e);else{let s={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:r};a=[...e.items,s]}let o=u(a,e.coupons.appliedCoupons);s={...e,items:a,...o,coupons:{...e.coupons,totalDiscount:o.totalDiscount}};break}case"REMOVE_ITEM":{let a=e.items.filter(e=>c(e)!==t.payload),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"UPDATE_QUANTITY":{let a=e.items.map(e=>c(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),r=u(a,e.coupons.appliedCoupons);s={...e,items:a,...r,coupons:{...e.coupons,totalDiscount:r.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let a=[...e.coupons.appliedCoupons,t.payload],r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"REMOVE_COUPON":{let a=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),r=u(e.items,a);s={...e,...r,coupons:{...e.coupons,appliedCoupons:a,totalDiscount:r.totalDiscount}};break}case"CLEAR_COUPONS":{let t=u(e.items,[]);s={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":s={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return i(s),s},p=({children:e})=>{let[t,s]=(0,r.useReducer)(x,d());return a.jsx(o.Provider,{value:{state:t,dispatch:s},children:e})},m=()=>{let e=(0,r.useContext)(o);if(!e)throw Error("useCart must be used within a CartProvider");return e}},52807:(e,t,s)=>{"use strict";s.d(t,{NotificationProvider:()=>l,z:()=>n});var a=s(10326),r=s(17577),o=s(77109);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},l=({children:e})=>{let{data:t,status:s}=(0,o.useSession)(),[n,l]=(0,r.useState)([]),[c,d]=(0,r.useState)(0),[u,x]=(0,r.useState)(!1),[p,m]=(0,r.useState)(null),h=(0,r.useCallback)(async(e={})=>{if(t?.user?.id)try{x(!0),m(null);let t=new URLSearchParams({page:(e.page||1).toString(),limit:(e.limit||10).toString(),...e.unreadOnly&&{unreadOnly:"true"}}),s=await fetch(`/api/notifications?${t}`),a=await s.json();a.success?(l(a.data.notifications),d(a.data.unreadCount)):m(a.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),m("Failed to fetch notifications")}finally{x(!1)}},[t?.user?.id]),f=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&d(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[t?.user?.id]),g=(0,r.useCallback)(async e=>{if(t?.user?.id)try{let t=await fetch(`/api/notifications/${e}/read`,{method:"POST"}),s=await t.json();s.success?(l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),d(e=>Math.max(0,e-1))):m(s.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),m("Failed to mark notification as read")}},[t?.user?.id]),y=(0,r.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(l(e=>e.map(e=>({...e,isRead:!0}))),d(0)):m(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),m("Failed to mark all notifications as read")}},[t?.user?.id]);return(0,r.useEffect)(()=>{"authenticated"===s&&t?.user?.id&&(h({limit:5}),f())},[s,t?.user?.id,h,f]),(0,r.useEffect)(()=>{if(!t?.user?.id)return;let e=setInterval(()=>{f()},3e4);return()=>clearInterval(e)},[t?.user?.id,f]),a.jsx(i.Provider,{value:{notifications:n,unreadCount:c,loading:u,error:p,fetchNotifications:h,markAsRead:g,markAllAsRead:y,refreshUnreadCount:f},children:e})}},67520:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(10326),r=s(77109);function o({children:e}){return a.jsx(r.SessionProvider,{children:e})}},36944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>d,viewport:()=>u});var a=s(19510),r=s(77366),o=s.n(r);s(67272);var i=s(68570);let n=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let d={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},u={width:"device-width",initialScale:1,themeColor:"#16a34a"};function x({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:o().className,children:a.jsx(l,{children:a.jsx(c,{children:a.jsx(n,{children:e})})})})})}},67272:()=>{}};