"use strict";exports.id=5630,exports.ids=[5630],exports.modules={65630:(e,t,n)=>{let i,o;n.d(t,{o4:()=>tk,IX:()=>tQ,O7:()=>tB,Km:()=>t6,Rx:()=>tG,Ry:()=>t0,Z_:()=>tI});var r=n(44764);let a=/^[cC][^\s-]{8,}$/,s=/^[0-9a-z]+$/,u=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,l=/^[0-9a-vA-V]{20}$/,c=/^[A-Za-z0-9]{27}$/,d=/^[a-zA-Z0-9_-]{21}$/,p=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,h=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,f=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,m=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,v=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,_=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,g=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,z=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,y=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,I=/^[A-Za-z0-9_-]*$/,k=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,w=/^\+(?:[0-9]){6,14}[0-9]$/,b="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",$=RegExp(`^${b}$`);function F(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let Z=e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)},T=/^\d+$/,x=/^-?\d+(?:\.\d+)?/i,N=/true|false/i,A=/^[^A-Z]*$/,E=/^[^a-z]*$/;var P=n(57232);let S=r.IF("$ZodCheck",(e,t)=>{var n;e._zod??(e._zod={}),e._zod.def=t,(n=e._zod).onattach??(n.onattach=[])}),O={number:"number",bigint:"bigint",object:"date"},C=r.IF("$ZodCheckLessThan",(e,t)=>{S.init(e,t);let n=O[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,i=(t.inclusive?n.maximum:n.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<i&&(t.inclusive?n.maximum=t.value:n.exclusiveMaximum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value<=t.value:i.value<t.value)||i.issues.push({origin:n,code:"too_big",maximum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),j=r.IF("$ZodCheckGreaterThan",(e,t)=>{S.init(e,t);let n=O[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,i=(t.inclusive?n.minimum:n.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>i&&(t.inclusive?n.minimum=t.value:n.exclusiveMinimum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value>=t.value:i.value>t.value)||i.issues.push({origin:n,code:"too_small",minimum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),R=r.IF("$ZodCheckMultipleOf",(e,t)=>{S.init(e,t),e._zod.onattach.push(e=>{var n;(n=e._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=n=>{if(typeof n.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof n.value?n.value%t.value===BigInt(0):0===P.k8(n.value,t.value))||n.issues.push({origin:typeof n.value,code:"not_multiple_of",divisor:t.value,input:n.value,inst:e,continue:!t.abort})}}),D=r.IF("$ZodCheckNumberFormat",(e,t)=>{S.init(e,t),t.format=t.format||"float64";let n=t.format?.includes("int"),i=n?"int":"number",[o,r]=P.Fl[t.format];e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,i.minimum=o,i.maximum=r,n&&(i.pattern=T)}),e._zod.check=a=>{let s=a.value;if(n){if(!Number.isInteger(s)){a.issues.push({expected:i,format:t.format,code:"invalid_type",input:s,inst:e});return}if(!Number.isSafeInteger(s)){s>0?a.issues.push({input:s,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):a.issues.push({input:s,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort});return}}s<o&&a.issues.push({origin:"number",input:s,code:"too_small",minimum:o,inclusive:!0,inst:e,continue:!t.abort}),s>r&&a.issues.push({origin:"number",input:s,code:"too_big",maximum:r,inst:e})}}),U=r.IF("$ZodCheckMaxLength",(e,t)=>{var n;S.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.NZ(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<n&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let i=n.value;if(i.length<=t.maximum)return;let o=P.XU(i);n.issues.push({origin:o,code:"too_big",maximum:t.maximum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),V=r.IF("$ZodCheckMinLength",(e,t)=>{var n;S.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.NZ(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>n&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let i=n.value;if(i.length>=t.minimum)return;let o=P.XU(i);n.issues.push({origin:o,code:"too_small",minimum:t.minimum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),M=r.IF("$ZodCheckLengthEquals",(e,t)=>{var n;S.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!P.NZ(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let i=n.value,o=i.length;if(o===t.length)return;let r=P.XU(i),a=o>t.length;n.issues.push({origin:r,...a?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),G=r.IF("$ZodCheckStringFormat",(e,t)=>{var n,i;S.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,t.pattern&&(n.patterns??(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?(n=e._zod).check??(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(i=e._zod).check??(i.check=()=>{})}),L=r.IF("$ZodCheckRegex",(e,t)=>{G.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),K=r.IF("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=A),G.init(e,t)}),W=r.IF("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=E),G.init(e,t)}),B=r.IF("$ZodCheckIncludes",(e,t)=>{S.init(e,t);let n=P.yI(t.includes),i=new RegExp("number"==typeof t.position?`^.{${t.position}}${n}`:n);t.pattern=i,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),X=r.IF("$ZodCheckStartsWith",(e,t)=>{S.init(e,t);let n=RegExp(`^${P.yI(t.prefix)}.*`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),Y=r.IF("$ZodCheckEndsWith",(e,t)=>{S.init(e,t);let n=RegExp(`.*${P.yI(t.suffix)}$`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),q=r.IF("$ZodCheckOverwrite",(e,t)=>{S.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class J{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}var Q=n(92478);Q.qc,Q.qc;let H=e=>(t,n,i)=>{let o=i?{...i,async:!1}:{async:!1},a=t._zod.run({value:n,issues:[]},o);if(a instanceof Promise)throw new r.TG;return a.issues.length?{success:!1,error:new(e??Q.h1)(a.issues.map(e=>P.G$(e,o,r.vc())))}:{success:!0,data:a.value}},ee=H(Q.qc),et=e=>async(t,n,i)=>{let o=i?Object.assign(i,{async:!0}):{async:!0},a=t._zod.run({value:n,issues:[]},o);return a instanceof Promise&&(a=await a),a.issues.length?{success:!1,error:new e(a.issues.map(e=>P.G$(e,o,r.vc())))}:{success:!0,data:a.value}},en=et(Q.qc),ei={major:4,minor:0,patch:5},eo=r.IF("$ZodType",(e,t)=>{var n;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=ei;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let n of t._zod.onattach)n(e);if(0===i.length)(n=e._zod).deferred??(n.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,n)=>{let i,o=P.Ko(e);for(let a of t){if(a._zod.def.when){if(!a._zod.def.when(e))continue}else if(o)continue;let t=e.issues.length,s=a._zod.check(e);if(s instanceof Promise&&n?.async===!1)throw new r.TG;if(i||s instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await s,e.issues.length===t||o||(o=P.Ko(e,t))});else{if(e.issues.length===t)continue;o||(o=P.Ko(e,t))}}return i?i.then(()=>e):e};e._zod.run=(n,o)=>{let a=e._zod.parse(n,o);if(a instanceof Promise){if(!1===o.async)throw new r.TG;return a.then(e=>t(e,i,o))}return t(a,i,o)}}e["~standard"]={validate:t=>{try{let n=ee(e,t);return n.success?{value:n.data}:{issues:n.error?.issues}}catch(n){return en(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),er=r.IF("$ZodString",(e,t)=>{eo.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??Z(e._zod.bag),e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),ea=r.IF("$ZodStringFormat",(e,t)=>{G.init(e,t),er.init(e,t)}),es=r.IF("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=h),ea.init(e,t)}),eu=r.IF("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=f(e))}else t.pattern??(t.pattern=f());ea.init(e,t)}),el=r.IF("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=m),ea.init(e,t)}),ec=r.IF("$ZodURL",(e,t)=>{ea.init(e,t),e._zod.check=n=>{try{let i=n.value,o=new URL(i),r=o.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(o.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:k.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),!i.endsWith("/")&&r.endsWith("/")?n.value=r.slice(0,-1):n.value=r;return}catch(i){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),ed=r.IF("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),ea.init(e,t)}),ep=r.IF("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=d),ea.init(e,t)}),eh=r.IF("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=a),ea.init(e,t)}),ef=r.IF("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=s),ea.init(e,t)}),em=r.IF("$ZodULID",(e,t)=>{t.pattern??(t.pattern=u),ea.init(e,t)}),ev=r.IF("$ZodXID",(e,t)=>{t.pattern??(t.pattern=l),ea.init(e,t)}),e_=r.IF("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=c),ea.init(e,t)}),eg=r.IF("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=F({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-]\\d{2}:\\d{2})");let i=`${t}(?:${n.join("|")})`;return RegExp(`^${b}T(?:${i})$`)}(t)),ea.init(e,t)}),ez=r.IF("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=$),ea.init(e,t)}),ey=r.IF("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${F(t)}$`)),ea.init(e,t)}),eI=r.IF("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=p),ea.init(e,t)}),ek=r.IF("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=v),ea.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),ew=r.IF("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=_),ea.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL(`http://[${n.value}]`)}catch{n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),eb=r.IF("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=g),ea.init(e,t)}),e$=r.IF("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=z),ea.init(e,t),e._zod.check=n=>{let[i,o]=n.value.split("/");try{if(!o)throw Error();let e=Number(o);if(`${e}`!==o||e<0||e>128)throw Error();new URL(`http://[${i}]`)}catch{n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function eF(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let eZ=r.IF("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=y),ea.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{eF(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}}),eT=r.IF("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=I),ea.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{!function(e){if(!I.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return eF(t.padEnd(4*Math.ceil(t.length/4),"="))}(n.value)&&n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),ex=r.IF("$ZodE164",(e,t)=>{t.pattern??(t.pattern=w),ea.init(e,t)}),eN=r.IF("$ZodJWT",(e,t)=>{ea.init(e,t),e._zod.check=n=>{!function(e,t=null){try{let n=e.split(".");if(3!==n.length)return!1;let[i]=n;if(!i)return!1;let o=JSON.parse(atob(i));if("typ"in o&&o?.typ!=="JWT"||!o.alg||t&&(!("alg"in o)||o.alg!==t))return!1;return!0}catch{return!1}}(n.value,t.alg)&&n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),eA=r.IF("$ZodNumber",(e,t)=>{eo.init(e,t),e._zod.pattern=e._zod.bag.pattern??x,e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=Number(n.value)}catch(e){}let o=n.value;if("number"==typeof o&&!Number.isNaN(o)&&Number.isFinite(o))return n;let r="number"==typeof o?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:o,inst:e,...r?{received:r}:{}}),n}}),eE=r.IF("$ZodNumber",(e,t)=>{D.init(e,t),eA.init(e,t)}),eP=r.IF("$ZodBoolean",(e,t)=>{eo.init(e,t),e._zod.pattern=N,e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=!!n.value}catch(e){}let o=n.value;return"boolean"==typeof o||n.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:e}),n}}),eS=r.IF("$ZodUnknown",(e,t)=>{eo.init(e,t),e._zod.parse=e=>e}),eO=r.IF("$ZodNever",(e,t)=>{eo.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function eC(e,t,n){e.issues.length&&t.issues.push(...P.Cr(n,e.issues)),t.value[n]=e.value}let ej=r.IF("$ZodArray",(e,t)=>{eo.init(e,t),e._zod.parse=(n,i)=>{let o=n.value;if(!Array.isArray(o))return n.issues.push({expected:"array",code:"invalid_type",input:o,inst:e}),n;n.value=Array(o.length);let r=[];for(let e=0;e<o.length;e++){let a=o[e],s=t.element._zod.run({value:a,issues:[]},i);s instanceof Promise?r.push(s.then(t=>eC(t,n,e))):eC(s,n,e)}return r.length?Promise.all(r).then(()=>n):n}});function eR(e,t,n){e.issues.length&&t.issues.push(...P.Cr(n,e.issues)),t.value[n]=e.value}function eD(e,t,n,i){e.issues.length?void 0===i[n]?n in i?t.value[n]=void 0:t.value[n]=e.value:t.issues.push(...P.Cr(n,e.issues)):void 0===e.value?n in i&&(t.value[n]=void 0):t.value[n]=e.value}let eU=r.IF("$ZodObject",(e,t)=>{let n,i;eo.init(e,t);let o=P.HX(()=>{let e=Object.keys(t.shape);for(let n of e)if(!(t.shape[n]instanceof eo))throw Error(`Invalid element at key "${n}": expected a Zod schema`);let n=P.bX(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(n)}});P.To(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let i=e[t]._zod;if(i.values)for(let e of(n[t]??(n[t]=new Set),i.values))n[t].add(e)}return n});let a=e=>{let t=new J(["shape","payload","ctx"]),n=o.value,i=e=>{let t=P.v_(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let r=Object.create(null),a=0;for(let e of n.keys)r[e]=`key_${a++}`;for(let e of(t.write("const newResult = {}"),n.keys))if(n.optionalKeys.has(e)){let n=r[e];t.write(`const ${n} = ${i(e)};`);let o=P.v_(e);t.write(`
        if (${n}.issues.length) {
          if (input[${o}] === undefined) {
            if (${o} in input) {
              newResult[${o}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${n}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${o}, ...iss.path] : [${o}],
              }))
            );
          }
        } else if (${n}.value === undefined) {
          if (${o} in input) newResult[${o}] = undefined;
        } else {
          newResult[${o}] = ${n}.value;
        }
        `)}else{let n=r[e];t.write(`const ${n} = ${i(e)};`),t.write(`
          if (${n}.issues.length) payload.issues = payload.issues.concat(${n}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${P.v_(e)}, ...iss.path] : [${P.v_(e)}]
          })));`),t.write(`newResult[${P.v_(e)}] = ${n}.value`)}t.write("payload.value = newResult;"),t.write("return payload;");let s=t.compile();return(t,n)=>s(e,t,n)},s=P.Kn,u=!r.w6.jitless,l=P.k1,c=u&&l.value,d=t.catchall;e._zod.parse=(r,l)=>{i??(i=o.value);let p=r.value;if(!s(p))return r.issues.push({expected:"object",code:"invalid_type",input:p,inst:e}),r;let h=[];if(u&&c&&l?.async===!1&&!0!==l.jitless)n||(n=a(t.shape)),r=n(r,l);else{r.value={};let e=i.shape;for(let t of i.keys){let n=e[t],i=n._zod.run({value:p[t],issues:[]},l),o="optional"===n._zod.optin&&"optional"===n._zod.optout;i instanceof Promise?h.push(i.then(e=>o?eD(e,r,t,p):eR(e,r,t))):o?eD(i,r,t,p):eR(i,r,t)}}if(!d)return h.length?Promise.all(h).then(()=>r):r;let f=[],m=i.keySet,v=d._zod,_=v.def.type;for(let e of Object.keys(p)){if(m.has(e))continue;if("never"===_){f.push(e);continue}let t=v.run({value:p[e],issues:[]},l);t instanceof Promise?h.push(t.then(t=>eR(t,r,e))):eR(t,r,e)}return(f.length&&r.issues.push({code:"unrecognized_keys",keys:f,input:p,inst:e}),h.length)?Promise.all(h).then(()=>r):r}});function eV(e,t,n,i){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:n,errors:e.map(e=>e.issues.map(e=>P.G$(e,i,r.vc())))}),t}let eM=r.IF("$ZodUnion",(e,t)=>{eo.init(e,t),P.To(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),P.To(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),P.To(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),P.To(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>P.ZA(e.source)).join("|")})$`)}}),e._zod.parse=(n,i)=>{let o=!1,r=[];for(let e of t.options){let t=e._zod.run({value:n.value,issues:[]},i);if(t instanceof Promise)r.push(t),o=!0;else{if(0===t.issues.length)return t;r.push(t)}}return o?Promise.all(r).then(t=>eV(t,n,e,i)):eV(r,n,e,i)}}),eG=r.IF("$ZodIntersection",(e,t)=>{eo.init(e,t),e._zod.parse=(e,n)=>{let i=e.value,o=t.left._zod.run({value:i,issues:[]},n),r=t.right._zod.run({value:i,issues:[]},n);return o instanceof Promise||r instanceof Promise?Promise.all([o,r]).then(([t,n])=>eL(e,t,n)):eL(e,o,r)}});function eL(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),P.Ko(e))return e;let i=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(P.PO(t)&&P.PO(n)){let i=Object.keys(n),o=Object.keys(t).filter(e=>-1!==i.indexOf(e)),r={...t,...n};for(let i of o){let o=e(t[i],n[i]);if(!o.valid)return{valid:!1,mergeErrorPath:[i,...o.mergeErrorPath]};r[i]=o.data}return{valid:!0,data:r}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let i=[];for(let o=0;o<t.length;o++){let r=e(t[o],n[o]);if(!r.valid)return{valid:!1,mergeErrorPath:[o,...r.mergeErrorPath]};i.push(r.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!i.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(i.mergeErrorPath)}`);return e.value=i.data,e}let eK=r.IF("$ZodEnum",(e,t)=>{eo.init(e,t);let n=P.B8(t.entries);e._zod.values=new Set(n),e._zod.pattern=RegExp(`^(${n.filter(e=>P.A0.has(typeof e)).map(e=>"string"==typeof e?P.yI(e):e.toString()).join("|")})$`),e._zod.parse=(t,i)=>{let o=t.value;return e._zod.values.has(o)||t.issues.push({code:"invalid_value",values:n,input:o,inst:e}),t}}),eW=r.IF("$ZodTransform",(e,t)=>{eo.init(e,t),e._zod.parse=(e,n)=>{let i=t.transform(e.value,e);if(n.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new r.TG;return e.value=i,e}}),eB=r.IF("$ZodOptional",(e,t)=>{eo.init(e,t),e._zod.optin="optional",e._zod.optout="optional",P.To(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),P.To(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${P.ZA(e.source)})?$`):void 0}),e._zod.parse=(e,n)=>"optional"===t.innerType._zod.optin?t.innerType._zod.run(e,n):void 0===e.value?e:t.innerType._zod.run(e,n)}),eX=r.IF("$ZodNullable",(e,t)=>{eo.init(e,t),P.To(e._zod,"optin",()=>t.innerType._zod.optin),P.To(e._zod,"optout",()=>t.innerType._zod.optout),P.To(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${P.ZA(e.source)}|null)$`):void 0}),P.To(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),eY=r.IF("$ZodDefault",(e,t)=>{eo.init(e,t),e._zod.optin="optional",P.To(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(e=>eq(e,t)):eq(i,t)}});function eq(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let eJ=r.IF("$ZodPrefault",(e,t)=>{eo.init(e,t),e._zod.optin="optional",P.To(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),eQ=r.IF("$ZodNonOptional",(e,t)=>{eo.init(e,t),P.To(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,i)=>{let o=t.innerType._zod.run(n,i);return o instanceof Promise?o.then(t=>eH(t,e)):eH(o,e)}});function eH(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let e0=r.IF("$ZodCatch",(e,t)=>{eo.init(e,t),e._zod.optin="optional",P.To(e._zod,"optout",()=>t.innerType._zod.optout),P.To(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>P.G$(e,n,r.vc()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>P.G$(e,n,r.vc()))},input:e.value}),e.issues=[]),e)}}),e1=r.IF("$ZodPipe",(e,t)=>{eo.init(e,t),P.To(e._zod,"values",()=>t.in._zod.values),P.To(e._zod,"optin",()=>t.in._zod.optin),P.To(e._zod,"optout",()=>t.out._zod.optout),P.To(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{let i=t.in._zod.run(e,n);return i instanceof Promise?i.then(e=>e9(e,t,n)):e9(i,t,n)}});function e9(e,t,n){return P.Ko(e)?e:t.out._zod.run({value:e.value,issues:e.issues},n)}let e4=r.IF("$ZodReadonly",(e,t)=>{eo.init(e,t),P.To(e._zod,"propValues",()=>t.innerType._zod.propValues),P.To(e._zod,"values",()=>t.innerType._zod.values),P.To(e._zod,"optin",()=>t.innerType._zod.optin),P.To(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(e6):e6(i)}});function e6(e){return e.value=Object.freeze(e.value),e}let e2=r.IF("$ZodCustom",(e,t)=>{S.init(e,t),eo.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let i=n.value,o=t.fn(i);if(o instanceof Promise)return o.then(t=>e5(t,n,i,e));e5(o,n,i,e)}});function e5(e,t,n,i){if(!e){let e={code:"custom",input:n,inst:i,path:[...i._zod.def.path??[]],continue:!i._zod.def.abort};i._zod.def.params&&(e.params=i._zod.def.params),t.issues.push(P.Q_(e))}}Symbol("ZodOutput"),Symbol("ZodInput");class e8{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let n=t[0];if(this._map.set(e,n),n&&"object"==typeof n&&"id"in n){if(this._idmap.has(n.id))throw Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let n={...this.get(t)??{}};return delete n.id,{...n,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let e3=new e8;function e7(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...P.de(t)})}function te(e,t){return new C({check:"less_than",...P.de(t),value:e,inclusive:!1})}function tt(e,t){return new C({check:"less_than",...P.de(t),value:e,inclusive:!0})}function tn(e,t){return new j({check:"greater_than",...P.de(t),value:e,inclusive:!1})}function ti(e,t){return new j({check:"greater_than",...P.de(t),value:e,inclusive:!0})}function to(e,t){return new R({check:"multiple_of",...P.de(t),value:e})}function tr(e,t){return new U({check:"max_length",...P.de(t),maximum:e})}function ta(e,t){return new V({check:"min_length",...P.de(t),minimum:e})}function ts(e,t){return new M({check:"length_equals",...P.de(t),length:e})}function tu(e){return new q({check:"overwrite",tx:e})}let tl=r.IF("ZodISODateTime",(e,t)=>{eg.init(e,t),tk.init(e,t)}),tc=r.IF("ZodISODate",(e,t)=>{ez.init(e,t),tk.init(e,t)}),td=r.IF("ZodISOTime",(e,t)=>{ey.init(e,t),tk.init(e,t)}),tp=r.IF("ZodISODuration",(e,t)=>{eI.init(e,t),tk.init(e,t)});var th=n(29489);let tf=(i=th.N,(e,t,n,o)=>{let a=n?Object.assign(n,{async:!1}):{async:!1},s=e._zod.run({value:t,issues:[]},a);if(s instanceof Promise)throw new r.TG;if(s.issues.length){let e=new(o?.Err??i)(s.issues.map(e=>P.G$(e,a,r.vc())));throw P.zw(e,o?.callee),e}return s.value}),tm=(o=th.N,async(e,t,n,i)=>{let a=n?Object.assign(n,{async:!0}):{async:!0},s=e._zod.run({value:t,issues:[]},a);if(s instanceof Promise&&(s=await s),s.issues.length){let e=new(i?.Err??o)(s.issues.map(e=>P.G$(e,a,r.vc())));throw P.zw(e,i?.callee),e}return s.value}),tv=H(th.N),t_=et(th.N),tg=r.IF("ZodType",(e,t)=>(eo.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...n)=>e.clone({...t,checks:[...t.checks??[],...n.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,n)=>P.d9(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>tf(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>tv(e,t,n),e.parseAsync=async(t,n)=>tm(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>t_(e,t,n),e.spa=e.safeParseAsync,e.refine=(t,n)=>e.check(function(e,t={}){return new ns({type:"custom",check:"custom",fn:e,...P.de(t)})}(t,n)),e.superRefine=t=>e.check(function(e){let t=function(e){let t=new S({check:"custom"});return t._zod.check=e,t}(n=>(n.addIssue=e=>{"string"==typeof e?n.issues.push(P.Q_(e,n.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=n.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),n.issues.push(P.Q_(e)))},e(n.value,n)));return t}(t)),e.overwrite=t=>e.check(tu(t)),e.optional=()=>t8(e),e.nullable=()=>t7(e),e.nullish=()=>t8(t7(e)),e.nonoptional=t=>new nn({type:"nonoptional",innerType:e,...P.de(t)}),e.array=()=>tQ(e),e.or=t=>{var n;return new t1({type:"union",options:[e,t],...P.de(void 0)})},e.and=t=>new t9({type:"intersection",left:e,right:t}),e.transform=t=>nr(e,new t2({type:"transform",transform:t})),e.default=t=>(function(e,t){return new ne({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.prefault=t=>(function(e,t){return new nt({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.catch=t=>(function(e,t){return new ni({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>nr(e,t),e.readonly=()=>new na({type:"readonly",innerType:e}),e.describe=t=>{let n=e.clone();return e3.add(n,{description:t}),n},Object.defineProperty(e,"description",{get:()=>e3.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return e3.get(e);let n=e.clone();return e3.add(n,t[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),tz=r.IF("_ZodString",(e,t)=>{er.init(e,t),tg.init(e,t);let n=e._zod.bag;e.format=n.format??null,e.minLength=n.minimum??null,e.maxLength=n.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new L({check:"string_format",format:"regex",...P.de(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new B({check:"string_format",format:"includes",...P.de(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new X({check:"string_format",format:"starts_with",...P.de(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new Y({check:"string_format",format:"ends_with",...P.de(t),suffix:e})}(...t)),e.min=(...t)=>e.check(ta(...t)),e.max=(...t)=>e.check(tr(...t)),e.length=(...t)=>e.check(ts(...t)),e.nonempty=(...t)=>e.check(ta(1,...t)),e.lowercase=t=>e.check(new K({check:"string_format",format:"lowercase",...P.de(t)})),e.uppercase=t=>e.check(new W({check:"string_format",format:"uppercase",...P.de(t)})),e.trim=()=>e.check(tu(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return tu(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(tu(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(tu(e=>e.toUpperCase()))}),ty=r.IF("ZodString",(e,t)=>{er.init(e,t),tz.init(e,t),e.email=t=>e.check(new tw({type:"string",format:"email",check:"string_format",abort:!1,...P.de(t)})),e.url=t=>e.check(new tF({type:"string",format:"url",check:"string_format",abort:!1,...P.de(t)})),e.jwt=t=>e.check(new tV({type:"string",format:"jwt",check:"string_format",abort:!1,...P.de(t)})),e.emoji=t=>e.check(new tZ({type:"string",format:"emoji",check:"string_format",abort:!1,...P.de(t)})),e.guid=t=>e.check(e7(tb,t)),e.uuid=t=>e.check(new t$({type:"string",format:"uuid",check:"string_format",abort:!1,...P.de(t)})),e.uuidv4=t=>e.check(new t$({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...P.de(t)})),e.uuidv6=t=>e.check(new t$({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...P.de(t)})),e.uuidv7=t=>e.check(new t$({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...P.de(t)})),e.nanoid=t=>e.check(new tT({type:"string",format:"nanoid",check:"string_format",abort:!1,...P.de(t)})),e.guid=t=>e.check(e7(tb,t)),e.cuid=t=>e.check(new tx({type:"string",format:"cuid",check:"string_format",abort:!1,...P.de(t)})),e.cuid2=t=>e.check(new tN({type:"string",format:"cuid2",check:"string_format",abort:!1,...P.de(t)})),e.ulid=t=>e.check(new tA({type:"string",format:"ulid",check:"string_format",abort:!1,...P.de(t)})),e.base64=t=>e.check(new tR({type:"string",format:"base64",check:"string_format",abort:!1,...P.de(t)})),e.base64url=t=>e.check(new tD({type:"string",format:"base64url",check:"string_format",abort:!1,...P.de(t)})),e.xid=t=>e.check(new tE({type:"string",format:"xid",check:"string_format",abort:!1,...P.de(t)})),e.ksuid=t=>e.check(new tP({type:"string",format:"ksuid",check:"string_format",abort:!1,...P.de(t)})),e.ipv4=t=>e.check(new tS({type:"string",format:"ipv4",check:"string_format",abort:!1,...P.de(t)})),e.ipv6=t=>e.check(new tO({type:"string",format:"ipv6",check:"string_format",abort:!1,...P.de(t)})),e.cidrv4=t=>e.check(new tC({type:"string",format:"cidrv4",check:"string_format",abort:!1,...P.de(t)})),e.cidrv6=t=>e.check(new tj({type:"string",format:"cidrv6",check:"string_format",abort:!1,...P.de(t)})),e.e164=t=>e.check(new tU({type:"string",format:"e164",check:"string_format",abort:!1,...P.de(t)})),e.datetime=t=>{var n;return e.check((n=t,new tl({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...P.de(n)})))},e.date=t=>{var n;return e.check((n=t,new tc({type:"string",format:"date",check:"string_format",...P.de(n)})))},e.time=t=>{var n;return e.check((n=t,new td({type:"string",format:"time",check:"string_format",precision:null,...P.de(n)})))},e.duration=t=>{var n;return e.check((n=t,new tp({type:"string",format:"duration",check:"string_format",...P.de(n)})))}});function tI(e){return new ty({type:"string",...P.de(e)})}let tk=r.IF("ZodStringFormat",(e,t)=>{ea.init(e,t),tz.init(e,t)}),tw=r.IF("ZodEmail",(e,t)=>{el.init(e,t),tk.init(e,t)}),tb=r.IF("ZodGUID",(e,t)=>{es.init(e,t),tk.init(e,t)}),t$=r.IF("ZodUUID",(e,t)=>{eu.init(e,t),tk.init(e,t)}),tF=r.IF("ZodURL",(e,t)=>{ec.init(e,t),tk.init(e,t)}),tZ=r.IF("ZodEmoji",(e,t)=>{ed.init(e,t),tk.init(e,t)}),tT=r.IF("ZodNanoID",(e,t)=>{ep.init(e,t),tk.init(e,t)}),tx=r.IF("ZodCUID",(e,t)=>{eh.init(e,t),tk.init(e,t)}),tN=r.IF("ZodCUID2",(e,t)=>{ef.init(e,t),tk.init(e,t)}),tA=r.IF("ZodULID",(e,t)=>{em.init(e,t),tk.init(e,t)}),tE=r.IF("ZodXID",(e,t)=>{ev.init(e,t),tk.init(e,t)}),tP=r.IF("ZodKSUID",(e,t)=>{e_.init(e,t),tk.init(e,t)}),tS=r.IF("ZodIPv4",(e,t)=>{ek.init(e,t),tk.init(e,t)}),tO=r.IF("ZodIPv6",(e,t)=>{ew.init(e,t),tk.init(e,t)}),tC=r.IF("ZodCIDRv4",(e,t)=>{eb.init(e,t),tk.init(e,t)}),tj=r.IF("ZodCIDRv6",(e,t)=>{e$.init(e,t),tk.init(e,t)}),tR=r.IF("ZodBase64",(e,t)=>{eZ.init(e,t),tk.init(e,t)}),tD=r.IF("ZodBase64URL",(e,t)=>{eT.init(e,t),tk.init(e,t)}),tU=r.IF("ZodE164",(e,t)=>{ex.init(e,t),tk.init(e,t)}),tV=r.IF("ZodJWT",(e,t)=>{eN.init(e,t),tk.init(e,t)}),tM=r.IF("ZodNumber",(e,t)=>{eA.init(e,t),tg.init(e,t),e.gt=(t,n)=>e.check(tn(t,n)),e.gte=(t,n)=>e.check(ti(t,n)),e.min=(t,n)=>e.check(ti(t,n)),e.lt=(t,n)=>e.check(te(t,n)),e.lte=(t,n)=>e.check(tt(t,n)),e.max=(t,n)=>e.check(tt(t,n)),e.int=t=>e.check(tK(t)),e.safe=t=>e.check(tK(t)),e.positive=t=>e.check(tn(0,t)),e.nonnegative=t=>e.check(ti(0,t)),e.negative=t=>e.check(te(0,t)),e.nonpositive=t=>e.check(tt(0,t)),e.multipleOf=(t,n)=>e.check(to(t,n)),e.step=(t,n)=>e.check(to(t,n)),e.finite=()=>e;let n=e._zod.bag;e.minValue=Math.max(n.minimum??Number.NEGATIVE_INFINITY,n.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(n.maximum??Number.POSITIVE_INFINITY,n.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(n.format??"").includes("int")||Number.isSafeInteger(n.multipleOf??.5),e.isFinite=!0,e.format=n.format??null});function tG(e){return new tM({type:"number",checks:[],...P.de(e)})}let tL=r.IF("ZodNumberFormat",(e,t)=>{eE.init(e,t),tM.init(e,t)});function tK(e){return new tL({type:"number",check:"number_format",abort:!1,format:"safeint",...P.de(e)})}let tW=r.IF("ZodBoolean",(e,t)=>{eP.init(e,t),tg.init(e,t)});function tB(e){return new tW({type:"boolean",...P.de(e)})}let tX=r.IF("ZodUnknown",(e,t)=>{eS.init(e,t),tg.init(e,t)});function tY(){return new tX({type:"unknown"})}let tq=r.IF("ZodNever",(e,t)=>{eO.init(e,t),tg.init(e,t)}),tJ=r.IF("ZodArray",(e,t)=>{ej.init(e,t),tg.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(ta(t,n)),e.nonempty=t=>e.check(ta(1,t)),e.max=(t,n)=>e.check(tr(t,n)),e.length=(t,n)=>e.check(ts(t,n)),e.unwrap=()=>e.element});function tQ(e,t){return new tJ({type:"array",element:e,...P.de(t)})}let tH=r.IF("ZodObject",(e,t)=>{eU.init(e,t),tg.init(e,t),P.To(e,"shape",()=>t.shape),e.keyof=()=>t6(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:tY()}),e.loose=()=>e.clone({...e._zod.def,catchall:tY()}),e.strict=()=>{var t;return e.clone({...e._zod.def,catchall:new tq({type:"never",...P.de(void 0)})})},e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>P.l7(e,t),e.merge=t=>P.TS(e,t),e.pick=t=>P.ei(e,t),e.omit=t=>P.CE(e,t),e.partial=(...t)=>P.r$(t5,e,t[0]),e.required=(...t)=>P.C1(nn,e,t[0])});function t0(e,t){return new tH({type:"object",get shape(){return P.DW(this,"shape",{...e}),this.shape},...P.de(t)})}let t1=r.IF("ZodUnion",(e,t)=>{eM.init(e,t),tg.init(e,t),e.options=t.options}),t9=r.IF("ZodIntersection",(e,t)=>{eG.init(e,t),tg.init(e,t)}),t4=r.IF("ZodEnum",(e,t)=>{eK.init(e,t),tg.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,i)=>{let o={};for(let i of e)if(n.has(i))o[i]=t.entries[i];else throw Error(`Key ${i} not found in enum`);return new t4({...t,checks:[],...P.de(i),entries:o})},e.exclude=(e,i)=>{let o={...t.entries};for(let t of e)if(n.has(t))delete o[t];else throw Error(`Key ${t} not found in enum`);return new t4({...t,checks:[],...P.de(i),entries:o})}});function t6(e,t){return new t4({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...P.de(t)})}let t2=r.IF("ZodTransform",(e,t)=>{eW.init(e,t),tg.init(e,t),e._zod.parse=(n,i)=>{n.addIssue=i=>{"string"==typeof i?n.issues.push(P.Q_(i,n.value,t)):(i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=n.value),i.inst??(i.inst=e),i.continue??(i.continue=!0),n.issues.push(P.Q_(i)))};let o=t.transform(n.value,n);return o instanceof Promise?o.then(e=>(n.value=e,n)):(n.value=o,n)}}),t5=r.IF("ZodOptional",(e,t)=>{eB.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType});function t8(e){return new t5({type:"optional",innerType:e})}let t3=r.IF("ZodNullable",(e,t)=>{eX.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType});function t7(e){return new t3({type:"nullable",innerType:e})}let ne=r.IF("ZodDefault",(e,t)=>{eY.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),nt=r.IF("ZodPrefault",(e,t)=>{eJ.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nn=r.IF("ZodNonOptional",(e,t)=>{eQ.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType}),ni=r.IF("ZodCatch",(e,t)=>{e0.init(e,t),tg.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),no=r.IF("ZodPipe",(e,t)=>{e1.init(e,t),tg.init(e,t),e.in=t.in,e.out=t.out});function nr(e,t){return new no({type:"pipe",in:e,out:t})}let na=r.IF("ZodReadonly",(e,t)=>{e4.init(e,t),tg.init(e,t)}),ns=r.IF("ZodCustom",(e,t)=>{e2.init(e,t),tg.init(e,t)})}};