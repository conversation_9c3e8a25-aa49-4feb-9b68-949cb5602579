exports.id=6879,exports.ids=[6879],exports.modules={75671:(e,t,a)=>{Promise.resolve().then(a.bind(a,2802))},15075:(e,t,a)=>{Promise.resolve().then(a.bind(a,94494)),Promise.resolve().then(a.bind(a,52807)),Promise.resolve().then(a.bind(a,67520))},10138:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},2802:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var r=a(10326),o=a(17577),i=a.n(o),s=a(90434),n=a(35047),l=a(77109),c=a(24319),d=a(48705),u=a(34565),p=a(57671),m=a(24061),f=a(40765),h=a(40617),x=a(6507),b=a(5932),g=a(71709),y=a(95920),C=a(88378),v=a(94019),w=a(90748),j=a(53080),N=a(71810);let P=({children:e})=>{let t=(0,n.usePathname)(),[a,o]=i().useState(!1),[P,k]=i().useState(!1),S=async()=>{if(!P)try{k(!0),o(!1),await (0,l.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},D=[{href:"/admin",label:"Dashboard",icon:c.Z},{href:"/admin/products",label:"Products",icon:d.Z},{href:"/admin/categories",label:"Categories",icon:u.Z},{href:"/admin/orders",label:"Orders",icon:p.Z},{href:"/admin/customers",label:"Customers",icon:m.Z},{href:"/admin/coupons",label:"Coupons",icon:f.Z},{href:"/admin/reviews",label:"Reviews",icon:h.Z},{href:"/admin/notifications",label:"Notifications",icon:x.Z},{href:"/admin/newsletter",label:"Newsletter",icon:b.Z},{href:"/admin/media",label:"Media",icon:g.Z},{href:"/admin/homepage",label:"Homepage",icon:y.Z},{href:"/admin/settings",label:"Settings",icon:C.Z}],U=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:r.jsx("button",{onClick:()=>o(!a),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:a?r.jsx(v.Z,{className:"w-6 h-6 text-gray-600"}):r.jsx(w.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${a?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:r.jsx(j.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),r.jsx("nav",{className:"mt-6 px-3",children:D.map(e=>{let t=e.icon,a=U(e.href);return(0,r.jsxs)(s.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${a?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>o(!1),children:[r.jsx(t,{className:`w-5 h-5 mr-3 ${a?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[r.jsx(s.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,r.jsxs)("button",{onClick:S,disabled:P,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx(N.Z,{className:"w-4 h-4 mr-2"}),P?"Logging out...":"Logout"]})]})]}),r.jsx("main",{className:"lg:ml-64",children:r.jsx("div",{className:"p-4 lg:p-8",children:e})}),a&&r.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>o(!1)})]})}},94494:(e,t,a)=>{"use strict";a.d(t,{CartProvider:()=>m,j:()=>f});var r=a(10326),o=a(17577);let i=(0,o.createContext)(null),s=e=>{},n=()=>null,l=(e,t)=>{if(!t||0===t.length)return e;let a=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>`${e.name}:${e.value}`).join("|");return`${e}__${a}`},c=e=>e.variantKey||e.product?.id||e.id,d=()=>n()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},u=(e,t)=>{let a=e.reduce((e,t)=>e+t.product.price*t.quantity,0),r=e.reduce((e,t)=>e+t.quantity,0),o=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:a,itemCount:r,total:a,finalTotal:a-o,totalDiscount:o}},p=(e,t)=>{let a;switch(t.type){case"ADD_ITEM":{let r;let o=l(t.payload.id,t.selectedVariants);if(e.items.find(e=>c(e)===o))r=e.items.map(e=>c(e)===o?{...e,quantity:e.quantity+1,variantKey:o}:e);else{let a={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:o};r=[...e.items,a]}let i=u(r,e.coupons.appliedCoupons);a={...e,items:r,...i,coupons:{...e.coupons,totalDiscount:i.totalDiscount}};break}case"REMOVE_ITEM":{let r=e.items.filter(e=>c(e)!==t.payload),o=u(r,e.coupons.appliedCoupons);a={...e,items:r,...o,coupons:{...e.coupons,totalDiscount:o.totalDiscount}};break}case"UPDATE_QUANTITY":{let r=e.items.map(e=>c(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),o=u(r,e.coupons.appliedCoupons);a={...e,items:r,...o,coupons:{...e.coupons,totalDiscount:o.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let r=[...e.coupons.appliedCoupons,t.payload],o=u(e.items,r);a={...e,...o,coupons:{...e.coupons,appliedCoupons:r,totalDiscount:o.totalDiscount}};break}case"REMOVE_COUPON":{let r=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),o=u(e.items,r);a={...e,...o,coupons:{...e.coupons,appliedCoupons:r,totalDiscount:o.totalDiscount}};break}case"CLEAR_COUPONS":{let t=u(e.items,[]);a={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":a={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return s(a),a},m=({children:e})=>{let[t,a]=(0,o.useReducer)(p,d());return r.jsx(i.Provider,{value:{state:t,dispatch:a},children:e})},f=()=>{let e=(0,o.useContext)(i);if(!e)throw Error("useCart must be used within a CartProvider");return e}},52807:(e,t,a)=>{"use strict";a.d(t,{NotificationProvider:()=>l,z:()=>n});var r=a(10326),o=a(17577),i=a(77109);let s=(0,o.createContext)(void 0),n=()=>{let e=(0,o.useContext)(s);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},l=({children:e})=>{let{data:t,status:a}=(0,i.useSession)(),[n,l]=(0,o.useState)([]),[c,d]=(0,o.useState)(0),[u,p]=(0,o.useState)(!1),[m,f]=(0,o.useState)(null),h=(0,o.useCallback)(async(e={})=>{if(t?.user?.id)try{p(!0),f(null);let t=new URLSearchParams({page:(e.page||1).toString(),limit:(e.limit||10).toString(),...e.unreadOnly&&{unreadOnly:"true"}}),a=await fetch(`/api/notifications?${t}`),r=await a.json();r.success?(l(r.data.notifications),d(r.data.unreadCount)):f(r.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),f("Failed to fetch notifications")}finally{p(!1)}},[t?.user?.id]),x=(0,o.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&d(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[t?.user?.id]),b=(0,o.useCallback)(async e=>{if(t?.user?.id)try{let t=await fetch(`/api/notifications/${e}/read`,{method:"POST"}),a=await t.json();a.success?(l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),d(e=>Math.max(0,e-1))):f(a.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),f("Failed to mark notification as read")}},[t?.user?.id]),g=(0,o.useCallback)(async()=>{if(t?.user?.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(l(e=>e.map(e=>({...e,isRead:!0}))),d(0)):f(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),f("Failed to mark all notifications as read")}},[t?.user?.id]);return(0,o.useEffect)(()=>{"authenticated"===a&&t?.user?.id&&(h({limit:5}),x())},[a,t?.user?.id,h,x]),(0,o.useEffect)(()=>{if(!t?.user?.id)return;let e=setInterval(()=>{x()},3e4);return()=>clearInterval(e)},[t?.user?.id,x]),r.jsx(s.Provider,{value:{notifications:n,unreadCount:c,loading:u,error:m,fetchNotifications:h,markAsRead:b,markAllAsRead:g,refreshUnreadCount:x},children:e})}},67520:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var r=a(10326),o=a(77109);function i({children:e}){return r.jsx(o.SessionProvider,{children:e})}},90596:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)},36944:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p,metadata:()=>d,viewport:()=>u});var r=a(19510),o=a(77366),i=a.n(o);a(67272);var s=a(68570);let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let c=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let d={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},u={width:"device-width",initialScale:1,themeColor:"#16a34a"};function p({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:i().className,children:r.jsx(l,{children:r.jsx(c,{children:r.jsx(n,{children:e})})})})})}},67272:()=>{}};