"use strict";exports.id=8376,exports.ids=[8376],exports.modules={38376:(e,t,s)=>{s(19801),s(55315),s(84770);var r,i,m,a,h,o,n,c,l,S=s(92048);let{readFile:u}=S.promises;(function(e){e.HEADER="header",e.QUERY="query"})(r||(r={})),function(e){e.HEADER="header",e.QUERY="query"}(i||(i={})),function(e){e.HTTP="http",e.HTTPS="https"}(m||(m={})),function(e){e.MD5="md5",e.CRC32="crc32",e.CRC32C="crc32c",e.SHA1="sha1",e.SHA256="sha256"}(a||(a={})),function(e){e[e.HEADER=0]="HEADER",e[e.TRAILER=1]="TRAILER"}(h||(h={})),function(e){e.PROFILE="profile",e.SSO_SESSION="sso-session",e.SERVICES="services"}(o||(o={})),function(e){e.HTTP_0_9="http/0.9",e.HTTP_1_0="http/1.0",e.TDS_8_0="tds/8.0"}(n||(n={}));let{readFile:b}=S.promises,y={name:"serializerMiddleware"};y.name,s(78893),s(76162),"function"==typeof ReadableStream&&ReadableStream,s(84492),s(32615),s(35240),s(32694),Symbol.iterator;let f={},p={};for(let e=0;e<256;e++){let t=e.toString(16).toLowerCase();1===t.length&&(t=`0${t}`),f[e]=t,p[t]=e}class g{constructor(e,t=new Map){this.namespace=e,this.schemas=t}static for(e){return g.registries.has(e)||g.registries.set(e,new g(e)),g.registries.get(e)}register(e,t){let s=this.normalizeShapeId(e);g.for(this.getNamespace(e)).schemas.set(s,t)}getSchema(e){let t=this.normalizeShapeId(e);if(!this.schemas.has(t))throw Error(`@smithy/core/schema - schema not found for ${t}`);return this.schemas.get(t)}getBaseException(){for(let[e,t]of this.schemas.entries())if(e.startsWith("smithy.ts.sdk.synthetic.")&&e.endsWith("ServiceException"))return t}find(e){return[...this.schemas.values()].find(e)}destroy(){g.registries.delete(this.namespace),this.schemas.clear()}normalizeShapeId(e){return e.includes("#")?e:this.namespace+"#"+e}getNamespace(e){return this.normalizeShapeId(e).split("#")[0]}}g.registries=new Map;class E{constructor(e,t){this.name=e,this.traits=t}}class T extends E{constructor(e,t,s){super(e,t),this.name=e,this.traits=t,this.valueSchema=s,this.symbol=T.symbol}static[Symbol.hasInstance](e){let t=T.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===T.symbol}}T.symbol=Symbol.for("@smithy/core/schema::ListSchema");class M extends E{constructor(e,t,s,r){super(e,t),this.name=e,this.traits=t,this.keySchema=s,this.valueSchema=r,this.symbol=M.symbol}static[Symbol.hasInstance](e){let t=M.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===M.symbol}}M.symbol=Symbol.for("@smithy/core/schema::MapSchema");class I extends E{constructor(e,t,s,r){super(e,t),this.name=e,this.traits=t,this.memberNames=s,this.memberList=r,this.symbol=I.symbol,this.members={};for(let e=0;e<s.length;++e)this.members[s[e]]=Array.isArray(r[e])?r[e]:[r[e],0]}static[Symbol.hasInstance](e){let t=I.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===I.symbol}}I.symbol=Symbol.for("@smithy/core/schema::StructureSchema");class d extends I{constructor(e,t,s,r,i){super(e,t,s,r),this.name=e,this.traits=t,this.memberNames=s,this.memberList=r,this.ctor=i,this.symbol=d.symbol}static[Symbol.hasInstance](e){let t=d.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===d.symbol}}d.symbol=Symbol.for("@smithy/core/schema::ErrorSchema");let O=e=>"function"==typeof e?e():e,N={BLOB:21,STREAMING_BLOB:42,BOOLEAN:2,STRING:0,NUMERIC:1,BIG_INTEGER:17,BIG_DECIMAL:19,DOCUMENT:15,TIMESTAMP_DEFAULT:4,TIMESTAMP_DATE_TIME:5,TIMESTAMP_HTTP_DATE:6,TIMESTAMP_EPOCH_SECONDS:7,LIST_MODIFIER:64,MAP_MODIFIER:128};class _ extends E{constructor(e,t,s){super(e,s),this.name=e,this.schemaRef=t,this.traits=s,this.symbol=_.symbol}static[Symbol.hasInstance](e){let t=_.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===_.symbol}}_.symbol=Symbol.for("@smithy/core/schema::SimpleSchema");class A{constructor(e,t){this.ref=e,this.memberName=t,this.symbol=A.symbol;let s=[],r=e,i=e;for(this._isMemberSchema=!1;Array.isArray(r);)s.push(r[1]),i=O(r=r[0]),this._isMemberSchema=!0;if(s.length>0){this.memberTraits={};for(let e=s.length-1;e>=0;--e){let t=s[e];Object.assign(this.memberTraits,A.translateTraits(t))}}else this.memberTraits=0;if(i instanceof A){this.name=i.name,this.traits=i.traits,this._isMemberSchema=i._isMemberSchema,this.schema=i.schema,this.memberTraits=Object.assign({},i.getMemberTraits(),this.getMemberTraits()),this.normalizedTraits=void 0,this.ref=i.ref,this.memberName=t??i.memberName;return}if(this.schema=O(i),this.schema&&"object"==typeof this.schema?this.traits=this.schema?.traits??{}:this.traits=0,this.name=("object"==typeof this.schema?this.schema?.name:void 0)??this.memberName??this.getSchemaName(),this._isMemberSchema&&!t)throw Error(`@smithy/core/schema - NormalizedSchema member schema ${this.getName(!0)} must initialize with memberName argument.`)}static[Symbol.hasInstance](e){let t=A.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===A.symbol}static of(e,t){return e instanceof A?e:new A(e,t)}static translateTraits(e){if("object"==typeof e)return e;let t={};return(1&(e|=0))==1&&(t.httpLabel=1),(e>>1&1)==1&&(t.idempotent=1),(e>>2&1)==1&&(t.idempotencyToken=1),(e>>3&1)==1&&(t.sensitive=1),(e>>4&1)==1&&(t.httpPayload=1),(e>>5&1)==1&&(t.httpResponseCode=1),(e>>6&1)==1&&(t.httpQueryParams=1),t}static memberFrom(e,t){return e instanceof A?(e.memberName=t,e._isMemberSchema=!0,e):new A(e,t)}getSchema(){return this.schema instanceof A?this.schema=this.schema.getSchema():this.schema instanceof _?O(this.schema.schemaRef):O(this.schema)}getName(e=!1){return!e&&this.name&&this.name.includes("#")?this.name.split("#")[1]:this.name||void 0}getMemberName(){if(!this.isMemberSchema())throw Error(`@smithy/core/schema - cannot get member name on non-member schema: ${this.getName(!0)}`);return this.memberName}isMemberSchema(){return this._isMemberSchema}isUnitSchema(){return"unit"===this.getSchema()}isListSchema(){let e=this.getSchema();return"number"==typeof e?e>=N.LIST_MODIFIER&&e<N.MAP_MODIFIER:e instanceof T}isMapSchema(){let e=this.getSchema();return"number"==typeof e?e>=N.MAP_MODIFIER&&e<=255:e instanceof M}isDocumentSchema(){return this.getSchema()===N.DOCUMENT}isStructSchema(){let e=this.getSchema();return null!==e&&"object"==typeof e&&"members"in e||e instanceof I}isBlobSchema(){return this.getSchema()===N.BLOB||this.getSchema()===N.STREAMING_BLOB}isTimestampSchema(){let e=this.getSchema();return"number"==typeof e&&e>=N.TIMESTAMP_DEFAULT&&e<=N.TIMESTAMP_EPOCH_SECONDS}isStringSchema(){return this.getSchema()===N.STRING}isBooleanSchema(){return this.getSchema()===N.BOOLEAN}isNumericSchema(){return this.getSchema()===N.NUMERIC}isBigIntegerSchema(){return this.getSchema()===N.BIG_INTEGER}isBigDecimalSchema(){return this.getSchema()===N.BIG_DECIMAL}isStreaming(){return!!this.getMergedTraits().streaming||this.getSchema()===N.STREAMING_BLOB}getMergedTraits(){return this.normalizedTraits||(this.normalizedTraits={...this.getOwnTraits(),...this.getMemberTraits()}),this.normalizedTraits}getMemberTraits(){return A.translateTraits(this.memberTraits)}getOwnTraits(){return A.translateTraits(this.traits)}getKeySchema(){if(this.isDocumentSchema())return A.memberFrom([N.DOCUMENT,0],"key");if(!this.isMapSchema())throw Error(`@smithy/core/schema - cannot get key schema for non-map schema: ${this.getName(!0)}`);let e=this.getSchema();return"number"==typeof e?A.memberFrom([63&e,0],"key"):A.memberFrom([e.keySchema,0],"key")}getValueSchema(){let e=this.getSchema();if("number"==typeof e){if(this.isMapSchema())return A.memberFrom([63&e,0],"value");if(this.isListSchema())return A.memberFrom([63&e,0],"member")}if(e&&"object"==typeof e){if(this.isStructSchema())throw Error(`cannot call getValueSchema() with StructureSchema ${this.getName(!0)}`);if("valueSchema"in e){if(this.isMapSchema())return A.memberFrom([e.valueSchema,0],"value");if(this.isListSchema())return A.memberFrom([e.valueSchema,0],"member")}}if(this.isDocumentSchema())return A.memberFrom([N.DOCUMENT,0],"value");throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a value member.`)}getMemberSchema(e){if(this.isStructSchema()){let t=this.getSchema();if(!(e in t.members))throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a member with name=${e}.`);return A.memberFrom(t.members[e],e)}if(this.isDocumentSchema())return A.memberFrom([N.DOCUMENT,0],e);throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have members.`)}getMemberSchemas(){let{schema:e}=this;if(!e||"object"!=typeof e)return{};if("members"in e){let t={};for(let s of e.memberNames)t[s]=this.getMemberSchema(s);return t}return{}}*structIterator(){if(this.isUnitSchema())return;if(!this.isStructSchema())throw Error("@smithy/core/schema - cannot acquire structIterator on non-struct schema.");let e=this.getSchema();for(let t=0;t<e.memberNames.length;++t)yield[e.memberNames[t],A.memberFrom([e.memberList[t],0],e.memberNames[t])]}getSchemaName(){let e=this.getSchema();if("number"==typeof e){let t=63&e,s=Object.entries(N).find(([,e])=>e===t)?.[0]??"Unknown";switch(192&e){case N.MAP_MODIFIER:return`${s}Map`;case N.LIST_MODIFIER:return`${s}List`;case 0:return s}}return"Unknown"}}A.symbol=Symbol.for("@smithy/core/schema::NormalizedSchema"),console.warn;let R=function(e){return Object.assign(new String(e),{deserializeJSON:()=>JSON.parse(String(e)),toString:()=>String(e),toJSON:()=>String(e)})};R.from=e=>e&&"object"==typeof e&&(e instanceof R||"deserializeJSON"in e)?e:"string"==typeof e||Object.getPrototypeOf(e)===String.prototype?R(String(e)):R(JSON.stringify(e)),R.fromObject=R.from,Symbol.hasInstance;y.name;class P extends Error{constructor(e){super(e.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=e.name,this.$fault=e.$fault,this.$metadata=e.$metadata}static isInstance(e){return!!e&&(P.prototype.isPrototypeOf(e)||!!e.$fault&&!!e.$metadata&&("client"===e.$fault||"server"===e.$fault))}static[Symbol.hasInstance](e){return!!e&&(this===P?P.isInstance(e):!!P.isInstance(e)&&(e.name&&this.name?this.prototype.isPrototypeOf(e)||e.name===this.name:this.prototype.isPrototypeOf(e)))}}(function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"})(c||(c={})),function(e){e.ENV="env",e.CONFIG="shared config entry"}(l||(l={}))}};