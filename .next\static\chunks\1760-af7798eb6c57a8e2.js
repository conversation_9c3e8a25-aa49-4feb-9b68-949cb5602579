"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1760],{1760:function(e,t,a){var o=a(7437),s=a(2265),l=a(7648),i=a(3145),r=a(9376),n=a(605),c=a(8293),u=a(4766),d=a(6275),m=a(2369),f=a(9637),x=a(2449),p=a(3827),h=a(9124);t.default=e=>{let{children:t}=e,a=(0,r.usePathname)(),{data:v}=(0,n.useSession)(),{state:g}=(0,p.j)(),{unreadCount:y}=(0,h.z)(),[j,b]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{b(!0)},[]),(0,o.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,o.jsx)("header",{className:"bg-white shadow-sm sticky top-0 z-40",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto lg:px-8",children:[(0,o.jsxs)("div",{className:"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between",children:[(0,o.jsx)("button",{className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,o.jsx)(l.default,{href:"/settings",children:(0,o.jsx)(c.Z,{className:"w-5 h-5 text-gray-600"})})}),(0,o.jsx)(l.default,{href:"/",className:"flex items-center",children:(0,o.jsx)(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[40px] w-auto"})}),(0,o.jsxs)(l.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[(0,o.jsx)(u.Z,{className:"w-5 h-5 text-gray-600"}),j&&(null==v?void 0:v.user)&&y>0&&(0,o.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:y>99?"99+":y})]})]}),(0,o.jsxs)("div",{className:"hidden lg:flex px-4 py-3 items-center justify-between",children:[(0,o.jsx)(l.default,{href:"/",className:"flex items-center",children:(0,o.jsx)(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[60px] w-auto"})}),(0,o.jsxs)("nav",{className:"flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2",children:[(0,o.jsx)(l.default,{href:"/",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Home"}),(0,o.jsx)(l.default,{href:"/shop",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Shop"}),(0,o.jsx)(l.default,{href:"/about",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"About"}),(0,o.jsx)(l.default,{href:"/contact",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Contact"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsxs)(l.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[(0,o.jsx)(u.Z,{className:"w-5 h-5 text-gray-600"}),j&&(null==v?void 0:v.user)&&y>0&&(0,o.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:y>99?"99+":y})]}),(0,o.jsxs)(l.default,{href:"/cart",className:"relative p-2 rounded-full hover:bg-gray-100 transition-colors",children:[(0,o.jsx)(d.Z,{className:"w-5 h-5 text-gray-600"}),j&&g.itemCount>0&&(0,o.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:g.itemCount})]}),(0,o.jsx)(l.default,{href:"/profile",className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,o.jsx)(m.Z,{className:"w-5 h-5 text-gray-600"})})]})]})]})}),(0,o.jsx)("main",{className:"flex-1 pb-20 lg:pb-8",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto lg:px-8",children:(0,o.jsx)("div",{className:"max-w-md mx-auto lg:max-w-none",children:t})})}),(0,o.jsx)("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden",children:(0,o.jsx)("div",{className:"max-w-md mx-auto px-4 py-2",children:(0,o.jsxs)("div",{className:"flex justify-around",children:[(0,o.jsxs)(l.default,{href:"/",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/"===a?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,o.jsx)(f.Z,{className:"w-6 h-6 mb-1"}),(0,o.jsx)("span",{className:"text-xs font-medium",children:"Home"})]}),(0,o.jsxs)(l.default,{href:"/shop",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/shop"===a?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,o.jsx)(x.Z,{className:"w-6 h-6 mb-1"}),(0,o.jsx)("span",{className:"text-xs font-medium",children:"Shop"})]}),(0,o.jsxs)(l.default,{href:"/cart",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ".concat("/cart"===a?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,o.jsx)(d.Z,{className:"w-6 h-6 mb-1"}),j&&g.itemCount>0&&(0,o.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:g.itemCount}),(0,o.jsx)("span",{className:"text-xs font-medium",children:"Cart"})]}),(0,o.jsxs)(l.default,{href:"/profile",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/profile"===a?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,o.jsx)(m.Z,{className:"w-6 h-6 mb-1"}),(0,o.jsx)("span",{className:"text-xs font-medium",children:"Profile"})]})]})})})]})}},3827:function(e,t,a){a.d(t,{CartProvider:function(){return x},j:function(){return p}});var o=a(7437),s=a(2265);let l=(0,s.createContext)(null),i="herbalicious_cart",r=e=>{try{localStorage.setItem(i,JSON.stringify(e))}catch(e){console.error("Error saving cart to localStorage:",e)}},n=()=>{try{{let e=localStorage.getItem(i);if(e)return JSON.parse(e)}}catch(e){console.error("Error loading cart from localStorage:",e)}return null},c=(e,t)=>{if(!t||0===t.length)return e;let a=[...t].sort((e,t)=>e.name.localeCompare(t.name)).map(e=>"".concat(e.name,":").concat(e.value)).join("|");return"".concat(e,"__").concat(a)},u=e=>{var t;return e.variantKey||(null===(t=e.product)||void 0===t?void 0:t.id)||e.id},d=()=>n()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},m=(e,t)=>{let a=e.reduce((e,t)=>e+t.product.price*t.quantity,0),o=e.reduce((e,t)=>e+t.quantity,0),s=t.reduce((e,t)=>e+t.discountAmount,0);return{subtotal:a,itemCount:o,total:a,finalTotal:a-s,totalDiscount:s}},f=(e,t)=>{let a;switch(t.type){case"ADD_ITEM":{let o;let s=c(t.payload.id,t.selectedVariants);if(e.items.find(e=>u(e)===s))o=e.items.map(e=>u(e)===s?{...e,quantity:e.quantity+1,variantKey:s}:e);else{let a={product:t.payload,quantity:1,selectedVariants:t.selectedVariants||[],variantKey:s};o=[...e.items,a]}let l=m(o,e.coupons.appliedCoupons);a={...e,items:o,...l,coupons:{...e.coupons,totalDiscount:l.totalDiscount}};break}case"REMOVE_ITEM":{let o=e.items.filter(e=>u(e)!==t.payload),s=m(o,e.coupons.appliedCoupons);a={...e,items:o,...s,coupons:{...e.coupons,totalDiscount:s.totalDiscount}};break}case"UPDATE_QUANTITY":{let o=e.items.map(e=>u(e)===t.payload.id?{...e,quantity:t.payload.quantity}:e).filter(e=>e.quantity>0),s=m(o,e.coupons.appliedCoupons);a={...e,items:o,...s,coupons:{...e.coupons,totalDiscount:s.totalDiscount}};break}case"APPLY_COUPON":{if(e.coupons.appliedCoupons.some(e=>e.coupon.id===t.payload.coupon.id)||e.coupons.appliedCoupons.some(e=>!e.coupon.isStackable)&&!t.payload.coupon.isStackable)return e;let o=[...e.coupons.appliedCoupons,t.payload],s=m(e.items,o);a={...e,...s,coupons:{...e.coupons,appliedCoupons:o,totalDiscount:s.totalDiscount}};break}case"REMOVE_COUPON":{let o=e.coupons.appliedCoupons.filter(e=>e.coupon.id!==t.payload),s=m(e.items,o);a={...e,...s,coupons:{...e.coupons,appliedCoupons:o,totalDiscount:s.totalDiscount}};break}case"CLEAR_COUPONS":{let t=m(e.items,[]);a={...e,...t,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":a={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return e}return r(a),a},x=e=>{let{children:t}=e,[a,i]=(0,s.useReducer)(f,d());return(0,o.jsx)(l.Provider,{value:{state:a,dispatch:i},children:t})},p=()=>{let e=(0,s.useContext)(l);if(!e)throw Error("useCart must be used within a CartProvider");return e}},9124:function(e,t,a){a.d(t,{NotificationProvider:function(){return n},z:function(){return r}});var o=a(7437),s=a(2265),l=a(605);let i=(0,s.createContext)(void 0),r=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},n=e=>{var t,a,r,n,c,u;let{children:d}=e,{data:m,status:f}=(0,l.useSession)(),[x,p]=(0,s.useState)([]),[h,v]=(0,s.useState)(0),[g,y]=(0,s.useState)(!1),[j,b]=(0,s.useState)(null),N=(0,s.useCallback)(async function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(null==m?void 0:null===(e=m.user)||void 0===e?void 0:e.id)try{y(!0),b(null);let e=new URLSearchParams({page:(t.page||1).toString(),limit:(t.limit||10).toString(),...t.unreadOnly&&{unreadOnly:"true"}}),a=await fetch("/api/notifications?".concat(e)),o=await a.json();o.success?(p(o.data.notifications),v(o.data.unreadCount)):b(o.error||"Failed to fetch notifications")}catch(e){console.error("Error fetching notifications:",e),b("Failed to fetch notifications")}finally{y(!1)}},[null==m?void 0:null===(t=m.user)||void 0===t?void 0:t.id]),w=(0,s.useCallback)(async()=>{var e;if(null==m?void 0:null===(e=m.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/notifications/unread-count"),t=await e.json();t.success&&v(t.unreadCount)}catch(e){console.error("Error fetching unread count:",e)}},[null==m?void 0:null===(a=m.user)||void 0===a?void 0:a.id]),C=(0,s.useCallback)(async e=>{var t;if(null==m?void 0:null===(t=m.user)||void 0===t?void 0:t.id)try{let t=await fetch("/api/notifications/".concat(e,"/read"),{method:"POST"}),a=await t.json();a.success?(p(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),v(e=>Math.max(0,e-1))):b(a.error||"Failed to mark notification as read")}catch(e){console.error("Error marking notification as read:",e),b("Failed to mark notification as read")}},[null==m?void 0:null===(r=m.user)||void 0===r?void 0:r.id]),k=(0,s.useCallback)(async()=>{var e;if(null==m?void 0:null===(e=m.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/notifications/mark-all-read",{method:"POST"}),t=await e.json();t.success?(p(e=>e.map(e=>({...e,isRead:!0}))),v(0)):b(t.error||"Failed to mark all notifications as read")}catch(e){console.error("Error marking all notifications as read:",e),b("Failed to mark all notifications as read")}},[null==m?void 0:null===(n=m.user)||void 0===n?void 0:n.id]);return(0,s.useEffect)(()=>{var e;"authenticated"===f&&(null==m?void 0:null===(e=m.user)||void 0===e?void 0:e.id)&&(N({limit:5}),w())},[f,null==m?void 0:null===(c=m.user)||void 0===c?void 0:c.id,N,w]),(0,s.useEffect)(()=>{var e;if(!(null==m?void 0:null===(e=m.user)||void 0===e?void 0:e.id))return;let t=setInterval(()=>{w()},3e4);return()=>clearInterval(t)},[null==m?void 0:null===(u=m.user)||void 0===u?void 0:u.id,w]),(0,o.jsx)(i.Provider,{value:{notifications:x,unreadCount:h,loading:g,error:j,fetchNotifications:N,markAsRead:C,markAllAsRead:k,refreshUnreadCount:w},children:d})}}}]);