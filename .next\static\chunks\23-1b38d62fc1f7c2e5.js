"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[23],{3905:function(s,e,l){l.d(e,{Gw:function(){return m},QW:function(){return d},sW:function(){return x}});var a=l(7437);l(2265);let c=s=>{let{className:e=""}=s;return(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 rounded ".concat(e)})},d=()=>(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,a.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[(0,a.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,a.jsx)(c,{className:"w-8 h-8 rounded-full"})]})]}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)(c,{className:"w-full h-80 rounded-xl"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)(c,{className:"h-8 w-3/4 mb-2"}),(0,a.jsx)(c,{className:"h-4 w-full mb-3"}),(0,a.jsx)(c,{className:"h-6 w-1/3 mb-4"}),(0,a.jsx)("div",{className:"flex border-b border-gray-200 mb-4",children:Array.from({length:4}).map((s,e)=>(0,a.jsx)(c,{className:"h-8 w-20 mr-4"},e))}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsx)(c,{className:"h-4 w-full"}),(0,a.jsx)(c,{className:"h-4 w-5/6"}),(0,a.jsx)(c,{className:"h-4 w-4/5"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c,{className:"w-10 h-10 rounded-lg"}),(0,a.jsx)(c,{className:"w-10 h-6"}),(0,a.jsx)(c,{className:"w-10 h-10 rounded-lg"})]}),(0,a.jsx)(c,{className:"flex-1 h-12 rounded-lg"})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)(c,{className:"h-6 w-32 mb-8"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,a.jsx)(c,{className:"w-full h-96 rounded-xl"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c,{className:"h-10 w-3/4 mb-4"}),(0,a.jsx)(c,{className:"h-6 w-full mb-6"}),(0,a.jsx)(c,{className:"h-8 w-1/3 mb-6"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-8",children:[Array.from({length:5}).map((s,e)=>(0,a.jsx)(c,{className:"w-5 h-5"},e)),(0,a.jsx)(c,{className:"h-4 w-24"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c,{className:"w-12 h-12 rounded-lg"}),(0,a.jsx)(c,{className:"w-12 h-6"}),(0,a.jsx)(c,{className:"w-12 h-12 rounded-lg"})]}),(0,a.jsx)(c,{className:"flex-1 h-14 rounded-lg"})]}),(0,a.jsx)("div",{className:"flex border-b border-gray-200 mb-6",children:Array.from({length:4}).map((s,e)=>(0,a.jsx)(c,{className:"h-10 w-24 mr-6"},e))}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(c,{className:"h-4 w-full"}),(0,a.jsx)(c,{className:"h-4 w-5/6"}),(0,a.jsx)(c,{className:"h-4 w-4/5"}),(0,a.jsx)(c,{className:"h-4 w-3/4"})]})]})]})]})})]}),m=()=>(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,a.jsx)(c,{className:"h-6 w-24"}),(0,a.jsx)("div",{className:"ml-auto",children:(0,a.jsx)(c,{className:"h-4 w-16"})})]})}),(0,a.jsxs)("div",{className:"px-4 py-6",children:[(0,a.jsx)(c,{className:"w-full h-12 rounded-2xl mb-6"}),(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((s,e)=>(0,a.jsx)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)(c,{className:"w-20 h-20 rounded-xl flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)(c,{className:"h-5 w-3/4"}),(0,a.jsx)(c,{className:"w-4 h-4 rounded-full"})]}),(0,a.jsx)(c,{className:"h-4 w-full mb-2"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)(c,{className:"h-4 w-8"}),(0,a.jsx)(c,{className:"h-4 w-16"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c,{className:"h-5 w-16"}),(0,a.jsx)(c,{className:"h-8 w-24 rounded-full"})]})]})]})},e))})]})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)(c,{className:"h-6 w-16 mb-8"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)(c,{className:"h-10 w-48"}),(0,a.jsx)(c,{className:"h-6 w-24"})]}),(0,a.jsx)(c,{className:"w-48 h-12 rounded-2xl mb-8"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-6",children:Array.from({length:6}).map((s,e)=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsx)(c,{className:"w-full h-48 rounded-xl mb-4"}),(0,a.jsx)(c,{className:"h-6 w-3/4 mb-2"}),(0,a.jsx)(c,{className:"h-4 w-full mb-3"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)(c,{className:"h-4 w-8"}),(0,a.jsx)(c,{className:"h-4 w-16"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c,{className:"h-6 w-20"}),(0,a.jsx)(c,{className:"h-8 w-24 rounded-xl"})]})]},e))})]})})]}),x=()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)(c,{className:"h-8 w-16"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c,{className:"w-10 h-10 rounded-full"}),(0,a.jsx)(c,{className:"w-20 h-10 rounded-lg"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)(c,{className:"h-4 w-32"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c,{className:"w-8 h-8 rounded"}),(0,a.jsx)(c,{className:"w-8 h-8 rounded"})]})]})]})}),(0,a.jsx)("div",{className:"px-4 py-6",children:(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Array.from({length:6}).map((s,e)=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden",children:[(0,a.jsx)(c,{className:"w-full h-48"}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)(c,{className:"h-4 w-3/4 mb-2"}),(0,a.jsx)(c,{className:"h-3 w-full mb-3"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(c,{className:"h-5 w-16 mb-1"}),(0,a.jsx)(c,{className:"h-3 w-20"})]}),(0,a.jsx)(c,{className:"w-10 h-10 rounded-full"})]})]})]},e))})})]}),(0,a.jsxs)("div",{className:"hidden lg:block",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(c,{className:"h-10 w-64 mx-auto mb-4"}),(0,a.jsx)(c,{className:"h-6 w-96 mx-auto"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,a.jsx)(c,{className:"h-12 w-80 rounded-xl"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(c,{className:"h-12 w-40 rounded-xl"}),(0,a.jsx)(c,{className:"h-10 w-20 rounded-lg"})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-center gap-6 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(c,{className:"h-4 w-20"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c,{className:"w-20 h-10 rounded-lg"}),(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)(c,{className:"w-20 h-10 rounded-lg"})]})]})}),(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:Array.from({length:6}).map((s,e)=>(0,a.jsx)(c,{className:"h-12 w-24 rounded-xl"},e))})})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)(c,{className:"h-4 w-32"}),(0,a.jsx)(c,{className:"h-4 w-24"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((s,e)=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden",children:[(0,a.jsx)(c,{className:"w-full h-56"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(c,{className:"h-5 w-3/4 mb-2"}),(0,a.jsx)(c,{className:"h-4 w-full mb-4"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(c,{className:"h-5 w-20 mb-1"}),(0,a.jsx)(c,{className:"h-3 w-16"})]}),(0,a.jsx)(c,{className:"w-12 h-12 rounded-full"})]})]})]},e))})]})]})]})},8997:function(s,e,l){l.d(e,{Z:function(){return a}});let a=(0,l(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},6595:function(s,e,l){l.d(e,{Z:function(){return a}});let a=(0,l(9763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},2489:function(s,e,l){l.d(e,{Z:function(){return a}});let a=(0,l(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);