(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1899],{1205:function(e,s,t){Promise.resolve().then(t.bind(t,4675))},4675:function(e,s,t){"use strict";t.r(s);var r=t(7437);t(2265);var a=t(9376),n=t(2660),c=t(9397),l=t(3774),d=t(5868),i=t(8930);s.default=()=>{let e=(0,a.useRouter)();return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,r.jsx)(n.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Back"})]})}),(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shipping Addresses"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-xl hover:border-green-400 hover:bg-green-50 transition-colors",children:[(0,r.jsx)(c.Z,{className:"w-6 h-6 text-gray-400"}),(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:"Add New Address"})]})}),[{id:"1",firstName:"John",lastName:"Doe",company:"Tech Corp",address1:"123 Main Street",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"India",phone:"+91 99878 10707",isDefault:!0},{id:"2",firstName:"John",lastName:"Doe",company:"",address1:"456 Oak Avenue",address2:"",city:"Brooklyn",state:"NY",postalCode:"11201",country:"India",phone:"+91 99878 10707",isDefault:!1}].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mt-1",children:(0,r.jsx)(l.Z,{className:"w-6 h-6 text-gray-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&(0,r.jsx)("p",{className:"font-medium",children:e.company}),(0,r.jsx)("p",{children:e.address1}),e.address2&&(0,r.jsx)("p",{children:e.address2}),(0,r.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),(0,r.jsx)("p",{children:e.country}),e.phone&&(0,r.jsx)("p",{className:"font-medium",children:e.phone})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),(0,r.jsx)("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:(0,r.jsx)(d.Z,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"w-4 h-4"})})]})]})},e.id)),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-2xl p-6 border border-blue-100",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Address Information"}),(0,r.jsx)("p",{className:"text-blue-800 text-sm",children:"Your default address will be automatically selected during checkout. You can always change it before placing an order."})]})]})]})})}},9763:function(e,s,t){"use strict";t.d(s,{Z:function(){return c}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(e,s)=>{let t=(0,r.forwardRef)((t,c)=>{let{color:l="currentColor",size:d=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:u="",children:h,...m}=t;return(0,r.createElement)("svg",{ref:c,...a,width:d,height:d,stroke:l,strokeWidth:o?24*Number(i)/Number(d):i,className:["lucide","lucide-".concat(n(e)),u].join(" "),...m},[...s.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(h)?h:[h]])});return t.displayName="".concat(e),t}},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3774:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},9397:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5868:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},8930:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},9376:function(e,s,t){"use strict";var r=t(5475);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=1205)}),_N_E=e.O()}]);