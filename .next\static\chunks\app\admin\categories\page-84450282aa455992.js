(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4331],{9812:function(e,t,a){Promise.resolve().then(a.bind(a,2394))},2394:function(e,t,a){"use strict";a.r(t);var s=a(7437),r=a(2265),n=a(3247),i=a(9397),l=a(5863),c=a(5868),o=a(8930);t.default=()=>{let[e,t]=(0,r.useState)(""),[a,d]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),[m,g]=(0,r.useState)(null),[h,p]=(0,r.useState)([]),[y,f]=(0,r.useState)(!0),[j,b]=(0,r.useState)(null);(0,r.useEffect)(()=>{N()},[]);let N=async()=>{try{f(!0);let e=await fetch("/api/categories"),t=await e.json();t.success?p(t.data):b("Failed to fetch categories")}catch(e){console.error("Error fetching categories:",e),b("Failed to fetch categories")}finally{f(!1)}},v=h.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())),w=e=>{g(e),x(!0)},k=async e=>{if(confirm("Are you sure you want to delete this category?"))try{(await fetch("/api/categories/".concat(e),{method:"DELETE"})).ok?p(t=>t.filter(t=>t.id!==e)):alert("Failed to delete category")}catch(e){console.error("Error deleting category:",e),alert("Failed to delete category")}};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"mb-6 flex flex-wrap items-center justify-between gap-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search categories...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),(0,s.jsx)(n.Z,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),(0,s.jsxs)("button",{onClick:()=>{d(!0)},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,s.jsx)(i.Z,{className:"h-5 w-5"}),"Add Category"]})]})}),y?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)(l.Z,{className:"h-8 w-8 animate-spin text-green-600"})}):j?(0,s.jsx)("div",{className:"text-center py-8 text-red-600",children:j}):0===v.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No categories found"}):(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Products"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created At"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:v.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.slug})]}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.description||"-"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[e._count.products," products"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:new Date(e.createdAt).toLocaleDateString()})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,s.jsx)("button",{onClick:()=>w(e),className:"text-green-600 hover:text-green-900 mr-3",children:(0,s.jsx)(c.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:()=>k(e.id),className:"text-red-600 hover:text-red-900",children:(0,s.jsx)(o.Z,{className:"h-5 w-5"})})]})]},e.id))})]})}),(0,s.jsx)(()=>{let[e,t]=(0,r.useState)({name:"",description:""}),[n,i]=(0,r.useState)(!1),l=async a=>{a.preventDefault(),i(!0);try{let a=await fetch("/api/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(a.ok){let e=await a.json();e.success?(p(t=>[...t,e.data]),d(!1),t({name:"",description:""})):alert("Failed to create category: "+e.error)}else alert("Failed to create category")}catch(e){console.error("Error creating category:",e),alert("Failed to create category")}finally{i(!1)}};return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Add New Category"}),(0,s.jsx)("button",{onClick:()=>d(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:l,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>d(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",disabled:n,children:n?"Creating...":"Add Category"})]})]})]})}):null},{}),(0,s.jsx)(()=>{let[e,t]=(0,r.useState)({name:(null==m?void 0:m.name)||"",description:(null==m?void 0:m.description)||""}),[a,n]=(0,r.useState)(!1);r.useEffect(()=>{m&&t({name:m.name,description:m.description||""})},[m]);let i=async t=>{if(t.preventDefault(),m){n(!0);try{let t=await fetch("/api/categories/".concat(m.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(t.ok){let e=await t.json();e.success?(p(t=>t.map(t=>t.id===m.id?e.data:t)),x(!1),g(null)):alert("Failed to update category: "+e.error)}else alert("Failed to update category")}catch(e){console.error("Error updating category:",e),alert("Failed to update category")}finally{n(!1)}}};return u?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Edit Category"}),(0,s.jsx)("button",{onClick:()=>{x(!1),g(null)},className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:i,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{x(!1),g(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",disabled:a,children:a?"Saving...":"Save Changes"})]})]})]})}):null},{})]})}},9763:function(e,t,a){"use strict";a.d(t,{Z:function(){return i}});var s=a(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let a=(0,s.forwardRef)((a,i)=>{let{color:l="currentColor",size:c=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:x,...m}=a;return(0,s.createElement)("svg",{ref:i,...r,width:c,height:c,stroke:l,strokeWidth:d?24*Number(o)/Number(c):o,className:["lucide","lucide-".concat(n(e)),u].join(" "),...m},[...t.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(x)?x:[x]])});return a.displayName="".concat(e),a}},5863:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},9397:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3247:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},5868:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},8930:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=9812)}),_N_E=e.O()}]);