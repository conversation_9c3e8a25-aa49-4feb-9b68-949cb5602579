(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6781],{9872:function(e,t,s){Promise.resolve().then(s.bind(s,9934))},9934:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return f}});var r=s(7437),a=s(2265);let n=(0,s(9763).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var i=s(5863),l=s(5805),c=s(525),d=s(2449),o=s(5252),x=s(3247),m=s(740),u=s(2735),h=s(2208),p=s(9345),y=s(8930),g=s(1380),f=()=>{let[e,t]=(0,a.useState)(""),[s,f]=(0,a.useState)(null),[j,N]=(0,a.useState)(!1),[w,v]=(0,a.useState)(!1),[b,k]=(0,a.useState)([]),[Z,C]=(0,a.useState)(!0),[M,S]=(0,a.useState)(null);(0,a.useEffect)(()=>{L()},[]);let L=async()=>{try{C(!0);let e=await fetch("/api/users"),t=await e.json();t.success?k(t.data):S("Failed to fetch customers")}catch(e){console.error("Error fetching customers:",e),S("Failed to fetch customers")}finally{C(!1)}},T=b.filter(t=>{var s;return(null===(s=t.name)||void 0===s?void 0:s.toLowerCase().includes(e.toLowerCase()))||t.email.toLowerCase().includes(e.toLowerCase())}),A=e=>"ADMIN"===e?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800",E=e=>new Date(e).toLocaleDateString();return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Customers"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your customer relationships"})]}),(0,r.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,r.jsxs)("button",{onClick:()=>v(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[(0,r.jsx)(n,{className:"w-5 h-5 mr-2"}),"Add Customer"]})})]})}),Z&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)(i.Z,{className:"w-8 h-8 animate-spin text-green-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading customers..."})]}),M&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("p",{className:"text-red-600",children:M}),(0,r.jsx)("button",{onClick:L,className:"mt-2 text-red-600 hover:text-red-700 underline",children:"Try again"})]}),!Z&&!M&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:b.length})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(l.Z,{className:"w-6 h-6 text-white"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Customers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:b.filter(e=>"CUSTOMER"===e.role).length})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(c.Z,{className:"w-6 h-6 text-white"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:b.reduce((e,t)=>e+t._count.orders,0)})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-white"})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:(0,g.T4)(0)})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"w-6 h-6 text-white"})})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search customers...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),(0,r.jsx)("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsx)(m.Z,{className:"w-5 h-5 text-gray-600"})})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsx)("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsx)(u.Z,{className:"w-5 h-5 text-gray-600"})})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Spent"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:T.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-green-600 font-medium",children:e.name?e.name.split(" ").map(e=>e[0]).join(""):e.email[0].toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name||"No name"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined ",E(e.createdAt)]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(A(e.role)),children:e.role})})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.email}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.phone||"No phone"})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e._count.orders}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total orders"})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:"-"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"No data"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",children:"Active"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"p-1 text-gray-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(h.Z,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(p.Z,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(y.Z,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),(0,r.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",T.length," of ",b.length," customers"]})]})]})}},1380:function(e,t,s){"use strict";function r(e){return function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return"₹".concat(s)}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function n(e,t){return(e?a(e):a(t))||"product"}s.d(t,{GD:function(){return a},T4:function(){return r},w:function(){return n}})},9763:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var r=s(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let s=(0,r.forwardRef)((s,i)=>{let{color:l="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:x="",children:m,...u}=s;return(0,r.createElement)("svg",{ref:i,...a,width:c,height:c,stroke:l,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(n(e)),x].join(" "),...u},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},5252:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},2735:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2208:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},740:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},5863:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},9345:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3247:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2449:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},8930:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},525:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},5805:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=9872)}),_N_E=e.O()}]);