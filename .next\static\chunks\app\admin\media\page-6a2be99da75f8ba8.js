(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1565],{2030:function(e,s,t){Promise.resolve().then(t.bind(t,1928))},1928:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return v}});var a=t(7437),r=t(2265),l=t(3113),c=t(9374),n=t(9658),i=t(2489),o=t(7689),d=t(8867),x=t(401),m=t(2252);let h=(0,t(9763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var g=t(3247),u=t(1473),y=t(7586),p=t(8930),f=t(2208),j=t(2735),N=t(6143),v=()=>{let[e,s]=(0,r.useState)([]),[t,v]=(0,r.useState)(!0),[b,w]=(0,r.useState)(!1),[k,C]=(0,r.useState)(""),[S,Z]=(0,r.useState)([]),[D,E]=(0,r.useState)("grid"),[_,M]=(0,r.useState)("all"),[T,L]=(0,r.useState)("all"),[F,U]=(0,r.useState)(!1),[R,A]=(0,r.useState)(null),[z,P]=(0,r.useState)(null),B=(0,r.useCallback)(async()=>{try{v(!0);let t="all"===T?void 0:T,a="/api/media/list".concat(t?"?folder=".concat(t):"");console.log("Loading files from:",a);let r=await fetch(a);if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));let l=await r.json();if(l.success){var e;s(l.files||[]),console.log("Loaded ".concat((null===(e=l.files)||void 0===e?void 0:e.length)||0," files"))}else throw Error(l.error||"Failed to load files")}catch(e){console.error("Error loading files:",e),O("error",e instanceof Error?e.message:"Failed to load files"),s([])}finally{v(!1)}},[T]);(0,r.useEffect)(()=>{B()},[B]);let O=(e,s)=>{P({type:e,message:s}),setTimeout(()=>P(null),3e3)},I=async e=>{w(!0),console.log("Starting upload of ".concat(e.length," files"));let s=Array.from(e).map(async e=>{let s=new FormData;s.append("file",e),s.append("folder","all"===T?"uploads":T);try{console.log("Uploading file: ".concat(e.name," (").concat(e.size," bytes)"));let t=await fetch("/api/media/upload",{method:"POST",body:s});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let a=await t.json();return console.log("Upload result for ".concat(e.name,":"),a),a}catch(s){return console.error("Upload failed for ".concat(e.name,":"),s),{success:!1,error:s instanceof Error?s.message:"Upload failed"}}}),t=await Promise.all(s),a=t.filter(e=>e.success).length,r=t.filter(e=>!e.success).length;a>0&&(O("success","".concat(a," file(s) uploaded successfully")),B()),r>0&&O("error","".concat(r," file(s) failed to upload")),w(!1),U(!1)},K=async e=>{if(confirm("Are you sure you want to delete ".concat(e.length," file(s)?")))try{console.log("Deleting files:",e);let s=e.map(async e=>{let s=await fetch("/api/media/delete",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:e})});if(!s.ok)throw Error("Failed to delete ".concat(e,": ").concat(s.status));let t=await s.json();if(!t.success)throw Error("Failed to delete ".concat(e,": ").concat(t.error));return t});await Promise.all(s),O("success","".concat(e.length," file(s) deleted successfully")),Z([]),B()}catch(e){console.error("Delete error:",e),O("error",e instanceof Error?e.message:"Failed to delete files")}},q=e=>{navigator.clipboard.writeText(e),O("success","URL copied to clipboard")},H=e=>{let s=document.createElement("a");s.href=e.url,s.download=e.name,s.target="_blank",document.body.appendChild(s),s.click(),document.body.removeChild(s),O("success","Download started")},V=e.filter(e=>{let s=e.name.toLowerCase().includes(k.toLowerCase()),t=!0;return"all"!==_&&(t="document"===_?"document"===e.type||"other"===e.type:e.type===_),s&&t}),Y=["all",...Array.from(new Set(e.map(e=>e.folder).filter(Boolean)))],G=e=>{switch(e){case"image":return(0,a.jsx)(l.Z,{className:"w-5 h-5"});case"video":return(0,a.jsx)(c.Z,{className:"w-5 h-5"});default:return(0,a.jsx)(n.Z,{className:"w-5 h-5"})}};return(0,a.jsxs)("div",{children:[z&&(0,a.jsxs)("div",{className:"fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ".concat("success"===z.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:["success"===z.type?(0,a.jsx)(x.Z,{className:"w-5 h-5 mr-2"}):(0,a.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),z.message]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Media Library"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your images, videos, and documents"})]}),(0,a.jsxs)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>B(),disabled:t,className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsx)(h,{className:"w-5 h-5 text-gray-600 ".concat(t?"animate-spin":"")})}),(0,a.jsxs)("button",{onClick:()=>U(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 mr-2"}),"Upload Files"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search files...",value:k,onChange:e=>C(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),(0,a.jsx)("select",{value:T,onChange:e=>L(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:Y.map(e=>(0,a.jsx)("option",{value:e,children:"all"===e?"All Folders":e},e))}),(0,a.jsxs)("select",{value:_,onChange:e=>M(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"image",children:"Images"}),(0,a.jsx)("option",{value:"video",children:"Videos"}),(0,a.jsx)("option",{value:"document",children:"Documents"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex border border-gray-300 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>E("grid"),className:"p-2 ".concat("grid"===D?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"),children:(0,a.jsx)(u.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>E("list"),className:"p-2 ".concat("list"===D?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"),children:(0,a.jsx)(y.Z,{className:"w-4 h-4"})})]}),S.length>0&&(0,a.jsx)("button",{onClick:()=>K(S),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:(0,a.jsx)(p.Z,{className:"w-5 h-5"})})]})]})}),t?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(h,{className:"w-8 h-8 text-gray-400 animate-spin"})}):0===V.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(l.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No files found"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Upload some files to get started"})]}):"grid"===D?(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:V.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"checkbox",checked:S.includes(e.key),onChange:s=>{s.target.checked?Z([...S,e.key]):Z(S.filter(s=>s!==e.key))},className:"absolute top-2 left-2 rounded border-gray-300 text-green-600 focus:ring-green-500 z-10"}),(0,a.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden",children:"image"===e.type?(0,a.jsx)("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"text-gray-400",children:G(e.type)})})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",title:e.name,children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:(0,N.sS)(e.size)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[(0,a.jsx)("button",{onClick:()=>A(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Preview",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>H(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Download",children:(0,a.jsx)(j.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>q(e.url),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Copy URL",children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>K([e.key]),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,a.jsx)(p.Z,{className:"w-4 h-4"})})]})]},e.key))}):(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:S.length===V.length&&V.length>0,onChange:e=>{e.target.checked?Z(V.map(e=>e.key)):Z([])},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Modified"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:V.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:S.includes(e.key),onChange:s=>{s.target.checked?Z([...S,e.key]):Z(S.filter(s=>s!==e.key))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3",children:"image"===e.type?(0,a.jsx)("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover rounded-lg"}):(0,a.jsx)("div",{className:"text-gray-400",children:G(e.type)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.folder||"Root"})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 capitalize",children:e.type}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900",children:(0,N.sS)(e.size)}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.lastModified).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>A(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Preview",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>H(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Download",children:(0,a.jsx)(j.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>q(e.url),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Copy URL",children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>K([e.key]),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,a.jsx)(p.Z,{className:"w-4 h-4"})})]})})]},e.key))})]})})}),(0,a.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",V.length," of ",e.length," files",S.length>0&&" • ".concat(S.length," selected")]}),(0,a.jsx)(()=>{let[e,s]=(0,r.useState)(!1);return F?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Upload Files"}),(0,a.jsx)("button",{onClick:()=>U(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(i.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors ".concat(e?"border-green-500 bg-green-50":"border-gray-300"),onDragOver:e=>{e.preventDefault(),s(!0)},onDragLeave:()=>s(!1),onDrop:e=>{e.preventDefault(),s(!1),e.dataTransfer.files&&I(e.dataTransfer.files)},children:[(0,a.jsx)(o.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop files here, or click to select"}),(0,a.jsx)("input",{type:"file",multiple:!0,onChange:e=>{e.target.files&&I(e.target.files)},className:"hidden",id:"file-upload",accept:"image/*,video/*,.pdf"}),(0,a.jsx)("label",{htmlFor:"file-upload",className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors cursor-pointer",children:"Select Files"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Supported formats: Images, Videos, PDFs (Max 10MB each)"})]})}):null},{}),(0,a.jsx)(()=>R?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:R.name}),(0,a.jsx)("button",{onClick:()=>A(null),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(i.Z,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"mb-4",children:"image"===R.type?(0,a.jsx)("img",{src:R.url,alt:R.name,className:"max-w-full h-auto rounded-lg"}):"video"===R.type?(0,a.jsx)("video",{src:R.url,controls:!0,className:"max-w-full h-auto rounded-lg"}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-100 rounded-lg",children:(0,a.jsx)(n.Z,{className:"w-16 h-16 text-gray-400"})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Size:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:(0,N.sS)(R.size)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Type:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:R.type})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Modified:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:new Date(R.lastModified).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Folder:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:R.folder||"Root"})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"URL:"}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)("input",{type:"text",value:R.url,readOnly:!0,className:"flex-1 px-3 py-1 text-sm border border-gray-300 rounded mr-2"}),(0,a.jsx)("button",{onClick:()=>q(R.url),className:"p-1 text-green-600 hover:text-green-700",children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})})]})]})]})}):null,{})]})}},6143:function(e,s,t){"use strict";t.d(s,{sS:function(){return l}});var a=t(4850);t(6395);var r=t(257);function l(e){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]}new a.g({region:"auto",endpoint:"https://".concat(r.env.R2_ACCOUNT_ID,".r2.cloudflarestorage.com"),credentials:{accessKeyId:r.env.R2_ACCESS_KEY_ID,secretAccessKey:r.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0}),r.env.R2_BUCKET_NAME,r.env.R2_PUBLIC_URL||r.env.R2_ACCOUNT_ID},2252:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8867:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},2735:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}},function(e){e.O(0,[411,4190,2971,2117,1744],function(){return e(e.s=2030)}),_N_E=e.O()}]);