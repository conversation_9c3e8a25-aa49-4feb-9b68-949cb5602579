(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6888],{8275:function(e,t,s){Promise.resolve().then(s.bind(s,1541))},1541:function(e,t,s){"use strict";s.r(t);var n=s(7437),r=s(2265),a=s(9376),i=s(5302),c=s(2660),l=s(2252),o=s(5805),d=s(2369),u=s(4743);t.default=()=>{let e=(0,a.useRouter)(),[t,s]=(0,r.useState)(!1),[m,x]=(0,r.useState)([]),[h,g]=(0,r.useState)([]),[p,f]=(0,r.useState)([]),[y,b]=(0,r.useState)(""),[N,j]=(0,r.useState)(!1),[v,k]=(0,r.useState)(!1),[w,S]=(0,r.useState)(null),[C,Z]=(0,r.useState)({title:"",content:"",type:"ADMIN_MESSAGE",priority:"NORMAL",sendEmail:!0,sendInApp:!0});(0,r.useEffect)(()=>{E(),A()},[]);let E=async()=>{try{let e=await fetch("/api/admin/users?limit=100"),t=await e.json();t.success&&x(t.data.users||[])}catch(e){console.error("Error fetching users:",e)}},A=async()=>{try{let e=await fetch("/api/admin/notifications/templates"),t=await e.json();t.success&&g(t.templates||[])}catch(e){console.error("Error fetching templates:",e)}},M=e=>{f(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},I=e=>{if(b(e),e){let t=h.find(t=>t.id===e);t&&(Z(e=>({...e,title:t.subject,content:t.content})),j(!1))}else j(!0)},P=async t=>{if(t.preventDefault(),0===p.length){S("Please select at least one user");return}if(!C.title.trim()||!C.content.trim()){S("Please provide both title and content");return}s(!0),S(null);try{let t=await fetch("/api/admin/notifications/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userIds:p,title:C.title,content:C.content,type:C.type,priority:C.priority,sendEmail:C.sendEmail,sendInApp:C.sendInApp})}),s=await t.json();s.success?(k(!0),setTimeout(()=>{e.push("/admin/notifications")},2e3)):S(s.error||"Failed to send notification")}catch(e){console.error("Error sending notification:",e),S("Failed to send notification")}finally{s(!1)}};return v?(0,n.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,n.jsx)(i.Z,{className:"w-12 h-12 text-green-600 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-xl font-semibold text-green-900 mb-2",children:"Notification Sent Successfully!"}),(0,n.jsxs)("p",{className:"text-green-700",children:["Your notification has been sent to ",p.length," user(s)."]}),(0,n.jsx)("p",{className:"text-sm text-green-600 mt-2",children:"Redirecting to notifications dashboard..."})]})}):(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,n.jsx)("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(c.Z,{className:"w-5 h-5 text-gray-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Send Notification"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Send a notification to selected users"})]})]}),w&&(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center space-x-2",children:[(0,n.jsx)(l.Z,{className:"w-5 h-5 text-red-600"}),(0,n.jsx)("span",{className:"text-red-700",children:w})]}),(0,n.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 flex items-center space-x-2",children:[(0,n.jsx)(o.Z,{className:"w-5 h-5"}),(0,n.jsxs)("span",{children:["Select Users (",p.length," selected)"]})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{type:"button",onClick:()=>{f(m.map(e=>e.id))},className:"text-sm text-green-600 hover:text-green-700",children:"Select All"}),(0,n.jsx)("button",{type:"button",onClick:()=>{f([])},className:"text-sm text-gray-600 hover:text-gray-700",children:"Clear"})]})]}),(0,n.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-2",children:m.map(e=>(0,n.jsxs)("label",{className:"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:p.includes(e.id),onChange:()=>M(e.id),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),(0,n.jsx)(d.Z,{className:"w-4 h-4 text-gray-400"}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name||"No name"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.email})]})]},e.id))})]}),(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Message Content"}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Use Template (Optional)"}),(0,n.jsxs)("select",{value:y,onChange:e=>I(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[(0,n.jsx)("option",{value:"",children:"Custom Message"}),h.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,n.jsx)("input",{type:"text",value:C.title,onChange:e=>Z(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"Notification title",required:!0})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content *"}),(0,n.jsx)("textarea",{value:C.content,onChange:e=>Z(t=>({...t,content:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"Notification content",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"checkbox",id:"sendEmail",checked:C.sendEmail,onChange:e=>Z(t=>({...t,sendEmail:e.target.checked})),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),(0,n.jsx)("label",{htmlFor:"sendEmail",className:"text-sm text-gray-700",children:"Send via Email"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"checkbox",id:"sendInApp",checked:C.sendInApp,onChange:e=>Z(t=>({...t,sendInApp:e.target.checked})),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),(0,n.jsx)("label",{htmlFor:"sendInApp",className:"text-sm text-gray-700",children:"Send In-App Notification"})]})]})]})]}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsxs)("button",{type:"submit",disabled:t||0===p.length,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[(0,n.jsx)(u.Z,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:t?"Sending...":"Send to ".concat(p.length," user(s)")})]})})]})]})}},9763:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var n=s(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let s=(0,n.forwardRef)((s,i)=>{let{color:c="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:m,...x}=s;return(0,n.createElement)("svg",{ref:i,...r,width:l,height:l,stroke:c,strokeWidth:d?24*Number(o)/Number(l):o,className:["lucide","lucide-".concat(a(e)),u].join(" "),...x},[...t.map(e=>{let[t,s]=e;return(0,n.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},2252:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2660:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5302:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4743:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},2369:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5805:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9376:function(e,t,s){"use strict";var n=s(5475);s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=8275)}),_N_E=e.O()}]);