(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4885],{1259:function(e,t,s){Promise.resolve().then(s.bind(s,143))},143:function(e,t,s){"use strict";s.r(t);var a=s(7437),r=s(2265),l=s(9376),n=s(2660),c=s(5302),i=s(2252),d=s(9397),o=s(8736),x=s(2208),m=s(5868),u=s(8930);t.default=()=>{let e=(0,l.useRouter)(),[t,s]=(0,r.useState)([]),[h,p]=(0,r.useState)(!0),[y,g]=(0,r.useState)(null),[j,f]=(0,r.useState)(null),[v,b]=(0,r.useState)(!1),[N,k]=(0,r.useState)(!1);(0,r.useEffect)(()=>{w()},[]);let w=async()=>{try{p(!0);let e=await fetch("/api/admin/notifications/templates"),t=await e.json();t.success?s(t.templates):g("Failed to fetch templates")}catch(e){console.error("Error fetching templates:",e),g("Failed to fetch templates")}finally{p(!1)}},Z=e=>{f(e),b(!0)},C=e=>{f(e),b(!0)},M=async e=>{if(confirm("Are you sure you want to delete this template?"))try{let a=await fetch("/api/admin/notifications/templates/".concat(e),{method:"DELETE"});(await a.json()).success?s(t.filter(t=>t.id!==e)):g("Failed to delete template")}catch(e){console.error("Error deleting template:",e),g("Failed to delete template")}},_=e=>{switch(e){case"order_placed":return"bg-blue-100 text-blue-800";case"order_shipped":return"bg-green-100 text-green-800";case"order_delivered":return"bg-emerald-100 text-emerald-800";case"price_drop_alert":return"bg-orange-100 text-orange-800";case"review_request":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return v&&j?(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsx)("button",{onClick:()=>b(!1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Template Preview"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:j.name})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Template Details"}),(0,a.jsx)("span",{className:"px-3 py-1 text-sm font-medium rounded-full ".concat(_(j.type)),children:j.type.replace("_"," ")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.type.replace("_"," ")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("p",{className:"inline-flex items-center space-x-1 ".concat(j.isActive?"text-green-600":"text-red-600"),children:[j.isActive?(0,a.jsx)(c.Z,{className:"w-4 h-4"}):(0,a.jsx)(i.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:j.isActive?"Active":"Inactive"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Created"}),(0,a.jsx)("p",{className:"text-gray-900",children:S(j.createdAt)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-gray-900",children:j.subject})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content"}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,a.jsx)("div",{className:"text-gray-900 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:j.content}})})]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Notification Templates"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage notification templates"})]})]}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,a.jsx)(d.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Create Template"})]})]}),y&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2",children:[(0,a.jsx)(i.Z,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-700",children:y})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:h?(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})}):0===t.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(o.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No templates found"}),(0,a.jsx)("button",{onClick:()=>k(!0),className:"mt-2 text-green-600 hover:text-green-700 underline",children:"Create your first template"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:t.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(o.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(_(e.type)),children:e.type.replace("_"," ")}),e.isActive?(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",children:"Active"}):(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800",children:"Inactive"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:e.subject}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Created ",S(e.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>Z(e),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Preview",children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>C(e),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Edit",children:(0,a.jsx)(m.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>M(e.id),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Delete",children:(0,a.jsx)(u.Z,{className:"w-4 h-4"})})]})]})},e.id))})})]})}},9763:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var a=s(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),n=(e,t)=>{let s=(0,a.forwardRef)((s,n)=>{let{color:c="currentColor",size:i=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:x="",children:m,...u}=s;return(0,a.createElement)("svg",{ref:n,...r,width:i,height:i,stroke:c,strokeWidth:o?24*Number(d)/Number(i):d,className:["lucide","lucide-".concat(l(e)),x].join(" "),...u},[...t.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},2252:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2660:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5302:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2208:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8736:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9397:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5868:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},8930:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},9376:function(e,t,s){"use strict";var a=s(5475);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=1259)}),_N_E=e.O()}]);