(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4122],{3488:function(e,t,a){Promise.resolve().then(a.bind(a,2192))},2192:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return z}});var s=a(7437),r=a(2265),l=a(9397),n=a(5863),i=a(3247),o=a(740),c=a(8736),d=a(7689),u=a(8930),x=a(5868),m=a(9763);let g=(0,m.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var p=a(8728),h=a(1380),y=a(2489),f=a(3229);let v=(0,m.Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),b=(0,m.Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),j=e=>{let{faq:t,index:a,totalCount:l,isEditing:n,onEdit:i,onUpdate:o,onDelete:c,onMove:d}=e,[m,g]=(0,r.useState)({question:t.question,answer:t.answer});return(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 bg-white",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"bg-gray-100 text-gray-600 px-2 py-1 rounded text-sm font-medium",children:["#",a+1]}),(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("button",{onClick:()=>d(t,"up"),disabled:0===a,className:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move up",children:(0,s.jsx)(v,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>d(t,"down"),disabled:a===l-1,className:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",title:"Move down",children:(0,s.jsx)(b,{className:"w-4 h-4"})})]})]}),(0,s.jsx)("div",{className:"flex space-x-2",children:n?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>{if(!m.question.trim()||!m.answer.trim()){alert("Please fill in both question and answer");return}o({...t,question:m.question,answer:m.answer})},className:"p-1 text-green-600 hover:text-green-700",title:"Save",children:(0,s.jsx)(f.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>{g({question:t.question,answer:t.answer}),i(null)},className:"p-1 text-gray-400 hover:text-gray-600",title:"Cancel",children:(0,s.jsx)(y.Z,{className:"w-4 h-4"})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>i(t),className:"p-1 text-blue-600 hover:text-blue-700",title:"Edit",children:(0,s.jsx)(x.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>c(t.id),className:"p-1 text-red-600 hover:text-red-700",title:"Delete",children:(0,s.jsx)(u.Z,{className:"w-4 h-4"})})]})})]}),n?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question"}),(0,s.jsx)("input",{type:"text",value:m.question,onChange:e=>g({...m,question:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Answer"}),(0,s.jsx)("textarea",{value:m.answer,onChange:e=>g({...m,answer:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:t.question}),(0,s.jsx)("p",{className:"text-gray-600 whitespace-pre-wrap",children:t.answer})]})]})};var N=e=>{let{isOpen:t,onClose:a,product:n}=e,[i,o]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null),[m,g]=(0,r.useState)(!1),[p,h]=(0,r.useState)({question:"",answer:""});(0,r.useEffect)(()=>{t&&n&&v()},[t,n]);let v=async()=>{try{d(!0);let e=await fetch("/api/products/".concat(n.id,"/faqs")),t=await e.json();t.success&&o(t.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{d(!1)}},b=async()=>{if(!p.question.trim()||!p.answer.trim()){alert("Please fill in both question and answer");return}try{let e=await fetch("/api/products/".concat(n.id,"/faqs"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)}),t=await e.json();t.success?(o(e=>[...e,t.data]),h({question:"",answer:""}),g(!1)):alert("Failed to add FAQ")}catch(e){console.error("Error adding FAQ:",e),alert("Failed to add FAQ")}},N=async e=>{try{let t=await fetch("/api/products/".concat(n.id,"/faqs/").concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await t.json();a.success?(o(t=>t.map(t=>t.id===e.id?a.data:t)),x(null)):alert("Failed to update FAQ")}catch(e){console.error("Error updating FAQ:",e),alert("Failed to update FAQ")}},w=async e=>{if(confirm("Are you sure you want to delete this FAQ?"))try{let t=await fetch("/api/products/".concat(n.id,"/faqs/").concat(e),{method:"DELETE"});(await t.json()).success?o(t=>t.filter(t=>t.id!==e)):alert("Failed to delete FAQ")}catch(e){console.error("Error deleting FAQ:",e),alert("Failed to delete FAQ")}},C=async(e,t)=>{let a=i.findIndex(t=>t.id===e.id),s="up"===t?a-1:a+1;if(s<0||s>=i.length)return;let r=[...i];[r[a],r[s]]=[r[s],r[a]],r[a].position=a,r[s].position=s,o(r),await N(r[s]),await N(r[a])};return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage FAQs - ",n.name]}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(y.Z,{className:"w-6 h-6"})})]}),(0,s.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("button",{onClick:()=>g(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Add FAQ"})]})}),m&&(0,s.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add New FAQ"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Question"}),(0,s.jsx)("input",{type:"text",value:p.question,onChange:e=>h({...p,question:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter the question"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Answer"}),(0,s.jsx)("textarea",{value:p.answer,onChange:e=>h({...p,answer:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter the answer"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:b,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,s.jsx)(f.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Save FAQ"})]}),(0,s.jsx)("button",{onClick:()=>{g(!1),h({question:"",answer:""})},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors",children:"Cancel"})]})]})]}),c?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Loading FAQs..."})}):0===i.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500",children:"No FAQs found. Add your first FAQ above."})}):(0,s.jsx)("div",{className:"space-y-4",children:i.map((e,t)=>(0,s.jsx)(j,{faq:e,index:t,totalCount:i.length,isEditing:(null==u?void 0:u.id)===e.id,onEdit:x,onUpdate:N,onDelete:w,onMove:C},e.id))})]})]})}):null},w=a(4794);let C=e=>{var t;let{variation:a,isEditing:l,onEdit:n,onUpdate:i,onDelete:o}=e,[c,d]=(0,r.useState)({name:a.name,value:a.value,price:(null===(t=a.price)||void 0===t?void 0:t.toString())||"",pricingMode:a.pricingMode||"INCREMENT"});return(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-white",children:(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsx)("div",{className:"flex-1",children:l?(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("input",{type:"text",value:c.name,onChange:e=>d({...c,name:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Value"}),(0,s.jsx)("input",{type:"text",value:c.value,onChange:e=>d({...c,value:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:c.price,onChange:e=>d({...c,price:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Pricing Mode"}),(0,s.jsxs)("select",{value:c.pricingMode,onChange:e=>d({...c,pricingMode:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"INCREMENT",children:"Add to Base"}),(0,s.jsx)("option",{value:"REPLACE",children:"Replace Base"}),(0,s.jsx)("option",{value:"FIXED",children:"Fixed Price"})]})]})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Name"}),(0,s.jsx)("div",{className:"font-medium",children:a.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Value"}),(0,s.jsx)("div",{className:"font-medium",children:a.value})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Price Adjustment"}),(0,s.jsx)("div",{className:"font-medium",children:a.price?(0,h.T4)(a.price):"No adjustment"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Mode"}),(0,s.jsx)("div",{className:"font-medium text-xs",children:"REPLACE"===a.pricingMode?"Replace":"FIXED"===a.pricingMode?"Fixed":"Add to Base"})]})]})}),(0,s.jsx)("div",{className:"flex space-x-2 ml-4",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>{if(!c.name.trim()||!c.value.trim()){alert("Please fill in both name and value");return}i({...a,name:c.name,value:c.value,price:c.price?parseFloat(c.price):void 0,pricingMode:c.pricingMode})},className:"p-1 text-green-600 hover:text-green-700",title:"Save",children:(0,s.jsx)(f.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>{var e;d({name:a.name,value:a.value,price:(null===(e=a.price)||void 0===e?void 0:e.toString())||"",pricingMode:a.pricingMode||"INCREMENT"}),n(null)},className:"p-1 text-gray-400 hover:text-gray-600",title:"Cancel",children:(0,s.jsx)(y.Z,{className:"w-4 h-4"})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>n(a),className:"p-1 text-blue-600 hover:text-blue-700",title:"Edit",children:(0,s.jsx)(x.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>o(a.id),className:"p-1 text-red-600 hover:text-red-700",title:"Delete",children:(0,s.jsx)(u.Z,{className:"w-4 h-4"})})]})})]})})};var k=e=>{let{isOpen:t,onClose:a,product:n}=e,[i,o]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null),[m,g]=(0,r.useState)(!1),[p,h]=(0,r.useState)({name:"",value:"",price:"",pricingMode:"INCREMENT"});(0,r.useEffect)(()=>{t&&n&&v()},[t,n]);let v=async()=>{try{d(!0);let e=await fetch("/api/products/".concat(n.id,"/variations")),t=await e.json();t.success&&o(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{d(!1)}},b=async()=>{if(!p.name.trim()||!p.value.trim()){alert("Please fill in both name and value");return}try{let e=await fetch("/api/products/".concat(n.id,"/variations"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:p.name,value:p.value,price:p.price?parseFloat(p.price):null,pricingMode:p.pricingMode})}),t=await e.json();t.success?(o(e=>[...e,t.data]),h({name:"",value:"",price:"",pricingMode:"INCREMENT"}),g(!1)):alert(t.error||"Failed to add variation")}catch(e){console.error("Error adding variation:",e),alert("Failed to add variation")}},j=async e=>{try{let t=await fetch("/api/products/".concat(n.id,"/variations/").concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await t.json();a.success?(o(t=>t.map(t=>t.id===e.id?a.data:t)),x(null)):alert(a.error||"Failed to update variation")}catch(e){console.error("Error updating variation:",e),alert("Failed to update variation")}},N=async e=>{if(confirm("Are you sure you want to delete this variation?"))try{let t=await fetch("/api/products/".concat(n.id,"/variations/").concat(e),{method:"DELETE"});(await t.json()).success?o(t=>t.filter(t=>t.id!==e)):alert("Failed to delete variation")}catch(e){console.error("Error deleting variation:",e),alert("Failed to delete variation")}},k=i.reduce((e,t)=>(e[t.name]||(e[t.name]=[]),e[t.name].push(t),e),{});return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center space-x-2",children:[(0,s.jsx)(w.Z,{className:"w-5 h-5"}),(0,s.jsxs)("span",{children:["Manage Variations - ",n.name]})]}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(y.Z,{className:"w-6 h-6"})})]}),(0,s.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("button",{onClick:()=>g(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Add Variation"})]})}),m&&(0,s.jsxs)("div",{className:"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50",children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add New Variation"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name (e.g., Size, Color)"}),(0,s.jsx)("input",{type:"text",value:p.name,onChange:e=>h({...p,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Size"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value (e.g., Large, Red)"}),(0,s.jsx)("input",{type:"text",value:p.value,onChange:e=>h({...p,value:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Large"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:p.price,onChange:e=>h({...p,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"0.00"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pricing Mode"}),(0,s.jsxs)("select",{value:p.pricingMode,onChange:e=>h({...p,pricingMode:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,s.jsx)("option",{value:"INCREMENT",children:"Add to Base Price"}),(0,s.jsx)("option",{value:"REPLACE",children:"Replace Base Price"}),(0,s.jsx)("option",{value:"FIXED",children:"Use as Fixed Price"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"How this variation affects the final price"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3 mt-4",children:[(0,s.jsxs)("button",{onClick:b,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,s.jsx)(f.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Save Variation"})]}),(0,s.jsx)("button",{onClick:()=>{g(!1),h({name:"",value:"",price:"",pricingMode:"INCREMENT"})},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors",children:"Cancel"})]})]}),c?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Loading variations..."})}):0===i.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500",children:"No variations found. Add your first variation above."})}):(0,s.jsx)("div",{className:"space-y-6",children:Object.entries(k).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-4 capitalize",children:t}),(0,s.jsx)("div",{className:"space-y-3",children:a.map(e=>(0,s.jsx)(C,{variation:e,isEditing:(null==u?void 0:u.id)===e.id,onEdit:x,onUpdate:j,onDelete:N},e.id))})]},t)})})]})]})}):null};let S=(0,m.Z)("Move",[["polyline",{points:"5 9 2 12 5 15",key:"1r5uj5"}],["polyline",{points:"9 5 12 2 15 5",key:"5v383o"}],["polyline",{points:"15 19 12 22 9 19",key:"g7qi8m"}],["polyline",{points:"19 9 22 12 19 15",key:"tpp73q"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}]]);var F=a(2208),E=a(3113),A=a(9374),P=a(9658),Z=a(1473),M=a(7586),L=a(401),I=a(6143),D=e=>{let{isOpen:t,onClose:a,onSelect:n,allowedTypes:o=["image","video","document"],title:c="Select Media",multiple:d=!1,currentSelection:u}=e,[x,m]=(0,r.useState)([]),[g,p]=(0,r.useState)(!0),[h,f]=(0,r.useState)(""),[v,b]=(0,r.useState)(null),[j,N]=(0,r.useState)([]),[w,C]=(0,r.useState)("grid"),[k,S]=(0,r.useState)(!1),F=(0,r.useRef)(null);(0,r.useEffect)(()=>{t&&q()},[t]);let D=e=>{d?j.some(t=>t.key===e.key)?N(j.filter(t=>t.key!==e.key)):N([...j,e]):b(e)},R=()=>{d?j.forEach(e=>n(e)):v&&n(v),a()},q=async()=>{try{p(!0);let e=await fetch("/api/media/list"),t=await e.json();t.success&&m(t.files.filter(e=>o.includes(e.type)))}catch(e){console.error("Error loading files:",e)}finally{p(!1)}},T=x.filter(e=>e.name.toLowerCase().includes(h.toLowerCase())),O=e=>{switch(e){case"image":return(0,s.jsx)(E.Z,{className:"w-5 h-5"});case"video":return(0,s.jsx)(A.Z,{className:"w-5 h-5"});default:return(0,s.jsx)(P.Z,{className:"w-5 h-5"})}},U=async e=>{S(!0);let t=Array.from(e).map(async e=>{let t=new FormData;t.append("file",e),t.append("folder","uploads");try{let e=await fetch("/api/media/upload",{method:"POST",body:t});return await e.json()}catch(t){return console.error("Upload failed for ".concat(e.name,":"),t),{success:!1,error:t instanceof Error?t.message:"Upload failed"}}}),a=(await Promise.all(t)).filter(e=>e.success);if(a.length>0){var s;q(),!d&&(null===(s=a[0])||void 0===s?void 0:s.file)&&b(a[0].file)}S(!1),F.current&&(F.current.value="")};return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:c}),(0,s.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(y.Z,{className:"w-6 h-6"})})]}),(0,s.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search files...",value:h,onChange:e=>f(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("button",{onClick:()=>{var e;null===(e=F.current)||void 0===e||e.click()},disabled:k,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:k?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Uploading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Upload"]})}),(0,s.jsxs)("div",{className:"flex border border-gray-300 rounded-lg",children:[(0,s.jsx)("button",{onClick:()=>C("grid"),className:"p-2 ".concat("grid"===w?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"),children:(0,s.jsx)(Z.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>C("list"),className:"p-2 ".concat("list"===w?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"),children:(0,s.jsx)(M.Z,{className:"w-4 h-4"})})]})]})]}),(0,s.jsx)("input",{ref:F,type:"file",accept:"image/*,video/*,.pdf",multiple:!0,onChange:e=>e.target.files&&U(e.target.files),className:"hidden"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto p-6",children:g?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})}):0===T.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(E.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 text-lg",children:"No files found"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search or upload new files"})]}):"grid"===w?(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:T.map(e=>{let t=d?j.some(t=>t.key===e.key):(null==v?void 0:v.key)===e.key;return(0,s.jsxs)("div",{onClick:()=>D(e),className:"bg-white rounded-lg border-2 p-3 cursor-pointer transition-all hover:shadow-md ".concat(t?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,s.jsxs)("div",{className:"relative",children:[t&&(0,s.jsx)("div",{className:"absolute top-2 right-2 bg-green-600 text-white rounded-full p-1",children:(0,s.jsx)(L.Z,{className:"w-3 h-3"})}),(0,s.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden",children:"image"===e.type?(0,s.jsx)("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"text-gray-400",children:O(e.type)})})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",title:e.name,children:e.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:(0,I.sS)(e.size)})]})]},e.key)})}):(0,s.jsx)("div",{className:"space-y-2",children:T.map(e=>{let t=d?j.some(t=>t.key===e.key):(null==v?void 0:v.key)===e.key;return(0,s.jsxs)("div",{onClick:()=>D(e),className:"flex items-center p-3 rounded-lg border cursor-pointer transition-all ".concat(t?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3 overflow-hidden",children:"image"===e.type?(0,s.jsx)("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"text-gray-400",children:O(e.type)})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," • ",(0,I.sS)(e.size)," • ",e.folder||"Root"]})]}),t&&(0,s.jsx)("div",{className:"bg-green-600 text-white rounded-full p-1 ml-3",children:(0,s.jsx)(L.Z,{className:"w-4 h-4"})})]},e.key)})})}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500",children:d?"".concat(j.length," file(s) selected"):v?"Selected: ".concat(v.name):"No file selected"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("button",{onClick:a,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{onClick:()=>{R(),b(null),N([])},disabled:d?0===j.length:!v,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Select Files":"Select File"})]})]})]})}):null},R=e=>{let{value:t,onChange:a,label:l="Image",placeholder:n="Select an image",className:i=""}=e,[o,c]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:i,children:[l&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l}),(0,s.jsxs)("div",{className:"space-y-3",children:[t&&(0,s.jsxs)("div",{className:"relative inline-block",children:[(0,s.jsx)("img",{src:t,alt:"Selected image",className:"w-32 h-32 object-cover rounded-lg border border-gray-300"}),(0,s.jsx)("button",{type:"button",onClick:()=>{a("")},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,s.jsx)(y.Z,{className:"w-3 h-3"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>c(!0),className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsx)(E.Z,{className:"w-4 h-4 mr-2"}),t?"Change Image":"Select Image"]}),!t&&(0,s.jsx)("span",{className:"text-sm text-gray-500",children:n})]}),(0,s.jsx)("div",{children:(0,s.jsx)("input",{type:"url",value:t||"",onChange:e=>a(e.target.value),placeholder:"Or enter image URL directly",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"})})]}),(0,s.jsx)(D,{isOpen:o,onClose:()=>c(!1),onSelect:e=>{a(e.url)},allowedTypes:["image"],title:"Select Image"})]})},q=e=>{let{images:t,onChange:a,productName:n="Product"}=e,[i,o]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),[x,m]=(0,r.useState)(null),g=e=>{a(t.filter((t,a)=>a!==e).map((e,t)=>({...e,position:t})))},p=(e,s)=>{let r=[...t];r[e]={...r[e],alt:s},a(r)},h=(e,s)=>{if(e===s)return;let r=[...t],[l]=r.splice(e,1);r.splice(s,0,l),a(r.map((e,t)=>({...e,position:t})))},f=(e,t)=>{m(t),e.dataTransfer.effectAllowed="move"},v=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},b=(e,t)=>{e.preventDefault(),null!==x&&(h(x,t),m(null))};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Product Images"}),(0,s.jsxs)("button",{type:"button",onClick:()=>o(!0),className:"bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 text-sm",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Add Image"})]})]}),0===t.length?(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[(0,s.jsx)(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"No images added yet"}),(0,s.jsx)("button",{type:"button",onClick:()=>o(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Add First Image"})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:t.map((e,t)=>(0,s.jsxs)("div",{draggable:!0,onDragStart:e=>f(e,t),onDragOver:v,onDrop:e=>b(e,t),className:"relative group border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow ".concat(x===t?"opacity-50":""),children:[(0,s.jsx)("div",{className:"absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-10",children:0===t?"Featured":"#".concat(t+1)}),(0,s.jsx)("div",{className:"absolute top-2 right-2 bg-black bg-opacity-75 text-white p-1 rounded cursor-move opacity-0 group-hover:opacity-100 transition-opacity z-10",children:(0,s.jsx)(S,{className:"w-4 h-4"})}),(0,s.jsxs)("div",{className:"aspect-square relative",children:[(0,s.jsx)("img",{src:e.url,alt:e.alt,className:"w-full h-full object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2",children:[(0,s.jsx)("button",{type:"button",onClick:()=>window.open(e.url,"_blank"),className:"bg-white text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors",title:"View full size",children:(0,s.jsx)(F.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>g(t),className:"bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-colors",title:"Remove image",children:(0,s.jsx)(y.Z,{className:"w-4 h-4"})})]})})]}),(0,s.jsx)("div",{className:"p-3",children:(0,s.jsx)("input",{type:"text",value:e.alt,onChange:e=>p(t,e.target.value),placeholder:"Image description (alt text)",className:"w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-green-500"})})]},t))}),i&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Select Image"}),(0,s.jsx)("button",{onClick:()=>o(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(y.Z,{className:"w-6 h-6"})})]}),(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)(R,{value:"",onChange:e=>{let s={url:e,alt:"".concat(n," - Image ").concat(t.length+1),position:t.length};a([...t,s]),o(!1)},label:"",placeholder:"Select or enter image URL"})})]})}),t.length>0&&(0,s.jsxs)("div",{className:"text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Tips:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"The first image will be used as the featured image"}),(0,s.jsx)("li",{children:"Drag and drop images to reorder them"}),(0,s.jsx)("li",{children:"Add descriptive alt text for better SEO and accessibility"}),(0,s.jsx)("li",{children:"Recommended image size: 800x800px or larger"})]})]})]})},T=a(875),O=e=>{let{categories:t,selectedCategoryIds:a,onChange:l,label:n="Categories",placeholder:i="Select categories...",required:o=!1}=e,[c,d]=(0,r.useState)(!1),[u,x]=(0,r.useState)(""),m=(0,r.useRef)(null),g=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&(d(!1),x(""))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let p=t.filter(e=>e.name.toLowerCase().includes(u.toLowerCase())),h=t.filter(e=>a.includes(e.id)),f=e=>{a.includes(e)?l(a.filter(t=>t!==e)):l([...a,e])},v=e=>{l(a.filter(t=>t!==e))};return(0,s.jsxs)("div",{className:"relative",ref:m,children:[n&&(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n,o&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("div",{className:"w-full min-h-[42px] px-3 py-2 border border-gray-300 rounded-lg focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500 cursor-pointer bg-white",onClick:()=>d(!c),children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 items-center",children:[h.length>0?h.map(e=>(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800",children:[e.name,(0,s.jsx)("button",{type:"button",onClick:t=>{t.stopPropagation(),v(e.id)},className:"ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-green-200 focus:outline-none",children:(0,s.jsx)(y.Z,{className:"w-3 h-3"})})]},e.id)):(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:i}),(0,s.jsx)("div",{className:"flex-1 flex justify-end",children:(0,s.jsx)(T.Z,{className:"w-5 h-5 text-gray-400 transition-transform ".concat(c?"transform rotate-180":"")})})]})}),c&&(0,s.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,s.jsx)("input",{ref:g,type:"text",placeholder:"Search categories...",value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",autoFocus:!0})}),h.length>0&&(0,s.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,s.jsxs)("button",{type:"button",onClick:()=>{l([])},className:"text-xs text-red-600 hover:text-red-800 font-medium",children:["Clear all (",h.length,")"]})}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto",children:p.length>0?p.map(e=>{let t=a.includes(e.id);return(0,s.jsxs)("div",{className:"px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ".concat(t?"bg-green-50":""),onClick:()=>f(e.id),children:[(0,s.jsx)("span",{className:"text-sm ".concat(t?"text-green-800 font-medium":"text-gray-700"),children:e.name}),t&&(0,s.jsx)(L.Z,{className:"w-4 h-4 text-green-600"})]},e.id)}):(0,s.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No categories found"})})]})]})};let U=e=>{var t,a,l,n;let{isOpen:i,onClose:o,onSave:c,categories:d,product:u}=e,[x,m]=(0,r.useState)({name:(null==u?void 0:u.name)||"",slug:(null==u?void 0:u.slug)||"",description:(null==u?void 0:u.description)||"",shortDescription:(null==u?void 0:u.shortDescription)||"",comparePrice:(null==u?void 0:null===(t=u.comparePrice)||void 0===t?void 0:t.toString())||"",categoryId:(null==u?void 0:null===(a=u.category)||void 0===a?void 0:a.id)||"",isFeatured:(null==u?void 0:u.isFeatured)||!1}),[g,p]=(0,r.useState)(()=>{var e;return(null==u?void 0:u.productCategories)&&u.productCategories.length>0?u.productCategories.map(e=>e.category.id):(null==u?void 0:null===(e=u.category)||void 0===e?void 0:e.id)?[u.category.id]:[]}),[y,f]=(0,r.useState)((null==u?void 0:null===(l=u.images)||void 0===l?void 0:l.map(e=>({id:e.id||"",url:e.url,alt:e.alt||"",position:e.position||0})))||[]),[v,b]=(0,r.useState)((null==u?void 0:null===(n=u.variants)||void 0===n?void 0:n.map(e=>{var t;return{id:e.id,name:e.name,value:e.value,price:(null===(t=e.price)||void 0===t?void 0:t.toString())||"0"}}))||[]),[j,N]=(0,r.useState)(!1),[w,C]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(null==u?void 0:u.variants)?b(u.variants.map(e=>{var t;return{id:e.id,name:e.name,value:e.value,price:(null===(t=e.price)||void 0===t?void 0:t.toString())||"0"}})):b([])},[u]);let k=(e,t,a)=>{let s=[...v];s[e]={...s[e],[t]:a},b(s)},S=e=>{b(v.filter((t,a)=>a!==e))},F=async e=>{e.preventDefault(),C(!0);try{let e=u?"/api/products/".concat(u.id):"/api/products",t=await fetch(e,{method:u?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...x,slug:x.slug||(0,h.GD)(x.name),comparePrice:x.comparePrice?parseFloat(x.comparePrice):null,categoryIds:g,images:y.map((e,t)=>({url:e.url,alt:e.alt||x.name,position:t})),variations:v.filter(e=>e.name&&e.value).map(e=>({name:e.name,value:e.value,price:e.price?parseFloat(e.price):0}))})}),a=await t.json();t.ok?(a.warnings&&a.warnings.length>0&&alert("Product saved successfully!\n\nWarnings:\n".concat(a.warnings.join("\n"))),c(),o()):alert("Failed to save product: ".concat(a.error||"Unknown error"))}catch(e){alert("Failed to save product")}finally{C(!1)}};return i?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:u?"Edit Product":"Add Product"}),(0,s.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:F,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name"}),(0,s.jsx)("input",{type:"text",value:x.name,onChange:e=>{let t=e.target.value;m({...x,name:t,slug:x.slug||(0,h.GD)(t)})},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["URL Slug",(0,s.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(SEO-friendly URL)"})]}),(0,s.jsx)("input",{type:"text",value:x.slug,onChange:e=>m({...x,slug:(0,h.GD)(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"auto-generated-from-name"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Preview: /products/",x.slug||(0,h.GD)(x.name)]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(O,{categories:d,selectedCategoryIds:g,onChange:p,label:"Categories",placeholder:"Select categories...",required:!0})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Compare Price (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:x.comparePrice,onChange:e=>m({...x,comparePrice:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Enter compare price in Rupees"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Short Description"}),(0,s.jsx)("input",{type:"text",value:x.shortDescription,onChange:e=>m({...x,shortDescription:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{value:x.description,onChange:e=>m({...x,description:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,s.jsx)(q,{images:y,onChange:e=>f(e.map(e=>({id:e.id||"",url:e.url,alt:e.alt||"",position:e.position||0}))),productName:x.name||"Product"}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Product Variations"}),(0,s.jsx)("button",{type:"button",onClick:()=>N(!j),className:"text-sm text-blue-600 hover:text-blue-700",children:j?"Hide Variations":"Add Variations"})]}),j&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-sm text-gray-600 mb-3",children:"Add variations like Size, Color, Material, etc. Each variation can have its own price adjustment and stock."}),v.map((e,t)=>(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Name (e.g., Size)"}),(0,s.jsx)("input",{type:"text",value:e.name,onChange:e=>k(t,"name",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"Size"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Value (e.g., Large)"}),(0,s.jsx)("input",{type:"text",value:e.value,onChange:e=>k(t,"value",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"Large"})]}),(0,s.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Price Adjustment (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:e.price,onChange:e=>k(t,"price",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"0.00"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>S(t),className:"px-2 py-1 text-red-600 hover:text-red-700 text-sm",title:"Remove variation",children:"\xd7"})]})]},t)),(0,s.jsx)("button",{type:"button",onClick:()=>{b([...v,{name:"",value:"",price:"0"}])},className:"w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors",children:"+ Add Variation"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"featured",checked:x.isFeatured,onChange:e=>m({...x,isFeatured:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,s.jsx)("label",{htmlFor:"featured",className:"ml-2 text-sm text-gray-700",children:"Featured Product"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:o,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:w,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50",children:w?"Saving...":u?"Update":"Create"})]})]})]})}):null};var z=()=>{let[e,t]=(0,r.useState)(""),[a,m]=(0,r.useState)([]),[y,f]=(0,r.useState)(!1),[v,b]=(0,r.useState)(!1),[j,w]=(0,r.useState)(null),[C,S]=(0,r.useState)([]),[F,E]=(0,r.useState)([]),[A,P]=(0,r.useState)(!0),[Z,M]=(0,r.useState)(null),[L,I]=(0,r.useState)(!1),[D,R]=(0,r.useState)(!1),[q,T]=(0,r.useState)(!1),[O,z]=(0,r.useState)(null),[V,_]=(0,r.useState)(!1),[Q,J]=(0,r.useState)(null);(0,r.useEffect)(()=>{B(),H()},[]);let B=async()=>{try{P(!0);let e=await fetch("/api/products?limit=1000"),t=await e.json();t.success?S(t.data):M("Failed to fetch products")}catch(e){M("Failed to fetch products")}finally{P(!1)}},H=async()=>{try{let e=await fetch("/api/categories"),t=await e.json();t.success&&E(t.data)}catch(e){}},G=C.filter(t=>{var a,s;if(!e)return!0;let r=t.name.toLowerCase().includes(e.toLowerCase()),l=null===(a=t.category)||void 0===a?void 0:a.name.toLowerCase().includes(e.toLowerCase()),n=null===(s=t.productCategories)||void 0===s?void 0:s.some(t=>t.category.name.toLowerCase().includes(e.toLowerCase()));return r||l||n}),K=e=>{m(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},W=e=>{w(e),b(!0)},X=async e=>{if(confirm("Are you sure you want to delete this product?"))try{let t=await fetch("/api/products/".concat(e),{method:"DELETE"}),a=await t.json();t.ok?("soft_delete"===a.type?alert("Product deactivated: ".concat(a.message)):alert("Product deleted successfully"),S(t=>t.filter(t=>t.id!==e))):alert("Failed to delete product: ".concat(a.error||"Unknown error"))}catch(e){alert("Failed to delete product")}},Y=async e=>{if(0===a.length){alert("Please select products first");return}if(confirm({delete:"Are you sure you want to delete the selected products? This action cannot be undone.",activate:"Are you sure you want to activate the selected products?",deactivate:"Are you sure you want to deactivate the selected products?",feature:"Are you sure you want to feature the selected products?",unfeature:"Are you sure you want to unfeature the selected products?"}[e]))try{R(!0);let t=await fetch("/api/products/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,productIds:a})}),s=await t.json();s.success?(alert(s.message),m([]),B()):alert("Failed to ".concat(e," products: ").concat(s.error))}catch(t){console.error("Error in bulk ".concat(e,":"),t),alert("Failed to ".concat(e," products"))}finally{R(!1)}},$=e=>{z(e),T(!0)},ee=e=>{J(e),_(!0)},et=e=>({Skincare:"bg-green-100 text-green-800","Hair Care":"bg-purple-100 text-purple-800","Body Care":"bg-blue-100 text-blue-800",cleanser:"bg-blue-100 text-blue-800",serum:"bg-purple-100 text-purple-800",moisturizer:"bg-green-100 text-green-800",mask:"bg-yellow-100 text-yellow-800",exfoliator:"bg-pink-100 text-pink-800","eye-care":"bg-indigo-100 text-indigo-800"})[e]||"bg-gray-100 text-gray-800";return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Products"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your skincare product catalog"})]}),(0,s.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,s.jsxs)("button",{onClick:()=>f(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[(0,s.jsx)(l.Z,{className:"w-5 h-5 mr-2"}),"Add Product"]})})]})}),A&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(n.Z,{className:"w-8 h-8 animate-spin text-green-600"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading products..."})]}),Z&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,s.jsx)("p",{className:"text-red-600",children:Z}),(0,s.jsx)("button",{onClick:B,className:"mt-2 text-red-600 hover:text-red-700 underline",children:"Try again"})]}),!A&&!Z&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search products...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),(0,s.jsx)("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,s.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("button",{onClick:()=>{let e=new Blob(['Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Variations\nSample Product,sample-product,"A great product description","Short description",99.99,149.99,Skincare,"Skincare;Face Care",yes,yes,"[{""name"":""Size"",""value"":""50ml"",""price"":99.99}]"\n# Instructions:\n# - Name: Required product name\n# - Slug: SEO-friendly URL (auto-generated if empty)\n# - Price: Required base price in rupees\n# - Category: Single category name\n# - Categories: Multiple categories separated by semicolons\n# - Featured: yes/no\n# - Active: yes/no (defaults to yes)\n# - Variations: JSON array of variations with name, value, and price'],{type:"text/csv"}),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="products_template.csv",a.click(),window.URL.revokeObjectURL(t)},className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Download CSV template",children:(0,s.jsx)(c.Z,{className:"w-5 h-5 text-gray-600"})}),(0,s.jsx)("button",{onClick:()=>{let e=new Blob([JSON.stringify({products:[{name:"Sample Product",slug:"sample-product",description:"A detailed product description",shortDescription:"Short description",price:99.99,comparePrice:149.99,categoryNames:["Skincare","Face Care"],isFeatured:!0,isActive:!0,variations:[{name:"Size",value:"50ml",price:99.99},{name:"Size",value:"100ml",price:179.99}]}]},null,2)],{type:"application/json"}),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="products_template.json",a.click(),window.URL.revokeObjectURL(t)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium text-gray-600",title:"Download JSON template",children:"JSON"})]}),(0,s.jsxs)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept=".csv,.json",e.onchange=async e=>{var t,a,s;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];if(r)try{I(!0);let e=await r.text(),t=[];if(r.name.toLowerCase().endsWith(".json"))try{let a=JSON.parse(e);if(a.products&&Array.isArray(a.products))t=a.products;else if(Array.isArray(a))t=a;else{alert('Invalid JSON format. Expected format: {"products": [...]} or direct array of products.');return}let s=[];for(let e of t){if(!e.name)continue;let t=[];e.category&&t.push(e.category),e.categoryNames&&Array.isArray(e.categoryNames)&&t.push(...e.categoryNames),0!==t.length&&(e.price&&e.price>0||e.variations&&e.variations.length>0)&&s.push({...e,categoryNames:t,slug:e.slug||(0,h.GD)(e.name),isFeatured:!!e.isFeatured,isActive:!1!==e.isActive})}t=s}catch(e){alert("Invalid JSON file. Please check the file format.");return}else{let r=e.split("\n").filter(e=>e.trim()&&!e.trim().startsWith("#")),l=r[0].split(",").map(e=>e.trim().replace(/"/g,"")),n=["Name"].filter(e=>!l.some(t=>t.toLowerCase().includes(e.toLowerCase())));if(n.length>0){alert("Missing required columns: ".concat(n.join(", ")));return}if(!l.some(e=>e.toLowerCase().includes("category"))){alert('Missing category information. Please include either "Category" or "Categories" column.');return}for(let e=1;e<r.length;e++){let n=r[e].trim();if(!n)continue;let i=n.split(",").map(e=>e.trim().replace(/"/g,"")),o=l.findIndex(e=>e.toLowerCase().includes("name")),c=l.findIndex(e=>e.toLowerCase().includes("slug")),d=l.findIndex(e=>e.toLowerCase().includes("variations")),u=o>=0&&i[o]||"",x=c>=0&&i[c]||"",m=d>=0&&i[d]||"",g=[];if(m)try{g=JSON.parse(m)}catch(e){}let p=[],y=l.findIndex(e=>"categories"===e.toLowerCase()),f=y>=0&&i[y]||"";if(f&&p.push(...f.split(";").map(e=>e.trim()).filter(e=>e)),0===p.length){let e=l.findIndex(e=>"category"===e.toLowerCase()),t=e>=0&&i[e]||"";t&&p.push(t)}let v=l.findIndex(e=>"price"===e.toLowerCase()),b=v>=0&&i[v]||"",j=parseFloat(b)||0,N=j>=0||g&&g.length>0,w=l.findIndex(e=>"description"===e.toLowerCase()),C=l.findIndex(e=>e.toLowerCase().includes("short description")),k=l.findIndex(e=>e.toLowerCase().includes("compare price")),S=l.findIndex(e=>"featured"===e.toLowerCase()),F=l.findIndex(e=>"active"===e.toLowerCase()),E={name:u,slug:(0,h.w)(x,u),description:w>=0&&i[w]||"",shortDescription:C>=0&&i[C]||"",price:j,comparePrice:k>=0&&parseFloat(i[k]||"0")||null,categoryNames:p,isFeatured:S>=0&&(null===(a=i[S])||void 0===a?void 0:a.toLowerCase())==="yes",isActive:!(F>=0)||(null===(s=i[F])||void 0===s?void 0:s.toLowerCase())!=="no",variations:g},A=p.length>0;E.name&&A&&N&&t.push(E)}}if(0===t.length){alert('No valid products found in the file. Please ensure:\n\n1. Each product has a Name\n2. Each product has at least one Category\n3. Price must be provided OR variations must have prices\n\nFor JSON: Use format {"products": [...]} or direct array\nFor CSV: Check the CSV template for the correct format.');return}let l=await fetch("/api/products/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({products:t})}),n=await l.json();if(n.success){let{success:e,failed:t,errors:a}=n.data,s="Import completed!\n✅ ".concat(e," products imported successfully");t>0&&(s+="\n❌ ".concat(t," products failed"),a.length>0&&(s+="\n\nErrors:\n".concat(a.slice(0,5).join("\n")),a.length>5&&(s+="\n... and ".concat(a.length-5," more errors")))),alert(s),e>0&&B()}else alert("Import failed: ".concat(n.error))}catch(e){alert("Failed to import products. Please check the file format.")}finally{I(!1)}},e.click()},disabled:L,className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",title:"Import products from CSV/JSON",children:[L?(0,s.jsx)(n.Z,{className:"w-4 h-4 mr-2 animate-spin"}):(0,s.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),L?"Importing...":"Import"]})]})]})}),a.length>0&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:[a.length," product",a.length>1?"s":""," selected"]}),(0,s.jsx)("button",{onClick:()=>m([]),className:"text-blue-600 hover:text-blue-700 text-sm",children:"Clear selection"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>Y("activate"),disabled:D,className:"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50",children:"Activate"}),(0,s.jsx)("button",{onClick:()=>Y("deactivate"),disabled:D,className:"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 disabled:opacity-50",children:"Deactivate"}),(0,s.jsx)("button",{onClick:()=>Y("feature"),disabled:D,className:"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50",children:"Feature"}),(0,s.jsx)("button",{onClick:()=>Y("unfeature"),disabled:D,className:"px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 disabled:opacity-50",children:"Unfeature"}),(0,s.jsxs)("button",{onClick:()=>Y("delete"),disabled:D,className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 flex items-center",children:[D?(0,s.jsx)(n.Z,{className:"w-4 h-4 mr-1 animate-spin"}):(0,s.jsx)(u.Z,{className:"w-4 h-4 mr-1"}),"Delete"]})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left",children:(0,s.jsx)("input",{type:"checkbox",checked:a.length===G.length&&G.length>0,onChange:()=>{m(a.length===G.length?[]:G.map(e=>e.id))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:G.map(e=>{var t,r;return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("input",{type:"checkbox",checked:a.includes(e.id),onChange:()=>K(e.id),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("img",{src:(null===(t=e.images[0])||void 0===t?void 0:t.url)||"/images/default-product.jpg",alt:(null===(r=e.images[0])||void 0===r?void 0:r.alt)||e.name,className:"w-12 h-12 rounded-lg object-cover mr-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.shortDescription})]})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.productCategories&&e.productCategories.length>0?e.productCategories.map(e=>(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(et(e.category.name)),children:e.category.name},e.category.id)):e.category&&(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(et(e.category.name)),children:e.category.name})})}),(0,s.jsxs)("td",{className:"px-6 py-4 text-sm text-gray-900",children:[e.variants&&e.variants.length>0?(()=>{let t=e.variants.map(e=>e.price).filter(e=>"number"==typeof e&&e>0);if(0===t.length)return"No pricing";let a=Math.min(...t),s=Math.max(...t);return a===s?(0,h.T4)(a):"".concat((0,h.T4)(a)," - ").concat((0,h.T4)(s))})():(0,s.jsx)("span",{className:"text-gray-500",children:"No variations"}),e.comparePrice&&(0,s.jsx)("span",{className:"text-xs text-gray-500 line-through ml-2",children:(0,h.T4)(e.comparePrice)})]}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.variants&&e.variants.length>0?(0,s.jsxs)("span",{className:"text-blue-600",children:[e.variants.length," variations"]}):(0,s.jsx)("span",{className:"text-gray-500",children:"No variations"})})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(e.isFeatured?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.isFeatured?"Featured":"Active"})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>W(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Edit Product",children:(0,s.jsx)(x.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>$(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Manage FAQs",children:(0,s.jsx)(g,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>ee(e),className:"p-1 text-gray-400 hover:text-purple-600 transition-colors",title:"Manage Variations",children:(0,s.jsx)(p.Z,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>X(e.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete Product",children:(0,s.jsx)(u.Z,{className:"w-4 h-4"})})]})})]},e.id)})})]})})}),(0,s.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",G.length," of ",C.length," products"]})]}),y&&(0,s.jsx)(U,{isOpen:y,onClose:()=>f(!1),onSave:B,categories:F}),v&&j&&(0,s.jsx)(U,{isOpen:v,onClose:()=>{b(!1),w(null)},onSave:B,categories:F,product:j}),q&&O&&(0,s.jsx)(N,{isOpen:q,onClose:()=>{T(!1),z(null)},product:O}),V&&Q&&(0,s.jsx)(k,{isOpen:V,onClose:()=>{_(!1),J(null)},product:Q})]})}},1380:function(e,t,a){"use strict";function s(e){return function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(isNaN(e))return"₹0";let a=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return"₹".concat(a)}(e,!0)}function r(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,t){return(e?r(e):r(t))||"product"}a.d(t,{GD:function(){return r},T4:function(){return s},w:function(){return l}})},6143:function(e,t,a){"use strict";a.d(t,{sS:function(){return l}});var s=a(4850);a(6395);var r=a(257);function l(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}new s.g({region:"auto",endpoint:"https://".concat(r.env.R2_ACCOUNT_ID,".r2.cloudflarestorage.com"),credentials:{accessKeyId:r.env.R2_ACCESS_KEY_ID,secretAccessKey:r.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0}),r.env.R2_BUCKET_NAME,r.env.R2_PUBLIC_URL||r.env.R2_ACCOUNT_ID},875:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},8736:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},740:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},5863:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4794:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},9397:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3229:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},8728:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5868:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])}},function(e){e.O(0,[411,4190,2971,2117,1744],function(){return e(e.s=3488)}),_N_E=e.O()}]);