(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6140],{1114:function(e,s,r){Promise.resolve().then(r.bind(r,2547))},2547:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return m}});var t=r(7437),a=r(2265);let n=(0,r(9763).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]);var o=r(9345),l=r(9202),i=r(8906),c=r(4766),d=r(8226),u=r(3229),m=()=>{let[e,s]=(0,a.useState)("general"),[r,m]=(0,a.useState)("idle"),[g,x]=(0,a.useState)({storeName:"Herbalicious",storeDescription:"Natural skincare products for radiant, healthy skin",storeEmail:"<EMAIL>",storePhone:"+91 99878 10707",storeAddress:"123 Wellness Street, Natural City, NC 12345",currency:"USD",timezone:"America/New_York",smtpHost:"smtp.gmail.com",smtpPort:"587",smtpUsername:"<EMAIL>",smtpPassword:"********",seoTitle:"Herbalicious - Natural Skincare Products",seoDescription:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin.",seoKeywords:"natural skincare, botanical, organic, herbal products",googleAnalyticsId:"UA-123456789-1",twoFactorAuth:!1,sessionTimeout:30,passwordPolicy:{minLength:8,requireUppercase:!0,requireNumbers:!0,requireSpecialChars:!0}}),h=[{id:"general",label:"General",icon:n},{id:"email",label:"Email",icon:o.Z},{id:"seo",label:"SEO",icon:l.Z},{id:"security",label:"Security",icon:i.Z},{id:"notifications",label:"Notifications",icon:c.Z},{id:"payments",label:"Payments",icon:d.Z}];return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your store configuration and preferences"})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)("div",{className:"lg:w-64",children:(0,t.jsx)("nav",{className:"space-y-1",children:h.map(r=>{let a=r.icon;return(0,t.jsxs)("button",{onClick:()=>s(r.id),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat(e===r.id?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,t.jsx)(a,{className:"w-5 h-5 mr-3 ".concat(e===r.id?"text-green-600":"text-gray-400")}),r.label]},r.id)})})}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(()=>{switch(e){case"general":return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Store Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Name"}),(0,t.jsx)("input",{type:"text",value:g.storeName,onChange:e=>x({...g,storeName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Email"}),(0,t.jsx)("input",{type:"email",value:g.storeEmail,onChange:e=>x({...g,storeEmail:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),(0,t.jsx)("input",{type:"tel",value:g.storePhone,onChange:e=>x({...g,storePhone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Currency"}),(0,t.jsxs)("select",{value:g.currency,onChange:e=>x({...g,currency:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,t.jsx)("option",{value:"USD",children:"USD - US Dollar"}),(0,t.jsx)("option",{value:"EUR",children:"EUR - Euro"}),(0,t.jsx)("option",{value:"GBP",children:"GBP - British Pound"}),(0,t.jsx)("option",{value:"CAD",children:"CAD - Canadian Dollar"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Description"}),(0,t.jsx)("textarea",{value:g.storeDescription,onChange:e=>x({...g,storeDescription:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Store Address"}),(0,t.jsx)("textarea",{value:g.storeAddress,onChange:e=>x({...g,storeAddress:e.target.value}),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})});case"email":return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"SMTP Configuration"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SMTP Host"}),(0,t.jsx)("input",{type:"text",value:g.smtpHost,onChange:e=>x({...g,smtpHost:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SMTP Port"}),(0,t.jsx)("input",{type:"text",value:g.smtpPort,onChange:e=>x({...g,smtpPort:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),(0,t.jsx)("input",{type:"text",value:g.smtpUsername,onChange:e=>x({...g,smtpUsername:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,t.jsx)("input",{type:"password",value:g.smtpPassword,onChange:e=>x({...g,smtpPassword:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})]})});case"seo":return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"SEO Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SEO Title"}),(0,t.jsx)("input",{type:"text",value:g.seoTitle,onChange:e=>x({...g,seoTitle:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"SEO Description"}),(0,t.jsx)("textarea",{value:g.seoDescription,onChange:e=>x({...g,seoDescription:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Keywords"}),(0,t.jsx)("input",{type:"text",value:g.seoKeywords,onChange:e=>x({...g,seoKeywords:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Separate keywords with commas"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Google Analytics ID"}),(0,t.jsx)("input",{type:"text",value:g.googleAnalyticsId,onChange:e=>x({...g,googleAnalyticsId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"UA-XXXXXXXXX-X"})]})]})]})});case"security":return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Security Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:g.twoFactorAuth,onChange:e=>x({...g,twoFactorAuth:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,t.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Enable Two-Factor Authentication for Admin"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Session Timeout (minutes)"}),(0,t.jsx)("input",{type:"number",value:g.sessionTimeout,onChange:e=>x({...g,sessionTimeout:parseInt(e.target.value)}),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"})]})]})]})});default:return(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsxs)("p",{className:"text-gray-500",children:["Settings for ",e," coming soon..."]})})}})(),(0,t.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,t.jsxs)("button",{onClick:()=>{m("saving"),setTimeout(()=>{m("saved"),setTimeout(()=>m("idle"),2e3)},1e3)},disabled:"saving"===r,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center disabled:opacity-50",children:[(0,t.jsx)(u.Z,{className:"w-5 h-5 mr-2"}),"saving"===r?"Saving...":"saved"===r?"Saved!":"Save Changes"]})})]})})]})]})}},9763:function(e,s,r){"use strict";r.d(s,{Z:function(){return o}});var t=r(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,s)=>{let r=(0,t.forwardRef)((r,o)=>{let{color:l="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:u="",children:m,...g}=r;return(0,t.createElement)("svg",{ref:o,...a,width:i,height:i,stroke:l,strokeWidth:d?24*Number(c)/Number(i):c,className:["lucide","lucide-".concat(n(e)),u].join(" "),...g},[...s.map(e=>{let[s,r]=e;return(0,t.createElement)(s,r)}),...Array.isArray(m)?m:[m]])});return r.displayName="".concat(e),r}},4766:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},8226:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},9202:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},9345:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3229:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},8906:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(9763).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=1114)}),_N_E=e.O()}]);