(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5311],{2558:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,7609))},7609:function(e,s,t){"use strict";t.d(s,{default:function(){return Z}});var a=t(7437),l=t(2265),r=t(7648),n=t(3145),i=t(2449),c=t(6858),d=t(8930),o=t(9763);let x=(0,o.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var m=t(9397),u=t(3827),h=t(605);let p=(0,o.Z)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var y=t(5252);let g=(0,o.Z)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var j=t(2720),f=t(2023),b=t(5302),N=t(2489),v=t(2252),w=t(1723),k=t(8867),C=e=>{let{cartItems:s,subtotal:t,userId:r,onCouponApply:n,onCouponRemove:i,appliedCoupons:c}=e,[d,o]=(0,l.useState)([]),[x,m]=(0,l.useState)([]),[u,h]=(0,l.useState)(""),[C,Z]=(0,l.useState)(!1),[E,S]=(0,l.useState)(""),[M,A]=(0,l.useState)(!1),[P,T]=(0,l.useState)(!0);(0,l.useEffect)(()=>{I(),F()},[s,r]);let I=async()=>{try{T(!0);let e=new URLSearchParams({active:"true",limit:"20"});r&&e.append("userId",r);let s=await fetch("/api/coupons?".concat(e));if(s.ok){let e=await s.json(),t=U(e.coupons,!0);o(t)}}catch(e){console.error("Error fetching coupons:",e)}finally{T(!1)}},F=async()=>{try{let e=new URLSearchParams({active:"true",showInModule:"true",limit:"10"});r&&e.append("userId",r);let s=await fetch("/api/coupons?".concat(e));if(s.ok){let e=await s.json(),t=U(e.coupons,!1);m(t)}}catch(e){console.error("Error fetching featured coupons:",e)}},U=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e.filter(e=>{if(c.some(s=>s.coupon.id===e.id)||a&&e.showInModule||e.minimumAmount&&t<e.minimumAmount)return!1;let l=s.map(e=>e.product.id),r=s.flatMap(e=>{var s;return(null===(s=e.product.categories)||void 0===s?void 0:s.map(e=>e.id))||[]});switch(e.type){case"PRODUCT_SPECIFIC":return e.applicableProducts.some(e=>l.includes(e));case"CATEGORY_SPECIFIC":return e.applicableCategories.some(e=>r.includes(e));case"MINIMUM_PURCHASE":return t>=(e.minimumAmount||0);default:return!0}})},D=async e=>{if(e.trim()){Z(!0),S("");try{let a=await fetch("/api/coupons/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({couponCode:e,cartItems:s,userId:r,subtotal:t})}),l=await a.json();if(l.isValid&&l.coupon){let e={coupon:l.coupon,discountAmount:l.discountAmount,isValid:!0};n(e),h(""),S("Coupon applied successfully!"),I(),F()}else S(l.errorMessage||"Invalid coupon code")}catch(e){S("Error validating coupon")}finally{Z(!1)}}},O=async e=>{await D(e.code)},V=e=>{navigator.clipboard.writeText(e)},R=e=>{switch(e){case"PERCENTAGE":return(0,a.jsx)(p,{className:"w-4 h-4"});case"FIXED_AMOUNT":return(0,a.jsx)(y.Z,{className:"w-4 h-4"});case"FREE_SHIPPING":return(0,a.jsx)(g,{className:"w-4 h-4"});default:return(0,a.jsx)(j.Z,{className:"w-4 h-4"})}},_=e=>{switch(e.discountType){case"PERCENTAGE":return"".concat(e.discountValue,"% OFF");case"FIXED_AMOUNT":return"₹".concat(e.discountValue," OFF");case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},q=e=>{if(!e.validUntil)return!1;let s=new Date(e.validUntil),t=new Date,a=Math.ceil((s.getTime()-t.getTime())/864e5);return a<=3&&a>0};if(P)return(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded"})]})]})});let L=(()=>{let e=d.filter(e=>("PERCENTAGE"===e.discountType?e.discountValue>=10:e.discountValue>=50)||q(e));return e.length>=3?e.slice(0,5):d.slice(0,Math.min(5,d.length))})(),z=M?d:L;return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)(f.Z,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Available Coupons"})]}),c.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-600 mb-3",children:"Applied Coupons"}),(0,a.jsx)("div",{className:"space-y-2",children:c.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-green-800",children:e.coupon.code}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["-₹",e.discountAmount.toFixed(2)]})]})]}),(0,a.jsx)("button",{onClick:()=>i(e.coupon.id),className:"p-1 text-green-600 hover:bg-green-100 rounded-full transition-colors",children:(0,a.jsx)(N.Z,{className:"w-4 h-4"})})]},e.coupon.id))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:u,onChange:e=>h(e.target.value.toUpperCase()),placeholder:"Enter coupon code",className:"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&D(u)}),(0,a.jsx)("button",{onClick:()=>D(u),disabled:C||!u.trim(),className:"px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:C?"Applying...":"Apply"})]}),E&&(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm ".concat(E.includes("successfully")?"text-green-600":"text-red-600"),children:[E.includes("successfully")?(0,a.jsx)(b.Z,{className:"w-4 h-4"}):(0,a.jsx)(v.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:E})]})]}),x.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-600 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(f.Z,{className:"w-4 h-4 text-yellow-500"}),(0,a.jsx)("span",{children:"Featured Offers"})]}),(0,a.jsx)("div",{className:"space-y-3",children:x.map(e=>(0,a.jsxs)("div",{className:"border-2 border-gradient-to-r from-yellow-200 to-orange-200 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 bg-yellow-400 text-yellow-900 px-2 py-1 text-xs font-bold rounded-bl-lg",children:"FEATURED"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 pr-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"p-1 rounded bg-yellow-100 text-yellow-600",children:R(e.discountType)}),(0,a.jsx)("span",{className:"font-bold text-lg text-gray-800",children:_(e)}),q(e)&&(0,a.jsx)("span",{className:"px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium",children:"Expires Soon"})]}),(0,a.jsx)("h5",{className:"font-semibold text-gray-800 mb-1",children:e.name}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(j.Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"font-medium",children:e.code})]}),e.validUntil&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(w.Z,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:["Until ",new Date(e.validUntil).toLocaleDateString()]})]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),(0,a.jsx)("button",{onClick:()=>O(e),className:"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm rounded-lg font-bold hover:from-yellow-600 hover:to-orange-600 transition-all transform hover:scale-105 shadow-lg",children:"Use It!"})]})]},e.id))})]}),z.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-600",children:M?"All Available Coupons":"Recommended for You"}),d.length>z.length&&(0,a.jsx)("button",{onClick:()=>A(!M),className:"text-sm text-green-600 hover:text-green-700 font-medium",children:M?"Show Less":"View All (".concat(d.length,")")})]}),(0,a.jsx)("div",{className:"space-y-3",children:z.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 transition-all hover:shadow-md ".concat(q(e)?"border-orange-200 bg-orange-50":"border-gray-200 hover:border-green-300"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"p-1 rounded ".concat(q(e)?"bg-orange-100 text-orange-600":"bg-green-100 text-green-600"),children:R(e.discountType)}),(0,a.jsx)("span",{className:"font-semibold text-gray-800",children:_(e)}),q(e)&&(0,a.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full font-medium",children:"Expires Soon"})]}),(0,a.jsx)("h5",{className:"font-medium text-gray-800 mb-1",children:e.name}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(j.Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.code}),(0,a.jsx)("button",{onClick:()=>V(e.code),className:"p-1 hover:bg-gray-100 rounded",children:(0,a.jsx)(k.Z,{className:"w-3 h-3"})})]}),e.validUntil&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(w.Z,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:["Until ",new Date(e.validUntil).toLocaleDateString()]})]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),(0,a.jsx)("button",{onClick:()=>O(e),className:"ml-4 px-4 py-2 bg-green-600 text-white text-sm rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Apply"})]})},e.id))})]}),0===d.length&&!P&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(g,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No coupons available for your current cart"})]})]})},Z=()=>{var e,s;let{state:t,dispatch:l}=(0,u.j)(),{data:o}=(0,h.useSession)(),p=(e,s)=>{s<=0?l({type:"REMOVE_ITEM",payload:e}):l({type:"UPDATE_QUANTITY",payload:{id:e,quantity:s}})},y=e=>{l({type:"REMOVE_ITEM",payload:e})},g=e=>{l({type:"APPLY_COUPON",payload:e})},j=e=>{l({type:"REMOVE_COUPON",payload:e})};return 0===t.items.length?(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-8 text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(i.Z,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Add some products to get started"}),(0,a.jsxs)(r.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:["Start Shopping",(0,a.jsx)(c.Z,{className:"ml-2 w-4 h-4"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-16 text-center",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,a.jsx)(i.Z,{className:"w-16 h-16 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Discover our amazing products and start shopping"}),(0,a.jsxs)(r.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-colors text-lg",children:["Start Shopping",(0,a.jsx)(c.Z,{className:"ml-3 w-5 h-5"})]})]})})]}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Shopping Cart"}),(0,a.jsx)("div",{className:"space-y-4 mb-6",children:t.items.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:(0,a.jsx)(n.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"80px"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-1 line-clamp-1",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.product.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.product.price]}),(0,a.jsx)("button",{onClick:()=>y(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>p(e.variantKey||e.product.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(x,{className:"w-4 h-4"})}),(0,a.jsx)("span",{className:"font-medium text-gray-800 w-8 text-center",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>p(e.variantKey||e.product.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(m.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]})]},e.variantKey||e.product.id))}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(C,{cartItems:t.items,subtotal:t.subtotal,userId:null==o?void 0:null===(e=o.user)||void 0===e?void 0:e.id,onCouponApply:g,onCouponRemove:j,appliedCoupons:t.coupons.appliedCoupons})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",t.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",t.subtotal.toFixed(2)]})]}),t.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,a.jsx)("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",t.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:"Free"})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",t.finalTotal.toFixed(2)]})]})})]})]}),(0,a.jsxs)(r.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[(0,a.jsx)("span",{children:"Proceed to Checkout"}),(0,a.jsx)(c.Z,{className:"w-5 h-5"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shopping Cart"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("div",{className:"space-y-6",children:t.items.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)("div",{className:"w-32 h-32 relative rounded-xl overflow-hidden flex-shrink-0",children:(0,a.jsx)(n.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"128px"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),(0,a.jsx)("p",{className:"text-gray-600",children:e.product.shortDescription})]}),(0,a.jsx)("button",{onClick:()=>y(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:(0,a.jsx)(d.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>p(e.variantKey||e.product.id,e.quantity-1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(x,{className:"w-5 h-5"})}),(0,a.jsx)("span",{className:"font-medium text-gray-800 w-12 text-center text-lg",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>p(e.variantKey||e.product.id,e.quantity+1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(m.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₹",e.product.price," each"]})]})]})]})]})},e.variantKey||e.product.id))}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(C,{cartItems:t.items,subtotal:t.subtotal,userId:null==o?void 0:null===(s=o.user)||void 0===s?void 0:s.id,onCouponApply:g,onCouponRemove:j,appliedCoupons:t.coupons.appliedCoupons})})]}),(0,a.jsx)("div",{className:"col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",t.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",t.subtotal.toFixed(2)]})]}),t.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,a.jsx)("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",t.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-xl",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",t.finalTotal.toFixed(2)]})]})})]}),(0,a.jsxs)(r.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 text-lg",children:[(0,a.jsx)("span",{children:"Proceed to Checkout"}),(0,a.jsx)(c.Z,{className:"w-5 h-5"})]}),(0,a.jsx)(r.default,{href:"/shop",className:"w-full mt-3 border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center",children:"Continue Shopping"})]})})]})]})})]})}},2252:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6858:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},5302:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8867:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5252:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},9397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},2023:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},2720:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},8930:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2489:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}},function(e){e.O(0,[605,1451,5704,1760,2971,2117,1744],function(){return e(e.s=2558)}),_N_E=e.O()}]);