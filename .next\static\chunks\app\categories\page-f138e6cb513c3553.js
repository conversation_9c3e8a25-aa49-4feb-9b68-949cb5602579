(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4457],{7276:function(e,t,s){Promise.resolve().then(s.bind(s,9348))},9348:function(e,t,s){"use strict";s.r(t);var r=s(7437),a=s(2265),l=s(7648),n=s(2023),i=s(3247),c=s(1473),o=s(7586),d=s(9547),x=s(6858),m=s(8997),h=s(6595);t.default=()=>{let[e,t]=(0,a.useState)([]),[s,u]=(0,a.useState)(!0),[g,p]=(0,a.useState)(""),[f,y]=(0,a.useState)("grid");(0,a.useEffect)(()=>{let e=()=>{window.innerWidth<768?y("list"):y("grid")};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/categories"),s=await e.json();s.success&&t(s.data)}catch(e){console.error("Error fetching categories:",e)}finally{u(!1)}})()},[]);let j=e.filter(e=>{var t;return e.name.toLowerCase().includes(g.toLowerCase())||(null===(t=e.description)||void 0===t?void 0:t.toLowerCase().includes(g.toLowerCase()))});return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-50",children:[(0,r.jsxs)("div",{className:"relative bg-green-600 text-white py-16 px-4 lg:py-20 lg:px-8",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black opacity-10"}),(0,r.jsxs)("div",{className:"relative max-w-4xl mx-auto text-center",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-500 bg-opacity-20 backdrop-blur-sm mb-6",children:[(0,r.jsx)(n.Z,{className:"w-4 h-4 mr-2"}),"Explore Our Collection"]}),(0,r.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight",children:"Shop by Category"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-green-100 mb-8 max-w-2xl mx-auto leading-relaxed",children:"Discover our carefully curated collection of natural skincare products, organized by your specific needs"}),(0,r.jsx)("div",{className:"max-w-md mx-auto relative",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:"Search categories...",value:g,onChange:e=>p(e.target.value),className:"w-full pl-10 pr-4 py-3 rounded-full border-0 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-300 shadow-lg"})]})})]})]}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12 lg:px-8 lg:py-16",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-8 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:[j.length," Categories"]}),g&&(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:['for "',g,'"']})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("button",{onClick:()=>y("grid"),className:"p-2 rounded-md transition-colors ".concat("grid"===f?"bg-green-600 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:(0,r.jsx)(c.Z,{className:"w-5 h-5"})}),(0,r.jsx)("button",{onClick:()=>y("list"),className:"p-2 rounded-md transition-colors ".concat("list"===f?"bg-green-600 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:(0,r.jsx)(o.Z,{className:"w-5 h-5"})})]})]}),s?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm animate-pulse",children:[(0,r.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-xl mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]},t))}):0===j.length?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(i.Z,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:g?"No categories found":"No categories available"}),(0,r.jsx)("p",{className:"text-gray-500 mb-6",children:g?'Try adjusting your search term "'.concat(g,'"'):"Categories will appear here once they are added."}),g&&(0,r.jsx)("button",{onClick:()=>p(""),className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Search"})]}):(0,r.jsx)("div",{className:"grid"===f?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:j.map(e=>(0,r.jsx)(l.default,{href:"/shop?category=".concat(e.slug),className:"group bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden ".concat("list"===f?"flex items-center p-6":"block"),children:"grid"===f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-green-100 to-emerald-100 overflow-hidden",children:[e.image?(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-16 h-16 text-green-400"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors",children:e.name}),(0,r.jsx)(x.Z,{className:"w-5 h-5 text-gray-400 group-hover:text-green-600 group-hover:translate-x-1 transition-all duration-200"})]}),e.description&&(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),void 0!==e.productCount&&(0,r.jsx)("div",{className:"flex items-center text-sm text-gray-500",children:(0,r.jsxs)("span",{children:[e.productCount," products"]})}),(0,r.jsxs)("div",{className:"mt-4 inline-flex items-center text-green-600 font-medium text-sm group-hover:text-green-700",children:["Explore Collection",(0,r.jsx)(x.Z,{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200"})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl overflow-hidden mr-6",children:e.image?(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-8 h-8 text-green-400"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors",children:e.name}),(0,r.jsx)(x.Z,{className:"w-5 h-5 text-gray-400 group-hover:text-green-600 group-hover:translate-x-1 transition-all duration-200"})]}),e.description&&(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-1 line-clamp-1",children:e.description}),void 0!==e.productCount&&(0,r.jsx)("div",{className:"flex items-center text-sm text-gray-500 mt-2",children:(0,r.jsxs)("span",{children:[e.productCount," products"]})})]})]})},e.id))}),!s&&j.length>0&&(0,r.jsx)("div",{className:"mt-16 text-center",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(m.Z,{className:"w-8 h-8 text-red-500 mr-2"}),(0,r.jsx)(h.Z,{className:"w-8 h-8 text-yellow-400"})]}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Can't find what you're looking for?"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Browse our complete collection or get in touch with our skincare experts for personalized recommendations."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(l.default,{href:"/shop",className:"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors",children:["Browse All Products",(0,r.jsx)(x.Z,{className:"w-5 h-5 ml-2"})]}),(0,r.jsx)(l.default,{href:"/contact",className:"inline-flex items-center px-6 py-3 border-2 border-green-600 text-green-600 font-semibold rounded-lg hover:bg-green-50 transition-colors",children:"Contact Us"})]})]})})})]})]})}},6858:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},1473:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},8997:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},9547:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},7586:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},3247:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2023:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},6595:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])}},function(e){e.O(0,[1451,2971,2117,1744],function(){return e(e.s=7276)}),_N_E=e.O()}]);