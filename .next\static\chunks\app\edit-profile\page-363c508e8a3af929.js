(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[819],{1697:function(e,s,t){Promise.resolve().then(t.bind(t,1424))},1424:function(e,s,t){"use strict";t.r(s);var a=t(7437),r=t(2265),n=t(9376),l=t(605),i=t(2660),d=t(2369),c=t(9345),o=t(3041),u=t(3229),m=t(3774),h=t(9397),x=t(5868),p=t(8930);s.default=()=>{var e;let s=(0,n.useRouter)(),{data:t,update:f}=(0,l.useSession)(),[y,g]=(0,r.useState)(!0),[v,j]=(0,r.useState)(!1),[b,N]=(0,r.useState)(null),[w,k]=(0,r.useState)([]),[Z,S]=(0,r.useState)({});(0,r.useEffect)(()=>{(async()=>{var e;if(null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/users/".concat(t.user.id));if(e.ok){let s=await e.json();N({id:s.data.id,name:s.data.name||"",email:s.data.email,phone:s.data.phone||""}),k(s.data.addresses||[])}}catch(e){console.error("Error fetching user data:",e)}finally{g(!1)}})()},[null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.id]);let P=(e,s)=>{b&&(N({...b,[e]:s}),Z[e]&&S(s=>({...s,[e]:""})))},C=()=>{var e,s;let t={};return(null==b?void 0:null===(e=b.name)||void 0===e?void 0:e.trim())||(t.name="Name is required"),(null==b?void 0:null===(s=b.email)||void 0===s?void 0:s.trim())?/\S+@\S+\.\S+/.test(b.email)||(t.email="Please enter a valid email"):t.email="Email is required",(null==b?void 0:b.phone)&&!/^\+?[\d\s\-\(\)]+$/.test(b.phone)&&(t.phone="Please enter a valid phone number"),S(t),0===Object.keys(t).length},A=async()=>{if(C()&&b){j(!0);try{(await fetch("/api/users/".concat(b.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:b.name,email:b.email,phone:b.phone||null})})).ok?(await f({...t,user:{...null==t?void 0:t.user,name:b.name,email:b.email}}),s.push("/profile")):S({general:"Failed to update profile"})}catch(e){S({general:"An error occurred while saving"})}finally{j(!1)}}},E=async e=>{if(confirm("Are you sure you want to delete this address?"))try{k(s=>s.filter(s=>s.id!==e))}catch(e){console.error("Error deleting address:",e)}},M=async e=>{try{k(s=>s.map(s=>({...s,isDefault:s.id===e})))}catch(e){console.error("Error setting default address:",e)}};return y?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-8"}),(0,a.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded"},e))})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,a.jsx)("div",{className:"flex items-center mb-8",children:(0,a.jsxs)("button",{onClick:()=>s.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(i.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back"})]})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Edit Profile"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Personal Information"})]}),Z.general&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:Z.general}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:(null==b?void 0:b.name)||"",onChange:e=>P("name",e.target.value),className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(Z.name?"border-red-500":"border-gray-300"),placeholder:"Enter your full name"}),Z.name&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:Z.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"email",value:(null==b?void 0:b.email)||"",onChange:e=>P("email",e.target.value),className:"w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(Z.email?"border-red-500":"border-gray-300"),placeholder:"Enter your email address"})]}),Z.email&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:Z.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"tel",value:(null==b?void 0:b.phone)||"",onChange:e=>P("phone",e.target.value),className:"w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(Z.phone?"border-red-500":"border-gray-300"),placeholder:"Enter your phone number (optional)"})]}),Z.phone&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:Z.phone})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:A,disabled:v,className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:[(0,a.jsx)(u.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:v?"Saving...":"Save Changes"})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Shipping Addresses"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:[(0,a.jsx)(h.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Address"})]})]}),0===w.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No addresses added yet"}),(0,a.jsx)("button",{className:"mt-4 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Add Your First Address"})]}):(0,a.jsx)("div",{className:"space-y-4",children:w.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-xl p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&(0,a.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&(0,a.jsx)("p",{className:"font-medium",children:e.company}),(0,a.jsx)("p",{children:e.address1}),e.address2&&(0,a.jsx)("p",{children:e.address2}),(0,a.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),(0,a.jsx)("p",{children:e.country}),e.phone&&(0,a.jsx)("p",{className:"font-medium",children:e.phone})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&(0,a.jsx)("button",{onClick:()=>M(e.id),className:"px-3 py-1 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),(0,a.jsx)("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>E(e.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:(0,a.jsx)(p.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]})]})})}},9763:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var a=t(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,s)=>{let t=(0,a.forwardRef)((t,l)=>{let{color:i="currentColor",size:d=24,strokeWidth:c=2,absoluteStrokeWidth:o,className:u="",children:m,...h}=t;return(0,a.createElement)("svg",{ref:l,...r,width:d,height:d,stroke:i,strokeWidth:o?24*Number(c)/Number(d):c,className:["lucide","lucide-".concat(n(e)),u].join(" "),...h},[...s.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(m)?m:[m]])});return t.displayName="".concat(e),t}},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9345:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3774:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},3041:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3229:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},5868:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},8930:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2369:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9376:function(e,s,t){"use strict";var a=t(5475);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})}},function(e){e.O(0,[605,2971,2117,1744],function(){return e(e.s=1697)}),_N_E=e.O()}]);