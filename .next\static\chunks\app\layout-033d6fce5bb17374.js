(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{4276:function(t,o,e){Promise.resolve().then(e.bind(e,3827)),Promise.resolve().then(e.bind(e,9124)),Promise.resolve().then(e.bind(e,578)),Promise.resolve().then(e.t.bind(e,911,23)),Promise.resolve().then(e.t.bind(e,7960,23))},3827:function(t,o,e){"use strict";e.d(o,{CartProvider:function(){return f},j:function(){return m}});var a=e(7437),i=e(2265);let n=(0,i.createContext)(null),r="herbalicious_cart",l=t=>{try{localStorage.setItem(r,JSON.stringify(t))}catch(t){console.error("Error saving cart to localStorage:",t)}},s=()=>{try{{let t=localStorage.getItem(r);if(t)return JSON.parse(t)}}catch(t){console.error("Error loading cart from localStorage:",t)}return null},u=(t,o)=>{if(!o||0===o.length)return t;let e=[...o].sort((t,o)=>t.name.localeCompare(o.name)).map(t=>"".concat(t.name,":").concat(t.value)).join("|");return"".concat(t,"__").concat(e)},c=t=>{var o;return t.variantKey||(null===(o=t.product)||void 0===o?void 0:o.id)||t.id},d=()=>s()||{items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}},p=(t,o)=>{let e=t.reduce((t,o)=>t+o.product.price*o.quantity,0),a=t.reduce((t,o)=>t+o.quantity,0),i=o.reduce((t,o)=>t+o.discountAmount,0);return{subtotal:e,itemCount:a,total:e,finalTotal:e-i,totalDiscount:i}},v=(t,o)=>{let e;switch(o.type){case"ADD_ITEM":{let a;let i=u(o.payload.id,o.selectedVariants);if(t.items.find(t=>c(t)===i))a=t.items.map(t=>c(t)===i?{...t,quantity:t.quantity+1,variantKey:i}:t);else{let e={product:o.payload,quantity:1,selectedVariants:o.selectedVariants||[],variantKey:i};a=[...t.items,e]}let n=p(a,t.coupons.appliedCoupons);e={...t,items:a,...n,coupons:{...t.coupons,totalDiscount:n.totalDiscount}};break}case"REMOVE_ITEM":{let a=t.items.filter(t=>c(t)!==o.payload),i=p(a,t.coupons.appliedCoupons);e={...t,items:a,...i,coupons:{...t.coupons,totalDiscount:i.totalDiscount}};break}case"UPDATE_QUANTITY":{let a=t.items.map(t=>c(t)===o.payload.id?{...t,quantity:o.payload.quantity}:t).filter(t=>t.quantity>0),i=p(a,t.coupons.appliedCoupons);e={...t,items:a,...i,coupons:{...t.coupons,totalDiscount:i.totalDiscount}};break}case"APPLY_COUPON":{if(t.coupons.appliedCoupons.some(t=>t.coupon.id===o.payload.coupon.id)||t.coupons.appliedCoupons.some(t=>!t.coupon.isStackable)&&!o.payload.coupon.isStackable)return t;let a=[...t.coupons.appliedCoupons,o.payload],i=p(t.items,a);e={...t,...i,coupons:{...t.coupons,appliedCoupons:a,totalDiscount:i.totalDiscount}};break}case"REMOVE_COUPON":{let a=t.coupons.appliedCoupons.filter(t=>t.coupon.id!==o.payload),i=p(t.items,a);e={...t,...i,coupons:{...t.coupons,appliedCoupons:a,totalDiscount:i.totalDiscount}};break}case"CLEAR_COUPONS":{let o=p(t.items,[]);e={...t,...o,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break}case"CLEAR_CART":e={items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]}};break;default:return t}return l(e),e},f=t=>{let{children:o}=t,[e,r]=(0,i.useReducer)(v,d());return(0,a.jsx)(n.Provider,{value:{state:e,dispatch:r},children:o})},m=()=>{let t=(0,i.useContext)(n);if(!t)throw Error("useCart must be used within a CartProvider");return t}},9124:function(t,o,e){"use strict";e.d(o,{NotificationProvider:function(){return s},z:function(){return l}});var a=e(7437),i=e(2265),n=e(605);let r=(0,i.createContext)(void 0),l=()=>{let t=(0,i.useContext)(r);if(void 0===t)throw Error("useNotifications must be used within a NotificationProvider");return t},s=t=>{var o,e,l,s,u,c;let{children:d}=t,{data:p,status:v}=(0,n.useSession)(),[f,m]=(0,i.useState)([]),[C,h]=(0,i.useState)(0),[y,b]=(0,i.useState)(!1),[_,k]=(0,i.useState)(null),E=(0,i.useCallback)(async function(){var t;let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{b(!0),k(null);let t=new URLSearchParams({page:(o.page||1).toString(),limit:(o.limit||10).toString(),...o.unreadOnly&&{unreadOnly:"true"}}),e=await fetch("/api/notifications?".concat(t)),a=await e.json();a.success?(m(a.data.notifications),h(a.data.unreadCount)):k(a.error||"Failed to fetch notifications")}catch(t){console.error("Error fetching notifications:",t),k("Failed to fetch notifications")}finally{b(!1)}},[null==p?void 0:null===(o=p.user)||void 0===o?void 0:o.id]),P=(0,i.useCallback)(async()=>{var t;if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{let t=await fetch("/api/notifications/unread-count"),o=await t.json();o.success&&h(o.unreadCount)}catch(t){console.error("Error fetching unread count:",t)}},[null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.id]),S=(0,i.useCallback)(async t=>{var o;if(null==p?void 0:null===(o=p.user)||void 0===o?void 0:o.id)try{let o=await fetch("/api/notifications/".concat(t,"/read"),{method:"POST"}),e=await o.json();e.success?(m(o=>o.map(o=>o.id===t?{...o,isRead:!0}:o)),h(t=>Math.max(0,t-1))):k(e.error||"Failed to mark notification as read")}catch(t){console.error("Error marking notification as read:",t),k("Failed to mark notification as read")}},[null==p?void 0:null===(l=p.user)||void 0===l?void 0:l.id]),g=(0,i.useCallback)(async()=>{var t;if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{let t=await fetch("/api/notifications/mark-all-read",{method:"POST"}),o=await t.json();o.success?(m(t=>t.map(t=>({...t,isRead:!0}))),h(0)):k(o.error||"Failed to mark all notifications as read")}catch(t){console.error("Error marking all notifications as read:",t),k("Failed to mark all notifications as read")}},[null==p?void 0:null===(s=p.user)||void 0===s?void 0:s.id]);return(0,i.useEffect)(()=>{var t;"authenticated"===v&&(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)&&(E({limit:5}),P())},[v,null==p?void 0:null===(u=p.user)||void 0===u?void 0:u.id,E,P]),(0,i.useEffect)(()=>{var t;if(!(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id))return;let o=setInterval(()=>{P()},3e4);return()=>clearInterval(o)},[null==p?void 0:null===(c=p.user)||void 0===c?void 0:c.id,P]),(0,a.jsx)(r.Provider,{value:{notifications:f,unreadCount:C,loading:y,error:_,fetchNotifications:E,markAsRead:S,markAllAsRead:g,refreshUnreadCount:P},children:d})}},578:function(t,o,e){"use strict";e.d(o,{default:function(){return n}});var a=e(7437),i=e(605);function n(t){let{children:o}=t;return(0,a.jsx)(i.SessionProvider,{children:o})}},7960:function(){},911:function(t){t.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(t){t.O(0,[8944,605,2971,2117,1744],function(){return t(t.s=4276)}),_N_E=t.O()}]);