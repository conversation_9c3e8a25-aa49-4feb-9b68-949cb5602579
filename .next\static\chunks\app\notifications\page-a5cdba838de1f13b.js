(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5193],{3974:function(e,t,s){Promise.resolve().then(s.bind(s,9937))},9937:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return b}});var a=s(7437),r=s(2265),n=s(9376),i=s(605),l=s(2449),c=s(8997),o=s(6595),d=s(3417),x=s(4766),u=s(5863),h=s(2660),g=s(740);let m=(0,s(9763).Z)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);var p=s(2252),y=s(1723),f=s(401),j=s(1760),N=s(9124),b=()=>{var e;let t=(0,n.useRouter)(),{data:s,status:b}=(0,i.useSession)(),{notifications:v,unreadCount:R,loading:E,error:w,fetchNotifications:D,markAsRead:k,markAllAsRead:S}=(0,N.z)(),[A,Z]=(0,r.useState)(1),[C,_]=(0,r.useState)(1),[O,I]=(0,r.useState)(0),[M,L]=(0,r.useState)({type:"",priority:"",isRead:"",search:""}),[P,T]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"unauthenticated"===b&&t.push("/login")},[b,t]);let H=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{let t=new URLSearchParams({page:e.toString(),limit:"20",...M.type&&{type:M.type},..."unread"===M.isRead&&{unreadOnly:"true"}}),s=await fetch("/api/notifications?".concat(t)),a=await s.json();a.success&&(_(a.data.pagination.totalPages),I(a.data.pagination.totalCount),Z(e))}catch(e){console.error("Error fetching notifications:",e)}};(0,r.useEffect)(()=>{var e;(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id)&&H(1)},[null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id,M]);let G=e=>{switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"ORDER_SHIPPED":case"ORDER_DELIVERED":return(0,a.jsx)(l.Z,{className:"w-5 h-5"});case"WISHLIST_ADDED":case"WISHLIST_REMOVED":case"PRICE_DROP_ALERT":return(0,a.jsx)(c.Z,{className:"w-5 h-5"});case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return(0,a.jsx)(o.Z,{className:"w-5 h-5"});case"ADMIN_MESSAGE":case"BROADCAST":return(0,a.jsx)(d.Z,{className:"w-5 h-5"});default:return(0,a.jsx)(x.Z,{className:"w-5 h-5"})}},U=(e,t)=>{if("URGENT"===t)return"text-red-600 bg-red-100";if("HIGH"===t)return"text-orange-600 bg-orange-100";switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":return"text-blue-600 bg-blue-100";case"ORDER_SHIPPED":case"ORDER_DELIVERED":return"text-green-600 bg-green-100";case"PRICE_DROP_ALERT":return"text-purple-600 bg-purple-100";case"ADMIN_MESSAGE":case"BROADCAST":return"text-indigo-600 bg-indigo-100";default:return"text-gray-600 bg-gray-100"}},W=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);return s<60?"Just now":s<3600?"".concat(Math.floor(s/60),"m ago"):s<86400?"".concat(Math.floor(s/3600),"h ago"):s<604800?"".concat(Math.floor(s/86400),"d ago"):t.toLocaleDateString()},V=async e=>{e.isRead||await k(e.id)},z=(e,t)=>{L(s=>({...s,[e]:t})),Z(1)};return"loading"===b?(0,a.jsx)(j.default,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(u.Z,{className:"w-8 h-8 animate-spin text-green-600"})})}):(null==s?void 0:s.user)?(0,a.jsx)(j.default,{children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>t.back(),className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(h.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[O," total • ",R," unread"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>T(!P),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(g.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Filter"})]}),R>0&&(0,a.jsxs)("button",{onClick:S,className:"flex items-center space-x-2 px-3 py-2 bg-green-600 text-white hover:bg-green-700 rounded-lg transition-colors",children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Mark all read"})]})]})]}),P&&(0,a.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,a.jsxs)("select",{value:M.type,onChange:e=>z("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"",children:"All types"}),(0,a.jsx)("option",{value:"ORDER_PLACED",children:"Order Updates"}),(0,a.jsx)("option",{value:"WISHLIST_ADDED",children:"Wishlist"}),(0,a.jsx)("option",{value:"PRICE_DROP_ALERT",children:"Price Alerts"}),(0,a.jsx)("option",{value:"REVIEW_REQUEST",children:"Reviews"}),(0,a.jsx)("option",{value:"ADMIN_MESSAGE",children:"Admin Messages"}),(0,a.jsx)("option",{value:"BROADCAST",children:"Announcements"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:M.isRead,onChange:e=>z("isRead",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"",children:"All notifications"}),(0,a.jsx)("option",{value:"unread",children:"Unread only"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Priority"}),(0,a.jsxs)("select",{value:M.priority,onChange:e=>z("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500",children:[(0,a.jsx)("option",{value:"",children:"All priorities"}),(0,a.jsx)("option",{value:"URGENT",children:"Urgent"}),(0,a.jsx)("option",{value:"HIGH",children:"High"}),(0,a.jsx)("option",{value:"NORMAL",children:"Normal"}),(0,a.jsx)("option",{value:"LOW",children:"Low"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{onClick:()=>{L({type:"",priority:"",isRead:"",search:""}),Z(1)},className:"w-full px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:"Clear filters"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg border border-gray-200",children:E?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(u.Z,{className:"w-8 h-8 animate-spin text-green-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]}):w?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(p.Z,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:w}),(0,a.jsx)("button",{onClick:()=>H(A),className:"text-red-600 hover:text-red-800 underline",children:"Try again"})]}):0===v.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(x.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 mb-2",children:"No notifications found"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"We'll notify you when something happens"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:v.map(e=>(0,a.jsx)("div",{onClick:()=>V(e),className:"p-6 hover:bg-gray-50 cursor-pointer transition-colors ".concat(e.isRead?"":"bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat(U(e.type,e.priority)),children:G(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium ".concat(e.isRead?"text-gray-700":"text-gray-900"," mb-1"),children:e.title}),!e.isRead&&(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full ml-4 mt-1 flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3 leading-relaxed",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500 flex items-center space-x-1",children:[(0,a.jsx)(y.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:W(e.createdAt)})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(U(e.type,e.priority)),children:e.type.replace("_"," ")}),"NORMAL"!==e.priority&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("URGENT"===e.priority?"bg-red-100 text-red-800":"HIGH"===e.priority?"bg-orange-100 text-orange-800":"bg-gray-100 text-gray-800"),children:e.priority})]}),e.isRead&&(0,a.jsx)(f.Z,{className:"w-4 h-4 text-green-500"})]})]})]})},e.id))})}),C>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(A-1)*20+1," to ",Math.min(20*A,O)," of ",O," notifications"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>H(A-1),disabled:1===A,className:"px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsxs)("span",{className:"px-3 py-2 text-gray-900",children:["Page ",A," of ",C]}),(0,a.jsx)("button",{onClick:()=>H(A+1),disabled:A===C,className:"px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})}):null}},2252:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2660:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},401:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1723:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},740:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},8997:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},5863:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3417:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},6595:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(9763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])}},function(e){e.O(0,[605,1451,5704,1760,2971,2117,1744],function(){return e(e.s=3974)}),_N_E=e.O()}]);