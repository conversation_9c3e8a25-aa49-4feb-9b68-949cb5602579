(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7947],{719:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,6273))},6273:function(e,s,t){"use strict";var a=t(7437),r=t(2265),l=t(9376),n=t(1723),i=t(340),c=t(5302),d=t(2660),x=t(2208),o=t(9076),m=t(4794);s.default=()=>{let e=(0,l.useRouter)(),[s,t]=(0,r.useState)("all"),h=[{id:"ORD-2024-001",date:"2024-01-15",status:"delivered",total:89.97,items:[{name:"Botanical Cleanser",quantity:1,price:28.99},{name:"Hydrating Serum",quantity:1,price:45.99},{name:"Nourishing Moisturizer",quantity:1,price:35.99}]},{id:"ORD-2024-002",date:"2024-01-20",status:"shipped",total:62.98,items:[{name:"Rejuvenating Face Mask",quantity:1,price:32.99},{name:"Gentle Exfoliator",quantity:1,price:29.99}]},{id:"ORD-2024-003",date:"2024-01-25",status:"processing",total:74.98,items:[{name:"Eye Care Cream",quantity:1,price:39.99},{name:"Botanical Cleanser",quantity:1,price:28.99}]}],g={processing:{icon:n.Z,color:"text-green-600",bg:"bg-green-100",label:"Processing"},shipped:{icon:i.Z,color:"text-green-600",bg:"bg-green-100",label:"Shipped"},delivered:{icon:c.Z,color:"text-green-600",bg:"bg-green-100",label:"Delivered"}},p=[{id:"all",label:"All Orders",count:h.length},{id:"processing",label:"Processing",count:h.filter(e=>"processing"===e.status).length},{id:"shipped",label:"Shipped",count:h.filter(e=>"shipped"===e.status).length},{id:"delivered",label:"Delivered",count:h.filter(e=>"delivered"===e.status).length}],u="all"===s?h:h.filter(e=>e.status===s),b=h.reduce((e,s)=>e+s.total,0);return(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(d.Z,{className:"w-5 h-5"})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"Order History"})]})}),(0,a.jsx)("div",{className:"px-4 py-6 bg-gradient-to-br from-green-50 to-green-100",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["₹",b.toFixed(2)]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Spent"})]})]})}),(0,a.jsx)("div",{className:"px-4 py-4 bg-white border-b",children:(0,a.jsx)("div",{className:"flex space-x-2 overflow-x-auto",children:p.map(e=>(0,a.jsxs)("button",{onClick:()=>t(e.id),className:"flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ".concat(s===e.id?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)("span",{children:e.label}),(0,a.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs ".concat(s===e.id?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:e.count})]},e.id))})}),(0,a.jsx)("div",{className:"px-4 py-6",children:u.length>0?(0,a.jsx)("div",{className:"space-y-4",children:u.map(e=>{let s=g[e.status].icon,t=g[e.status];return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.id}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:new Date(e.date).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 rounded-full ".concat(t.bg),children:[(0,a.jsx)(s,{className:"w-4 h-4 ".concat(t.color)}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t.color),children:t.label})]})]}),(0,a.jsx)("div",{className:"space-y-2 mb-4",children:e.items.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.name]}),(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},s))}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["Total: ₹",e.total.toFixed(2)]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Details"})]}),"delivered"===e.status&&(0,a.jsxs)("button",{className:"flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No orders match the selected filter"}),(0,a.jsx)("button",{onClick:()=>t("all"),className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"View All Orders"})]})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)("div",{className:"flex items-center mb-8",children:(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(d.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back"})]})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Order History"}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8 mb-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:h.length}),(0,a.jsx)("div",{className:"text-gray-600",children:"Total Orders"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:["₹",b.toFixed(2)]}),(0,a.jsx)("div",{className:"text-gray-600",children:"Total Spent"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:h.filter(e=>"delivered"===e.status).length}),(0,a.jsx)("div",{className:"text-gray-600",children:"Completed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:["₹",(b/h.length).toFixed(2)]}),(0,a.jsx)("div",{className:"text-gray-600",children:"Average Order"})]})]})}),(0,a.jsx)("div",{className:"flex space-x-4 mb-8",children:p.map(e=>(0,a.jsxs)("button",{onClick:()=>t(e.id),className:"flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ".concat(s===e.id?"bg-green-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"),children:[(0,a.jsx)("span",{children:e.label}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm ".concat(s===e.id?"bg-green-500 text-white":"bg-gray-100 text-gray-600"),children:e.count})]},e.id))}),u.length>0?(0,a.jsx)("div",{className:"space-y-6",children:u.map(e=>{let s=g[e.status].icon,t=g[e.status];return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:e.id}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Ordered on ",new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-2 rounded-xl ".concat(t.bg),children:[(0,a.jsx)(s,{className:"w-5 h-5 ".concat(t.color)}),(0,a.jsx)("span",{className:"font-medium ".concat(t.color),children:t.label})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Items Ordered"}),(0,a.jsx)("div",{className:"space-y-2",children:e.items.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.name]}),(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:"Free"})]}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex space-x-3 ml-auto",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Details"})]}),"delivered"===e.status&&(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Reorder"})]})]})})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)(m.Z,{className:"w-24 h-24 text-gray-300 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"No orders match the selected filter"}),(0,a.jsx)("button",{onClick:()=>t("all"),className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors",children:"View All Orders"})]})]})})]})}},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5302:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4794:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},9076:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},340:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},function(e){e.O(0,[605,1451,5704,1760,2971,2117,1744],function(){return e(e.s=719)}),_N_E=e.O()}]);