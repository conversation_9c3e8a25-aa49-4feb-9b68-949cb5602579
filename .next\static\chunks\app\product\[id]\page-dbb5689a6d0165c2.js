(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[188],{157:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,2766))},5975:function(e,s,t){"use strict";t.d(s,{Z:function(){return x}});var a=t(7437);t(2265);var r=t(7648),l=t(9547),i=t(2023),n=t(8997);let c=(0,t(9763).Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var d=t(1239),o=t(2208),x=e=>{let{product:s,showAsLinks:t=!1,className:x="",maxCategories:m}=e,u=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(s),h=m?u.slice(0,m):u;if(0===h.length)return null;let g=e=>({Skincare:(0,a.jsx)(l.Z,{className:"w-3 h-3"}),"Hair Care":(0,a.jsx)(i.Z,{className:"w-3 h-3"}),"Body Care":(0,a.jsx)(n.Z,{className:"w-3 h-3"}),cleanser:(0,a.jsx)(c,{className:"w-3 h-3"}),serum:(0,a.jsx)(d.Z,{className:"w-3 h-3"}),moisturizer:(0,a.jsx)(c,{className:"w-3 h-3"}),mask:(0,a.jsx)(o.Z,{className:"w-3 h-3"}),exfoliator:(0,a.jsx)(i.Z,{className:"w-3 h-3"}),"eye-care":(0,a.jsx)(o.Z,{className:"w-3 h-3"})})[e]||(0,a.jsx)(l.Z,{className:"w-3 h-3"});return(0,a.jsx)("div",{className:"flex flex-wrap gap-3 ".concat(x),children:h.map(e=>{let s=g(e.name);return t?(0,a.jsxs)(r.default,{href:"/shop?category=".concat(e.slug),className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600 hover:text-black transition-colors",children:[s,(0,a.jsx)("span",{children:e.name})]},e.id):(0,a.jsxs)("span",{className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600",children:[s,(0,a.jsx)("span",{children:e.name})]},e.id)})})}},2766:function(e,s,t){"use strict";t.d(s,{default:function(){return I}});var a=t(7437),r=t(2265),l=t(9376),i=t(605),n=t(2660),c=t(8997),d=t(9763);let o=(0,d.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var x=t(6595),m=t(401),u=t(6275),h=t(3827);let g=(0,d.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var p=t(875),j=e=>{let{productId:s}=e,[t,l]=(0,r.useState)([]),[i,n]=(0,r.useState)(!0),[c,d]=(0,r.useState)(null);(0,r.useEffect)(()=>{o()},[s]);let o=async()=>{try{n(!0);let e=await fetch("/api/products/".concat(s,"/faqs")),t=await e.json();t.success&&l(t.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{n(!1)}},x=e=>{d(c===e?null:e)};return i?(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-100 rounded"})]},e))}):0===t.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("div",{className:"text-gray-500",children:"No frequently asked questions available for this product."})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Frequently Asked Questions"}),(0,a.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,a.jsx)("button",{onClick:()=>x(e.id),className:"w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 pr-4",children:e.question}),(0,a.jsx)("div",{className:"flex-shrink-0",children:c===e.id?(0,a.jsx)(g,{className:"w-5 h-5 text-gray-500"}):(0,a.jsx)(p.Z,{className:"w-5 h-5 text-gray-500"})})]})}),c===e.id&&(0,a.jsx)("div",{className:"px-6 pb-4 bg-gray-50",children:(0,a.jsx)("div",{className:"pt-2 text-gray-700 whitespace-pre-wrap leading-relaxed",children:e.answer})})]},e.id))}),(0,a.jsx)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Have a question that's not answered here?"})," Feel free to contact our customer support team for more information about this product."]})})]})},v=t(1380),b=e=>{let{productId:s,basePrice:t,onVariationChange:l}=e,[i,n]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[o,x]=(0,r.useState)({});(0,r.useEffect)(()=>{m()},[s]),(0,r.useEffect)(()=>{if(i.length>0&&0===Object.keys(o).length){let e=i.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),s={};Object.entries(e).forEach(e=>{let[t,a]=e,r=[...a].sort((e,s)=>{var t,a;let r=null!==(t=e.price)&&void 0!==t?t:0;return(null!==(a=s.price)&&void 0!==a?a:0)-r});s[t]=r[0]}),x(s);let t=h(Object.values(s)),a=1===Object.keys(s).length?Object.values(s)[0]:null;l&&l(a,t)}},[i,t,l,o]);let m=async()=>{try{d(!0);let e=await fetch("/api/products/".concat(s,"/variations")),t=await e.json();t.success&&n(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{d(!1)}},u=i.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),h=e=>{let s=null!=t?t:0;if(0===e.length)return s;if(e.some(e=>"INCREMENT"===e.pricingMode&&void 0!==e.price&&null!==e.price))s=(null!=t?t:0)+e.reduce((e,s)=>{var t;return e+(null!==(t=s.price)&&void 0!==t?t:0)},0);else{let t=e.filter(e=>void 0!==e.price&&null!==e.price&&e.price>0);if(t.length>0){var a;s=null!==(a=t.reduce((e,s)=>{var t,a;return(null!==(t=s.price)&&void 0!==t?t:0)>(null!==(a=e.price)&&void 0!==a?a:0)?s:e}).price)&&void 0!==a?a:s}}return Math.max(0,s)},g=(e,s)=>{let t={...o,[e]:s};x(t);let a=h(Object.values(t)),r=1===Object.keys(t).length?Object.values(t)[0]:null;l&&l(r,a)},p=e=>!0;if(c)return(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,a.jsx)("div",{className:"flex space-x-2",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-10 w-16 bg-gray-200 rounded"},e))})]})});if(0===i.length)return null;let j=(()=>{let e=Object.values(o);return 0===e.length?null:{totalPrice:h(e),selectedValues:e}})();return(0,a.jsxs)("div",{className:"space-y-6",children:[Object.entries(u).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:s}),o[s]&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Selected: ",o[s].value]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>{var t;let r=(null===(t=o[s])||void 0===t?void 0:t.id)===e.id,l=p(e);return(0,a.jsx)("button",{onClick:()=>l&&g(s,e),disabled:!l,className:"\n                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors\n                    ".concat(r?"border-green-500 bg-green-50 text-green-700":l?"border-gray-300 bg-white text-gray-700 hover:border-gray-400":"border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed","\n                  "),children:(0,a.jsx)("span",{children:e.value})},e.id)})})]},s)}),j&&(0,a.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(0,v.T4)(j.totalPrice)})}),Object.keys(u).length>0&&0===Object.keys(o).length&&(0,a.jsx)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"Please select all required options before adding to cart."})})]})};let f=(0,d.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),y=(0,d.Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var N=t(407),w=t(2489),k=e=>{let{images:s,productName:t}=e,[l,i]=(0,r.useState)(0),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)(!0),[x,m]=(0,r.useState)(null),[u,h]=(0,r.useState)(null),g=[...s].sort((e,s)=>e.position-s.position);(0,r.useEffect)(()=>{g.length>0&&o(!1)},[g]);let p=e=>{i(e)},j=()=>{i(e=>0===e?g.length-1:e-1)},v=()=>{i(e=>e===g.length-1?0:e+1)},b=()=>{c(!1)},k=e=>{n&&("Escape"===e.key&&b(),"ArrowLeft"===e.key&&j(),"ArrowRight"===e.key&&v())};if((0,r.useEffect)(()=>(document.addEventListener("keydown",k),()=>document.removeEventListener("keydown",k)),[n]),!g||0===g.length)return(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"aspect-square bg-gray-200 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No images available"})]})})});let Z=g[l];return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"relative mb-4 group",children:(0,a.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-lg bg-gray-100",onTouchStart:e=>{h(null),m(e.targetTouches[0].clientX)},onTouchMove:e=>{h(e.targetTouches[0].clientX)},onTouchEnd:()=>{if(!x||!u)return;let e=x-u;e>50&&g.length>1&&v(),e<-50&&g.length>1&&j()},children:[d&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse rounded-lg"}),(0,a.jsx)("img",{src:Z.url,alt:Z.alt||"".concat(t," - Image ").concat(l+1),className:"w-full h-full object-cover cursor-zoom-in transition-transform duration-300 hover:scale-105 select-none",onClick:()=>{c(!0)},onLoad:()=>o(!1),draggable:!1}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)("div",{className:"bg-white bg-opacity-90 p-2 rounded-full",children:(0,a.jsx)(f,{className:"w-6 h-6 text-gray-700"})})})}),g.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:j,className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:(0,a.jsx)(y,{className:"w-5 h-5 text-gray-700"})}),(0,a.jsx)("button",{onClick:v,className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:(0,a.jsx)(N.Z,{className:"w-5 h-5 text-gray-700"})})]}),g.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:[l+1," / ",g.length]})]})}),g.length>1&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"hidden md:flex space-x-2 overflow-x-auto pb-2",children:g.map((e,s)=>(0,a.jsx)("button",{onClick:()=>p(s),className:"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ".concat(s===l?"border-green-500 ring-2 ring-green-200":"border-gray-200 hover:border-gray-300"),children:(0,a.jsx)("img",{src:e.url,alt:e.alt||"".concat(t," thumbnail ").concat(s+1),className:"w-full h-full object-cover"})},e.id||s))}),(0,a.jsx)("div",{className:"md:hidden flex space-x-2 overflow-x-auto pb-2",children:g.map((e,s)=>(0,a.jsx)("button",{onClick:()=>p(s),className:"flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ".concat(s===l?"border-green-500 ring-1 ring-green-200":"border-gray-200"),children:(0,a.jsx)("img",{src:e.url,alt:e.alt||"".concat(t," thumbnail ").concat(s+1),className:"w-full h-full object-cover"})},e.id||s))}),(0,a.jsxs)("div",{className:"md:hidden flex justify-center space-x-4",children:[(0,a.jsx)("button",{onClick:j,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:(0,a.jsx)(y,{className:"w-5 h-5 text-gray-700"})}),(0,a.jsx)("button",{onClick:v,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:(0,a.jsx)(N.Z,{className:"w-5 h-5 text-gray-700"})})]})]}),n&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[(0,a.jsx)("button",{onClick:b,className:"absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full z-10",children:(0,a.jsx)(w.Z,{className:"w-6 h-6"})}),g.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:j,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:(0,a.jsx)(y,{className:"w-6 h-6"})}),(0,a.jsx)("button",{onClick:v,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:(0,a.jsx)(N.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)("img",{src:Z.url,alt:Z.alt||"".concat(t," - Image ").concat(l+1),className:"max-w-full max-h-full object-contain"}),(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-center",children:[l+1," of ",g.length,Z.alt&&" - ".concat(Z.alt)]})})]})})]})},Z=t(5975),C=t(2369),S=t(5863),E=e=>{let{productId:s,onReviewSubmitted:t}=e,{data:l}=(0,i.useSession)(),[n,c]=(0,r.useState)(0),[d,o]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[h,g]=(0,r.useState)(!1),[p,j]=(0,r.useState)(null),[v,b]=(0,r.useState)(!1),f=async e=>{if(e.preventDefault(),!(null==l?void 0:l.user)){j("Please sign in to leave a review");return}if(0===n){j("Please select a rating");return}g(!0),j(null);try{let e=await fetch("/api/products/".concat(s,"/reviews"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({rating:n,title:d,content:m})}),a=await e.json();a.success?(b(!0),c(0),o(""),u(""),null==t||t()):j(a.error||"Failed to submit review")}catch(e){j("Failed to submit review")}finally{g(!1)}};return(null==l?void 0:l.user)?v?(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-green-600 mb-2",children:"✓"}),(0,a.jsx)("p",{className:"text-green-800 font-medium",children:"Review submitted for approval!"}),(0,a.jsx)("p",{className:"text-green-600 text-sm mt-1",children:"Your review will appear once approved by our team."})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Write a Review"}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rating *"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>c(e),className:"p-1 hover:scale-110 transition-transform",children:(0,a.jsx)(x.Z,{className:"w-6 h-6 ".concat(e<=n?"text-yellow-400 fill-current":"text-gray-300")})},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Title"}),(0,a.jsx)("input",{type:"text",value:d,onChange:e=>o(e.target.value),placeholder:"Summarize your experience",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:100})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Details"}),(0,a.jsx)("textarea",{value:m,onChange:e=>u(e.target.value),placeholder:"Tell us about your experience with this product",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:1e3})]}),p&&(0,a.jsx)("div",{className:"text-red-600 text-sm",children:p}),(0,a.jsx)("button",{type:"submit",disabled:h||0===n,className:"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(S.Z,{className:"w-4 h-4 inline mr-2 animate-spin"}),"Submitting..."]}):"Submit Review"})]})]}):(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Please sign in to leave a review"})})},M=e=>{let{productId:s}=e,[t,l]=(0,r.useState)([]),[i,n]=(0,r.useState)(!0),[c,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{u()},[s]);let u=async()=>{try{n(!0);let e=await fetch("/api/products/".concat(s,"/reviews")),t=await e.json();t.success?l(t.data):d(t.error||"Failed to fetch reviews")}catch(e){d("Failed to fetch reviews")}finally{n(!1)}},h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),g=t.length>0?(t.reduce((e,s)=>e+s.rating,0)/t.length).toFixed(1):0;return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900",children:["Customer Reviews (",t.length,")"]}),(0,a.jsx)("button",{onClick:()=>m(!o),className:"text-green-600 hover:text-green-700 font-medium",children:"Write a Review"})]}),t.length>0&&(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(e=>(0,a.jsx)(x.Z,{className:"w-5 h-5 ".concat(e<=Math.round(Number(g))?"text-yellow-400 fill-current":"text-gray-300")},e))}),(0,a.jsx)("span",{className:"text-lg font-semibold",children:g}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",t.length," reviews)"]})]})}),o&&(0,a.jsx)(E,{productId:s,onReviewSubmitted:()=>{m(!1),u()}}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(x.Z,{className:"w-12 h-12 mx-auto"})}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"No reviews yet"}),(0,a.jsx)("button",{onClick:()=>m(!0),className:"text-green-600 hover:text-green-700 font-medium",children:"Be the first to write a review"})]}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>{var s,t;return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:(null===(s=e.user)||void 0===s?void 0:s.avatar)?(0,a.jsx)("img",{src:e.user.avatar,alt:e.user.name||"User",className:"w-10 h-10 rounded-full"}):(0,a.jsx)(C.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(null===(t=e.user)||void 0===t?void 0:t.name)||"Anonymous"}),e.isVerified&&(0,a.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Verified Purchase"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(s=>(0,a.jsx)(x.Z,{className:"w-4 h-4 ".concat(s<=e.rating?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:h(e.createdAt)})]}),e.title&&(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:e.title}),e.content&&(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:e.content})]})]})})},e.id)})})]})},F=t(3905);let L=e=>{var s,t;let a=e.reviews||[],r=a.length>0?a.reduce((e,s)=>e+s.rating,0)/a.length:0,l=[],i=[];if(e.description){e.description.toLowerCase();let s=e.description.match(/ingredients?:\s*([^.]+)/i);s&&l.push(...s[1].split(",").map(e=>e.trim()).filter(e=>e));let t=e.description.match(/benefits?:\s*([^.]+)/i);t&&i.push(...t[1].split(",").map(e=>e.trim()).filter(e=>e))}return 0===l.length&&l.push("Natural extracts","Essential oils","Vitamins"),0===i.length&&i.push("Hydrates skin","Reduces fine lines","Improves skin texture"),{id:e.id,name:e.name,description:e.description||"",shortDescription:e.shortDescription||"",price:e.price||0,image:(null===(s=e.images[0])||void 0===s?void 0:s.url)||"/images/default-product.jpg",images:e.images.map(s=>({id:s.id,url:s.url,alt:s.alt||e.name,position:s.position||0})),category:(null===(t=e.category)||void 0===t?void 0:t.slug)||"skincare",featured:e.isFeatured,ingredients:l,benefits:i,rating:Math.round(10*r)/10,reviews:a.length,_raw:e}};var I=e=>{let{id:s}=e,t=(0,l.useRouter)(),{data:d}=(0,i.useSession)(),{dispatch:g}=(0,h.j)(),[p,v]=(0,r.useState)("description"),[f,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[C,S]=(0,r.useState)(1),[E,I]=(0,r.useState)(null),[A,D]=(0,r.useState)(!0),[P,T]=(0,r.useState)(null),[q,z]=(0,r.useState)(0),[O,R]=(0,r.useState)(!1),[_,B]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{D(!0);let e=await fetch("/api/products/".concat(s)),t=await e.json();if(t.success){let e=L(t.data);I(e),z(e.price)}else T("Product not found")}catch(e){console.error("Error fetching product:",e),T("Failed to load product")}finally{D(!1)}};s&&e()},[s]),(0,r.useEffect)(()=>{(async()=>{var e;if((null==d?void 0:null===(e=d.user)||void 0===e?void 0:e.id)&&(null==E?void 0:E.id))try{let e=await fetch("/api/wishlist");if(e.ok){let s=(await e.json()).items.some(e=>e.id===E.id);R(s)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[d,E]);let V=(e,s)=>{w(e),z(s)},H=()=>{E&&(g({type:"ADD_ITEM",payload:{...E,price:q},selectedVariants:N?[{id:N.id,name:N.name,value:N.value,price:N.price}]:void 0}),y(!0),setTimeout(()=>y(!1),2e3))},Q=async()=>{var e;if(!(null==d?void 0:null===(e=d.user)||void 0===e?void 0:e.id)){t.push("/login");return}if(null==E?void 0:E.id){B(!0);try{O?(await fetch("/api/wishlist?productId=".concat(E.id),{method:"DELETE"})).ok&&R(!1):(await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:E.id})})).ok&&R(!0)}catch(e){console.error("Error updating wishlist:",e)}finally{B(!1)}}},U=async()=>{let e={title:(null==E?void 0:E.name)||"Check out this product",text:(null==E?void 0:E.shortDescription)||"Amazing product from Herbalicious",url:window.location.href};try{navigator.share&&navigator.canShare(e)?await navigator.share(e):(await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!"))}catch(e){console.error("Error sharing:",e);try{await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!")}catch(e){console.error("Clipboard error:",e),alert("Unable to share. Please copy the URL manually.")}}};return A?(0,a.jsx)(F.QW,{}):P||!E?(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:P||"Product not found"}),(0,a.jsx)("button",{onClick:()=>t.back(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Go Back"})]})}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,a.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[(0,a.jsx)("button",{onClick:()=>t.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:Q,disabled:_,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-5 h-5 ".concat(O?"text-red-500 fill-current":"text-gray-600")})}),(0,a.jsx)("button",{onClick:U,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(o,{className:"w-5 h-5 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"relative p-4",children:[(0,a.jsx)(k,{images:E.images||[],productName:E.name}),(0,a.jsx)("div",{className:"absolute top-8 right-8 bg-white rounded-full p-2 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:E.rating})]})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:E.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:E.shortDescription}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(Z.Z,{product:E._raw||E,showAsLinks:!0})}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:N?"₹".concat(q):(null==E?void 0:E.variants)&&E.variants.length>0?"₹".concat(Math.min(...E.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})),"-").concat(Math.max(...E.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0}))):"₹".concat((null==E?void 0:E.price)||0)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(x.Z,{className:"w-4 h-4 ".concat(s<Math.floor((null==E?void 0:E.rating)||0)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",(null==E?void 0:E.reviews)||0," reviews)"]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"flex border-b border-gray-200",children:[{id:"description",label:"Description"},{id:"ingredients",label:"Ingredients"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>(0,a.jsx)("button",{onClick:()=>v(e.id),className:"px-4 py-2 text-sm font-medium transition-colors ".concat(p===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),children:e.label},e.id))}),(0,a.jsxs)("div",{className:"py-4",children:["description"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:E.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Benefits:"}),(0,a.jsx)("ul",{className:"space-y-1",children:E.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e})]},s))})]})]}),"ingredients"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Key Ingredients:"}),(0,a.jsx)("div",{className:"space-y-2",children:E.ingredients.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-600 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e})]},s))})]}),"reviews"===p&&(0,a.jsx)("div",{children:(0,a.jsx)(M,{productId:E.id})}),"faqs"===p&&(0,a.jsx)("div",{children:(0,a.jsx)(j,{productId:E.id})})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(b,{productId:E.id,basePrice:E.price,onVariationChange:V})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>S(Math.max(1,C-1)),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"-"}),(0,a.jsx)("span",{className:"w-10 text-center font-medium",children:C}),(0,a.jsx)("button",{onClick:()=>S(C+1),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"+"})]}),(0,a.jsx)("button",{onClick:H,disabled:f,className:"flex-1 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ".concat(f?"bg-green-500 text-white":"bg-green-600 text-white hover:bg-green-700"),children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add to Cart"})]})})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mb-8",children:[(0,a.jsx)(n.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back to Products"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(k,{images:E.images||[],productName:E.name}),(0,a.jsx)("div",{className:"absolute top-6 right-6 bg-white rounded-full p-3 shadow-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.Z,{className:"w-5 h-5 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:E.rating})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:E.name}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-6",children:E.shortDescription}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(Z.Z,{product:E._raw||E,showAsLinks:!0})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:N?"₹".concat(q):(null==E?void 0:E.variants)&&E.variants.length>0?"₹".concat(Math.min(...E.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})),"-").concat(Math.max(...E.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0}))):"₹".concat((null==E?void 0:E.price)||0)}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:Q,disabled:_,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-6 h-6 ".concat(O?"text-red-500 fill-current":"text-gray-600")})}),(0,a.jsx)("button",{onClick:U,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,a.jsx)(o,{className:"w-6 h-6 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-8",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(x.Z,{className:"w-5 h-5 ".concat(s<Math.floor((null==E?void 0:E.rating)||0)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",(null==E?void 0:E.reviews)||0," reviews)"]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(b,{productId:E.id,basePrice:E.price,onVariationChange:V})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>S(Math.max(1,C-1)),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg",children:"-"}),(0,a.jsx)("span",{className:"w-12 text-center font-medium text-lg",children:C}),(0,a.jsx)("button",{onClick:()=>S(C+1),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg",children:"+"})]}),(0,a.jsx)("button",{onClick:H,disabled:f,className:"flex-1 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-3 text-lg ".concat(f?"bg-green-500 text-white":"bg-green-600 text-white hover:bg-green-700"),children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"w-6 h-6"}),(0,a.jsx)("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.Z,{className:"w-6 h-6"}),(0,a.jsx)("span",{children:"Add to Cart"})]})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex border-b border-gray-200 mb-6",children:[{id:"description",label:"Description"},{id:"ingredients",label:"Ingredients"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>(0,a.jsx)("button",{onClick:()=>v(e.id),className:"px-6 py-3 font-medium transition-colors ".concat(p===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),children:e.label},e.id))}),(0,a.jsxs)("div",{children:["description"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6 leading-relaxed",children:E.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Benefits:"}),(0,a.jsx)("ul",{className:"space-y-3",children:E.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-600",children:e})]},s))})]})]}),"ingredients"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Key Ingredients:"}),(0,a.jsx)("div",{className:"space-y-3",children:E.ingredients.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-600 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-600",children:e})]},s))})]}),"reviews"===p&&(0,a.jsx)("div",{children:(0,a.jsx)(M,{productId:E.id})}),"faqs"===p&&(0,a.jsx)("div",{children:(0,a.jsx)(j,{productId:E.id})})]})]})]})]})]})})]})}},1380:function(e,s,t){"use strict";function a(e){return function(e){let s=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(isNaN(e))return"₹0";let t=new Intl.NumberFormat("en-IN",{minimumFractionDigits:s?2:0,maximumFractionDigits:s?2:0}).format(e);return"₹".concat(t)}(e,!0)}function r(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,s){return(e?r(e):r(s))||"product"}t.d(s,{GD:function(){return r},T4:function(){return a},w:function(){return l}})},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},401:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},875:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},407:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},2208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9547:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},5863:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2023:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},1239:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])}},function(e){e.O(0,[605,1451,5704,1760,23,2971,2117,1744],function(){return e(e.s=157)}),_N_E=e.O()}]);