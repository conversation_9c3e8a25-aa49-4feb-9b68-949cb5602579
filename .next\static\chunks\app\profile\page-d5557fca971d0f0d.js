(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4178],{3526:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,6370))},6370:function(e,s,t){"use strict";var a=t(7437),l=t(2265),r=t(7648),i=t(605),n=t(4794),d=t(8997),c=t(2369),o=t(8906),x=t(407),m=t(7692);s.default=()=>{var e,s,t,h,u,g,f,j;let{data:p}=(0,i.useSession)(),[y,N]=(0,l.useState)(null),[b,v]=(0,l.useState)(!0);(0,l.useEffect)(()=>{(async()=>{var e;if(null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/users/".concat(p.user.id,"/stats"));if(e.ok){let s=await e.json();N(s.data)}}catch(e){console.error("Error fetching user stats:",e)}finally{v(!1)}})()},[null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.id]);let w={name:(null==p?void 0:null===(s=p.user)||void 0===s?void 0:s.name)||"User",email:(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.email)||"",avatar:(null==p?void 0:null===(h=p.user)||void 0===h?void 0:h.image)||"",joinDate:(null==y?void 0:null===(u=y.user)||void 0===u?void 0:u.joinDate)?new Date(y.user.joinDate).toLocaleDateString("en-US",{year:"numeric",month:"long"}):"Loading...",totalOrders:(null==y?void 0:null===(g=y.orders)||void 0===g?void 0:g.total)||0,totalSpent:(null==y?void 0:null===(f=y.orders)||void 0===f?void 0:f.totalSpent)||0,isAdmin:(null==p?void 0:null===(j=p.user)||void 0===j?void 0:j.role)==="ADMIN",accountStatus:(null==y?void 0:y.accountStatus)||"Loading..."},Z=async()=>{try{await (0,i.signOut)({redirect:!1,callbackUrl:"/"}),window.location.replace("/")}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},k=[{icon:n.Z,title:"Order History",description:"View your past orders",href:"/order-history",color:"bg-green-100 text-green-600"},{icon:d.Z,title:"Wishlist",description:"Your saved items",href:"/wishlist",color:"bg-green-100 text-green-600"},{icon:c.Z,title:"Edit Profile",description:"Update your profile details",href:"/edit-profile",color:"bg-green-100 text-green-600"}],S=[{icon:o.Z,title:"Admin Panel",description:"Manage store (Admin only)",href:"/admin",color:"bg-green-100 text-green-600"}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 grid grid-cols-1 lg:grid-cols-12 gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden col-span-12",children:(0,a.jsxs)("div",{className:"px-4 py-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center",children:w.avatar?(0,a.jsx)("img",{src:w.avatar,alt:w.name,className:"w-16 h-16 rounded-full"}):(0,a.jsx)(c.Z,{className:"w-8 h-8 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:w.name}),(0,a.jsx)("p",{className:"text-gray-600",children:w.email}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",w.joinDate]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-800",children:w.totalOrders}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-800",children:["₹",w.totalSpent]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Spent"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[k.map((e,s)=>(0,a.jsxs)(r.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(e.color),children:(0,a.jsx)(e.icon,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,a.jsx)(x.Z,{className:"w-5 h-5 text-gray-400"})]},s)),w.isAdmin&&S.map((e,s)=>(0,a.jsxs)(r.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(e.color),children:(0,a.jsx)(e.icon,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,a.jsx)(x.Z,{className:"w-5 h-5 text-gray-400"})]},"admin-".concat(s)))]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:Z,className:"w-full flex items-center justify-center space-x-2 p-4 bg-red-50 text-red-600 rounded-2xl font-medium hover:bg-red-100 transition-colors",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Sign Out"})]})})]})}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-12",children:"Profile"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4",children:w.avatar?(0,a.jsx)("img",{src:w.avatar,alt:w.name,className:"w-24 h-24 rounded-full"}):(0,a.jsx)(c.Z,{className:"w-12 h-12 text-white"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-1",children:w.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-1",children:w.email}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",w.joinDate]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Account Stats"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Orders"}),(0,a.jsx)("span",{className:"font-semibold text-gray-800",children:w.totalOrders})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Spent"}),(0,a.jsxs)("span",{className:"font-semibold text-gray-800",children:["₹",w.totalSpent]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Account Status"}),(0,a.jsx)("span",{className:"font-semibold text-green-600",children:w.accountStatus})]})]})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Quick Actions"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:k.map((e,s)=>(0,a.jsxs)(r.default,{href:e.href,className:"flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-100",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat(e.color),children:(0,a.jsx)(e.icon,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Account Actions"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(r.default,{href:"/edit-profile",className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors",children:[(0,a.jsx)(c.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Edit Profile"})]}),w.isAdmin&&(0,a.jsxs)(r.default,{href:"/admin",className:"flex items-center space-x-2 px-6 py-3 bg-green-700 text-white rounded-xl font-medium hover:bg-green-800 transition-colors",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Admin Panel"})]}),(0,a.jsxs)("button",{onClick:Z,className:"flex items-center space-x-2 px-6 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors ml-auto",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})]})}},407:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8997:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},7692:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},4794:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},8906:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},function(e){e.O(0,[605,1451,5704,1760,2971,2117,1744],function(){return e(e.s=3526)}),_N_E=e.O()}]);