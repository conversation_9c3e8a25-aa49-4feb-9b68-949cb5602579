(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3021],{4239:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,43))},5975:function(e,s,t){"use strict";t.d(s,{Z:function(){return x}});var a=t(7437);t(2265);var r=t(7648),l=t(9547),n=t(2023),i=t(8997);let c=(0,t(9763).Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var o=t(1239),d=t(2208),x=e=>{let{product:s,showAsLinks:t=!1,className:x="",maxCategories:m}=e,u=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(s),h=m?u.slice(0,m):u;if(0===h.length)return null;let g=e=>({Skincare:(0,a.jsx)(l.Z,{className:"w-3 h-3"}),"Hair Care":(0,a.jsx)(n.Z,{className:"w-3 h-3"}),"Body Care":(0,a.jsx)(i.Z,{className:"w-3 h-3"}),cleanser:(0,a.jsx)(c,{className:"w-3 h-3"}),serum:(0,a.jsx)(o.Z,{className:"w-3 h-3"}),moisturizer:(0,a.jsx)(c,{className:"w-3 h-3"}),mask:(0,a.jsx)(d.Z,{className:"w-3 h-3"}),exfoliator:(0,a.jsx)(n.Z,{className:"w-3 h-3"}),"eye-care":(0,a.jsx)(d.Z,{className:"w-3 h-3"})})[e]||(0,a.jsx)(l.Z,{className:"w-3 h-3"});return(0,a.jsx)("div",{className:"flex flex-wrap gap-3 ".concat(x),children:h.map(e=>{let s=g(e.name);return t?(0,a.jsxs)(r.default,{href:"/shop?category=".concat(e.slug),className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600 hover:text-black transition-colors",children:[s,(0,a.jsx)("span",{children:e.name})]},e.id):(0,a.jsxs)("span",{className:"inline-flex items-center gap-1.5 text-xs font-medium text-green-600",children:[s,(0,a.jsx)("span",{children:e.name})]},e.id)})})}},43:function(e,s,t){"use strict";t.d(s,{default:function(){return M}});var a=t(7437),r=t(2265),l=t(9376),n=t(3247),i=t(9763);let c=(0,i.Z)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var o=t(2489),d=t(1473),x=t(7586);let m=(0,i.Z)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var u=t(7648),h=t(3145),g=t(8997),p=t(6595),y=t(9397),f=t(6275),v=t(3827);function j(e){return!e||e.includes("placeholder")||e.includes("api/placeholder")?"/images/default-product.jpg":e}var b=t(5975),N=e=>{let{product:s,featured:t=!1,viewMode:l="grid"}=e,{dispatch:n}=(0,v.j)(),[i,c]=(0,r.useState)(!1),[o,d]=(0,r.useState)(!1),x=e=>{e.preventDefault(),e.stopPropagation();let t=s.price||0,a=s.variants;if(0===s.price&&a&&a.length>0){let e=a.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0}).filter(e=>e>0);e.length>0&&(t=Math.max(...e))}n({type:"ADD_ITEM",payload:{...s,price:t}})},m=e=>{e.preventDefault(),e.stopPropagation(),c(!i)},N=s.variants,w="₹".concat(s.price||0);if(N&&N.length>0){let e=[s.price||0,...N.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})].filter(e=>e>0);if(e.length>0){let s=Math.min(...e),t=Math.max(...e);s!==t&&(w="₹".concat(s," - ₹").concat(t))}}return"list"===l?(0,a.jsx)(u.default,{href:"/product/".concat(s.slug||s.id),className:"block group",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-green-200",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"relative w-28 h-28 sm:w-40 sm:h-40 flex-shrink-0",children:(0,a.jsxs)("div",{className:"w-full h-full relative overflow-hidden rounded-l-2xl",children:[!o&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),(0,a.jsx)(h.default,{src:j(s.image),alt:s.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300 ".concat(o?"opacity-100":"opacity-0"),sizes:"(max-width: 640px) 112px, 160px",onLoad:()=>d(!0)})]})}),(0,a.jsxs)("div",{className:"flex-1 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 line-clamp-1 text-base sm:text-lg",children:s.name}),(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)(b.Z,{product:s._raw||s,maxCategories:1})})]}),(0,a.jsx)("button",{onClick:m,className:"p-1.5 sm:p-2 rounded-full transition-colors ml-2 ".concat(i?"text-red-500 bg-red-50 hover:bg-red-100":"text-gray-400 hover:text-red-500 hover:bg-red-50"),children:(0,a.jsx)(g.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 ".concat(i?"fill-current":"")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:w}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"text-xs text-gray-600 ml-1",children:s.rating})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["(",s.reviews," reviews)"]})]})]}),(0,a.jsxs)("button",{onClick:x,className:"bg-green-600 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center space-x-1.5 sm:space-x-2",children:[(0,a.jsx)(y.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"}),(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Add"})]})]})]})]})})}):(0,a.jsx)(u.default,{href:"/product/".concat(s.slug||s.id),className:"block group",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-green-200 ".concat(t?"w-72 flex-shrink-0 lg:w-80":"w-full"),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-full aspect-square relative overflow-hidden",children:[!o&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),(0,a.jsx)(h.default,{src:j(s.image),alt:s.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300 ".concat(o?"opacity-100":"opacity-0"),sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>d(!0)})]}),(0,a.jsx)("div",{className:"absolute top-3 right-3 flex flex-col space-y-2",children:(0,a.jsx)("button",{onClick:m,className:"p-2 rounded-full shadow-sm transition-all duration-200 ".concat(i?"bg-red-500 text-white":"bg-white text-gray-600 hover:text-red-500 hover:bg-red-50"),children:(0,a.jsx)(g.Z,{className:"w-4 h-4 ".concat(i?"fill-current":"")})})}),(0,a.jsx)("div",{className:"absolute top-3 left-3 bg-white rounded-full px-2 py-1 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700",children:s.rating})]})})]}),(0,a.jsxs)("div",{className:"p-4 lg:p-5",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-1 text-base lg:text-lg leading-tight",children:s.name}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(b.Z,{product:s._raw||s,className:"mb-2",maxCategories:1})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-base lg:text-lg font-bold text-gray-900 mb-1",children:w}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[s.reviews," reviews"]})]}),(0,a.jsx)("button",{onClick:x,className:"bg-green-600 text-white p-2.5 lg:p-3 rounded-full hover:bg-green-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105 active:scale-95",children:(0,a.jsx)(f.Z,{className:"w-4 h-4 lg:w-5 lg:h-5"})})]})]})]})})},w=t(5863),k=e=>{let{hasMore:s,loading:t,onLoadMore:l,threshold:n=100,children:i}=e,c=(0,r.useRef)(null),o=(0,r.useRef)(!1),d=(0,r.useCallback)(e=>{let[a]=e;a.isIntersecting&&s&&!t&&!o.current&&(o.current=!0,l(),setTimeout(()=>{o.current=!1},1e3))},[s,t,l]);return(0,r.useEffect)(()=>{let e=c.current;if(!e)return;let s=new IntersectionObserver(d,{threshold:.1,rootMargin:"".concat(n,"px")});return s.observe(e),()=>{e&&s.unobserve(e)}},[d,n]),(0,a.jsxs)("div",{children:[i,(0,a.jsxs)("div",{ref:c,className:"w-full py-8",children:[t&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,a.jsx)(w.Z,{className:"w-5 h-5 animate-spin"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Loading more products..."})]})}),!s&&!t&&(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("div",{className:"inline-flex items-center px-4 py-2 bg-gray-100 rounded-full",children:(0,a.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"You've reached the end of our collection"})})})]})]})},Z=t(3905);let C=e=>{var s,t;let a=(null===(s=e.category)||void 0===s?void 0:s.slug)||"skincare";if(e.price,e.variants&&e.variants.length>0){let s=[e.price||0,...e.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})].filter(e=>e>0);s.length>0&&([...s],[...s])}return{id:e.id,slug:e.slug,name:e.name,description:e.description,shortDescription:e.shortDescription,price:e.price||0,image:(null===(t=e.images[0])||void 0===t?void 0:t.url)||"/placeholde.jpg",images:e.images.map(e=>({id:e.id,url:e.url,alt:e.alt,position:e.position||0})),category:a,featured:e.isFeatured,ingredients:[],benefits:[],rating:0,reviews:0,variants:e.variants,_raw:e}};var M=()=>{let e=(0,l.useSearchParams)(),[s,t]=(0,r.useState)("all"),[i,u]=(0,r.useState)("random"),[h,g]=(0,r.useState)(!1),[p,y]=(0,r.useState)([]),[f,v]=(0,r.useState)([]),[j,b]=(0,r.useState)([]),[w,M]=(0,r.useState)([]),[S,L]=(0,r.useState)(!0),[_,P]=(0,r.useState)(!1),[A,E]=(0,r.useState)(null),[D,z]=(0,r.useState)(""),[F,q]=(0,r.useState)("grid"),[R,I]=(0,r.useState)([0,1e4]),[H,O]=(0,r.useState)(!1),[T,U]=(0,r.useState)(1),[B,G]=(0,r.useState)(!0),V=(0,r.useRef)(null);(0,r.useEffect)(()=>{let s=e.get("category");s&&t(s)},[e]),(0,r.useEffect)(()=>{(async()=>{try{L(!0);let e=new URLSearchParams({limit:"1000",sort:i}),[s,t]=await Promise.all([fetch("/api/products?".concat(e.toString())),fetch("/api/categories")]),[a,r]=await Promise.all([s.json(),t.json()]);if(a.success&&r.success){let e=a.data.filter(e=>e.isActive).map(e=>({...C(e),_raw:e}));if(y(e),v(e),0===w.length){let e=[{id:"all",name:"All Products"},...r.data.map(e=>({id:e.slug,name:e.name}))];M(e)}}else E("Failed to fetch data")}catch(e){console.error("Error fetching data:",e),E("Failed to load products")}finally{L(!1)}})()},[i]),(0,r.useEffect)(()=>{let e=p;if("all"!==s&&(e=e.filter(e=>{var t,a,r;let l=e._raw;return(null===(t=l.category)||void 0===t?void 0:t.slug)===s||null!==(r=null===(a=l.productCategories)||void 0===a?void 0:a.some(e=>e.category.slug===s))&&void 0!==r&&r})),D.trim()){let s=D.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s)||e.shortDescription.toLowerCase().includes(s))}v(e=e.filter(e=>{let s=e.price||0;return s>=R[0]&&s<=R[1]})),U(1),G(e.length>12)},[s,p,D,R]),(0,r.useEffect)(()=>{let e=12*T;b(f.slice(0,e)),G(e<f.length)},[f,T]);let W=()=>{!_&&B&&(P(!0),setTimeout(()=>{U(e=>e+1),P(!1)},500))};(0,r.useEffect)(()=>{H&&V.current&&V.current.focus()},[H]);let Y=e=>{t(e)};return S?(0,a.jsx)(Z.sW,{}):A?(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:A}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Try Again"})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:[(0,a.jsxs)("div",{className:"px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Shop"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>O(!H),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("button",{onClick:()=>g(!h),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filters"})]})]})]}),(0,a.jsx)("div",{className:"transition-all duration-300 ".concat(H?"max-h-20 opacity-100 mb-3":"max-h-0 opacity-0 overflow-hidden"),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{ref:V,type:"text",placeholder:"Search products...",value:D,onChange:e=>z(e.target.value),className:"w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),D&&(0,a.jsx)("button",{onClick:()=>z(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(o.Z,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[f.length," products found"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>q("grid"),className:"p-1.5 rounded ".concat("grid"===F?"bg-green-100 text-green-600":"text-gray-400"),children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>q("list"),className:"p-1.5 rounded ".concat("list"===F?"bg-green-100 text-green-600":"text-gray-400"),children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsx)("div",{className:"border-t bg-white transition-all duration-300 ".concat(h?"max-h-screen opacity-100":"max-h-0 opacity-0 overflow-hidden"),children:(0,a.jsxs)("div",{className:"px-4 py-4 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Sort by"}),(0,a.jsxs)("select",{value:i,onChange:e=>u(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-sm",children:[(0,a.jsx)("option",{value:"random",children:"Random (Default)"}),(0,a.jsx)("option",{value:"name_asc",children:"Name (A-Z)"}),(0,a.jsx)("option",{value:"name_desc",children:"Name (Z-A)"}),(0,a.jsx)("option",{value:"price_asc",children:"Price (Low to High)"}),(0,a.jsx)("option",{value:"price_desc",children:"Price (High to Low)"}),(0,a.jsx)("option",{value:"newest",children:"Newest First"}),(0,a.jsx)("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Price Range"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:R[0],onChange:e=>I([parseInt(e.target.value)||0,R[1]]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),(0,a.jsx)("span",{className:"text-gray-400",children:"-"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:R[1],onChange:e=>I([R[0],parseInt(e.target.value)||1e4]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",R[0]," - ₹",R[1]]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Categories"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:w.map(e=>(0,a.jsx)("button",{onClick:()=>Y(e.id),className:"px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ".concat(s===e.id?"bg-green-600 text-white shadow-md scale-105":"bg-gray-100 text-gray-700 hover:bg-gray-200 active:scale-95"),children:e.name},e.id))})]}),(0,a.jsx)("button",{onClick:()=>{t("all"),z(""),I([0,1e4]),u("random")},className:"w-full py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors",children:"Clear All Filters"})]})})]}),(0,a.jsx)("div",{className:"px-4 py-6",children:0===f.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(n.Z,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Try adjusting your search or filters"}),(0,a.jsx)("button",{onClick:()=>{t("all"),z(""),I([0,1e4])},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):(0,a.jsx)(k,{hasMore:B,loading:_,onLoadMore:W,children:(0,a.jsx)("div",{className:"".concat("grid"===F?"grid grid-cols-2 gap-4":"space-y-4"),children:j.map(e=>(0,a.jsx)(N,{product:e,viewMode:F},e.id))})})})]}),(0,a.jsxs)("div",{className:"hidden lg:block",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Our Products"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover our complete collection of natural skincare products, carefully crafted with botanical ingredients"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(n.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:"Search products...",value:D,onChange:e=>z(e.target.value),className:"w-full pl-12 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),D&&(0,a.jsx)("button",{onClick:()=>z(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(o.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m,{className:"w-5 h-5 text-gray-600"}),(0,a.jsxs)("select",{value:i,onChange:e=>u(e.target.value),className:"px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",children:[(0,a.jsx)("option",{value:"random",children:"Random (Default)"}),(0,a.jsx)("option",{value:"name_asc",children:"Name (A-Z)"}),(0,a.jsx)("option",{value:"name_desc",children:"Name (Z-A)"}),(0,a.jsx)("option",{value:"price_asc",children:"Price (Low to High)"}),(0,a.jsx)("option",{value:"price_desc",children:"Price (High to Low)"}),(0,a.jsx)("option",{value:"newest",children:"Newest First"}),(0,a.jsx)("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>q("grid"),className:"p-2 rounded ".concat("grid"===F?"bg-white shadow-sm text-green-600":"text-gray-500"),children:(0,a.jsx)(d.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>q("list"),className:"p-2 rounded ".concat("list"===F?"bg-white shadow-sm text-green-600":"text-gray-500"),children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-center gap-6 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Price Range:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",placeholder:"Min",value:R[0],onChange:e=>I([parseInt(e.target.value)||0,R[1]]),className:"w-20 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),(0,a.jsx)("span",{className:"text-gray-400",children:"-"}),(0,a.jsx)("input",{type:"number",placeholder:"Max",value:R[1],onChange:e=>I([R[0],parseInt(e.target.value)||1e4]),className:"w-20 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]})]})}),(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:w.map(e=>(0,a.jsx)("button",{onClick:()=>Y(e.id),className:"px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ".concat(s===e.id?"bg-green-600 text-white shadow-md scale-105":"text-gray-700 hover:bg-gray-100 active:scale-95"),children:e.name},e.id))})})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-gray-600",children:[f.length," products found"]}),(0,a.jsx)("button",{onClick:()=>{t("all"),z(""),I([0,1e4]),u("random")},className:"text-sm text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),0===f.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(n.Z,{className:"w-10 h-10 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"Try adjusting your search or filters"}),(0,a.jsx)("button",{onClick:()=>{t("all"),z(""),I([0,1e4])},className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):(0,a.jsx)(k,{hasMore:B,loading:_,onLoadMore:W,children:(0,a.jsx)("div",{className:"".concat("grid"===F?"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6":"grid grid-cols-1 lg:grid-cols-2 gap-6"),children:j.map(e=>(0,a.jsx)(N,{product:e,viewMode:F},e.id))})})]})]})]})}},2208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1473:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},9547:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},7586:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},5863:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},9397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3247:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2023:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},1239:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])}},function(e){e.O(0,[605,1451,5704,1760,23,2971,2117,1744],function(){return e(e.s=4239)}),_N_E=e.O()}]);