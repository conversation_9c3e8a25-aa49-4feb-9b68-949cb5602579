(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4456],{5679:function(e,s,t){Promise.resolve().then(t.bind(t,1760)),Promise.resolve().then(t.bind(t,6622))},6622:function(e,s,t){"use strict";var l=t(7437),r=t(2265),a=t(9376),i=t(605),n=t(7648),c=t(3145),d=t(2660),o=t(2489),x=t(8997),m=t(6275),h=t(6595),g=t(3827),f=t(3905);s.default=()=>{let e=(0,a.useRouter)(),{data:s}=(0,i.useSession)(),{dispatch:t}=(0,g.j)(),[u,p]=(0,r.useState)([]),[j,b]=(0,r.useState)(!0),[N,v]=(0,r.useState)(null),[w,y]=(0,r.useState)([]);(0,r.useEffect)(()=>{(async()=>{var e;if(!(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id)){b(!1);return}try{let e=await fetch("/api/wishlist");if(!e.ok)throw Error("Failed to fetch wishlist");let s=await e.json();p(s.items||[])}catch(e){console.error("Error fetching wishlist:",e),v("Failed to load wishlist")}finally{b(!1)}})()},[s]),(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/products?limit=4");if(e.ok){let s=await e.json();y(s.data||[])}}catch(e){console.error("Error fetching recommendations:",e)}})()},[]);let k=async e=>{try{if(!(await fetch("/api/wishlist?productId=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to remove from wishlist");p(s=>s.filter(s=>s.id!==e))}catch(e){console.error("Error removing from wishlist:",e)}},Z=e=>{t({type:"ADD_ITEM",payload:{id:e.id,name:e.name,description:e.shortDescription,shortDescription:e.shortDescription,price:e.price,image:e.image,category:"general",featured:!1,ingredients:[],benefits:[],rating:e.rating,reviews:e.reviews}})},C=()=>{u.forEach(e=>{t({type:"ADD_ITEM",payload:{id:e.id,name:e.name,description:e.shortDescription,shortDescription:e.shortDescription,price:e.price,image:e.image,category:"general",featured:!1,ingredients:[],benefits:[],rating:e.rating,reviews:e.reviews}})})};return(0,l.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,l.jsxs)("div",{className:"lg:hidden",children:[(0,l.jsx)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,l.jsx)(d.Z,{className:"w-5 h-5"})}),(0,l.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"Wishlist"}),(0,l.jsx)("div",{className:"ml-auto",children:(0,l.jsxs)("span",{className:"text-sm text-gray-600",children:[u.length," items"]})})]})}),(0,l.jsx)("div",{className:"px-4 py-6",children:j?(0,l.jsx)(f.Gw,{}):N?(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)(o.Z,{className:"w-12 h-12 text-red-500"})}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Error loading wishlist"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:N}),(0,l.jsx)("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Try Again"})]}):s?u.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsxs)("button",{onClick:C,className:"w-full bg-green-600 text-white py-3 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[(0,l.jsx)(m.Z,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Add All to Cart"})]})}),(0,l.jsx)("div",{className:"space-y-4",children:u.map(e=>(0,l.jsx)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(n.default,{href:"/product/".concat(e.id),className:"block",children:(0,l.jsx)("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"80px"})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,l.jsx)(n.default,{href:"/product/".concat(e.id),children:(0,l.jsx)("h3",{className:"font-semibold text-gray-800 line-clamp-1",children:e.name})}),(0,l.jsx)("button",{onClick:()=>k(e.id),className:"p-1 text-red-500 hover:bg-red-50 rounded-full transition-colors ml-2",children:(0,l.jsx)(o.Z,{className:"w-4 h-4"})})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.shortDescription}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.rating})]}),(0,l.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.reviews," reviews)"]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.price]}),(0,l.jsxs)("button",{onClick:()=>Z(e),className:"bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-green-700 transition-colors flex items-center space-x-1",children:[(0,l.jsx)(m.Z,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Add to Cart"})]})]})]})]})},e.id))}),(0,l.jsxs)("div",{className:"mt-8",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"You might also like"}),(0,l.jsx)("div",{className:"grid grid-cols-2 gap-4",children:w.slice(0,4).map(e=>(0,l.jsx)(n.default,{href:"/product/".concat(e.id),className:"block",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[(0,l.jsx)("div",{className:"w-full h-32 relative rounded-xl overflow-hidden mb-3",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 50vw, 25vw"})}),(0,l.jsx)("h3",{className:"font-medium text-gray-800 text-sm line-clamp-1 mb-1",children:e.name}),(0,l.jsx)("p",{className:"text-xs text-gray-600 line-clamp-1 mb-2",children:e.shortDescription}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",e.price]}),(0,l.jsx)("button",{className:"p-1 text-gray-400 hover:text-red-500 transition-colors",children:(0,l.jsx)(x.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)(x.Z,{className:"w-12 h-12 text-gray-400"})}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your wishlist is empty"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"Save products you love for later"}),(0,l.jsx)(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Start Shopping"})]}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)(x.Z,{className:"w-12 h-12 text-gray-400"})}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Sign in to view wishlist"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"Create an account to save your favorite products"}),(0,l.jsx)(n.default,{href:"/login",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Sign In"})]})})]}),(0,l.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,l.jsxs)("div",{className:"py-8",children:[(0,l.jsx)("div",{className:"flex items-center mb-8",children:(0,l.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,l.jsx)(d.Z,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Back"})]})}),(0,l.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-800",children:"My Wishlist"}),(0,l.jsxs)("span",{className:"text-gray-600",children:[u.length," items saved"]})]}),j?(0,l.jsx)(f.Gw,{}):N?(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,l.jsx)(o.Z,{className:"w-16 h-16 text-red-500"})}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Error loading wishlist"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:N}),(0,l.jsx)("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Try Again"})]}):s?u.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsxs)("button",{onClick:C,className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center space-x-3 text-lg",children:[(0,l.jsx)(m.Z,{className:"w-6 h-6"}),(0,l.jsx)("span",{children:"Add All to Cart"})]})}),(0,l.jsx)("div",{className:"grid grid-cols-3 gap-6 mb-12",children:u.map(e=>(0,l.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,l.jsxs)("div",{className:"relative mb-4",children:[(0,l.jsx)(n.default,{href:"/product/".concat(e.id),children:(0,l.jsx)("div",{className:"w-full h-48 relative rounded-xl overflow-hidden",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300",sizes:"(max-width: 1200px) 50vw, 33vw"})})}),(0,l.jsx)("button",{onClick:()=>k(e.id),className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-red-500 hover:bg-red-50 transition-colors",children:(0,l.jsx)(o.Z,{className:"w-4 h-4"})})]}),(0,l.jsx)(n.default,{href:"/product/".concat(e.id),children:(0,l.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 hover:text-green-600 transition-colors",children:e.name})}),(0,l.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.shortDescription}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.rating})]}),(0,l.jsxs)("span",{className:"text-gray-500",children:["(",e.reviews," reviews)"]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:["₹",e.price]}),(0,l.jsxs)("button",{onClick:()=>Z(e),className:"bg-green-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,l.jsx)(m.Z,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Add to Cart"})]})]})]},e.id))}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"You might also like"}),(0,l.jsx)("div",{className:"grid grid-cols-4 gap-6",children:w.slice(0,4).map(e=>(0,l.jsx)(n.default,{href:"/product/".concat(e.id),className:"block group",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,l.jsxs)("div",{className:"relative mb-4",children:[(0,l.jsx)("div",{className:"w-full h-40 relative rounded-xl overflow-hidden",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 1200px) 50vw, 25vw"})}),(0,l.jsx)("button",{className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-gray-400 hover:text-red-500 transition-colors",children:(0,l.jsx)(x.Z,{className:"w-4 h-4"})})]}),(0,l.jsx)("h3",{className:"font-medium text-gray-800 mb-2 group-hover:text-green-600 transition-colors line-clamp-1",children:e.name}),(0,l.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2 mb-3",children:e.shortDescription}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",e.price]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(h.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.rating})]})]})]})},e.id))})]})]}):(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,l.jsx)(x.Z,{className:"w-16 h-16 text-gray-400"})}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Your wishlist is empty"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Save products you love for later and never lose track of them"}),(0,l.jsx)(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Start Shopping"})]}):(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,l.jsx)(x.Z,{className:"w-16 h-16 text-gray-400"})}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Sign in to view wishlist"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Create an account to save your favorite products"}),(0,l.jsx)(n.default,{href:"/login",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Sign In"})]})]})})]})}},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},function(e){e.O(0,[605,1451,5704,1760,23,2971,2117,1744],function(){return e(e.s=5679)}),_N_E=e.O()}]);