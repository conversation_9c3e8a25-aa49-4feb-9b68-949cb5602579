'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AdminPageLoader } from '@/app/components/loaders/AdminLoaders';
import { AlertCircle, Check, ArrowRight, Palette, Users, Download, MessageSquare } from 'lucide-react';
import SearchableProductDropdown from '../../components/admin/SearchableProductDropdown';
import ColorPicker from '../../components/admin/ColorPicker';
import TestimonialsModal from '../../components/admin/TestimonialsModal';

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  images: { url: string; alt: string }[];
}

interface HomepageSetting {
  id: string;
  // Hero Section
  heroTitle: string | null;
  heroSubtitle: string | null;
  heroCtaText: string | null;
  heroCtaLink: string | null;
  heroSecondaryCtaText: string | null;
  heroSecondaryCtaLink: string | null;
  heroBadgeText: string | null;
  heroBackgroundColor: string | null;
  showHero: boolean;

  // Hero Trust Indicators
  trustIndicator1Value: string | null;
  trustIndicator1Label: string | null;
  trustIndicator2Value: string | null;
  trustIndicator2Label: string | null;
  trustIndicator3Value: string | null;
  trustIndicator3Label: string | null;
  trustIndicator4Value: string | null;
  trustIndicator4Label: string | null;

  // Product of the Month
  productOfTheMonthId: string | null;
  showProductOfMonth: boolean;

  // Promotional Banner
  bannerText: string | null;
  bannerCtaText: string | null;
  bannerCtaLink: string | null;
  bannerBackgroundColor: string | null;
  showBanner: boolean;

  // Sections
  showCategories: boolean;
  productSectionBgColor: string | null;
  bestsellerIds: string[];
  showBestsellers: boolean;

  // Newsletter
  newsletterTitle: string | null;
  newsletterSubtitle: string | null;
  showNewsletter: boolean;

  // Trust Badges
  showTrustBadges: boolean;

  // Flash Sale Section
  flashSaleTitle: string | null;
  flashSaleSubtitle: string | null;
  flashSaleEndDate: string | null;
  flashSaleBackgroundColor: string | null;
  showFlashSale: boolean;

  // Testimonials Section
  testimonialsTitle: string | null;
  testimonialsSubtitle: string | null;
  testimonialsBackgroundColor: string | null;
  showTestimonials: boolean;

  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function HomepageSettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [showTestimonialsModal, setShowTestimonialsModal] = useState(false);
  const [settings, setSettings] = useState<HomepageSetting>({
    id: '',
    // Hero Section
    heroTitle: 'Natural Skincare Essentials',
    heroSubtitle: 'Discover our botanical collection crafted with nature\'s finest ingredients for radiant, healthy skin',
    heroCtaText: 'Shop Collection',
    heroCtaLink: '/shop',
    heroSecondaryCtaText: 'View Categories',
    heroSecondaryCtaLink: '/categories',
    heroBadgeText: 'New Collection',
    heroBackgroundColor: '#f0fdf4',
    showHero: true,

    // Hero Trust Indicators
    trustIndicator1Value: '100%',
    trustIndicator1Label: 'Natural',
    trustIndicator2Value: '500+',
    trustIndicator2Label: 'Happy Customers',
    trustIndicator3Value: '50+',
    trustIndicator3Label: 'Products',
    trustIndicator4Value: '4.8★',
    trustIndicator4Label: 'Rating',

    // Product of the Month
    productOfTheMonthId: null,
    showProductOfMonth: true,

    // Promotional Banner
    bannerText: null,
    bannerCtaText: null,
    bannerCtaLink: null,
    bannerBackgroundColor: '#22c55e',
    showBanner: true,

    // Sections
    showCategories: true,
    productSectionBgColor: '#f0fdf4',
    bestsellerIds: [],
    showBestsellers: true,

    // Newsletter
    newsletterTitle: 'Stay Updated',
    newsletterSubtitle: 'Get the latest updates on new products and exclusive offers',
    showNewsletter: true,

    // Trust Badges
    showTrustBadges: true,

    // Flash Sale Section
    flashSaleTitle: 'Weekend Flash Sale',
    flashSaleSubtitle: 'Get 25% off all natural skincare products',
    flashSaleEndDate: null,
    flashSaleBackgroundColor: '#16a34a',
    showFlashSale: true,

    // Testimonials Section
    testimonialsTitle: 'What Our Customers Say',
    testimonialsSubtitle: 'Real reviews from real customers who love our natural skincare',
    testimonialsBackgroundColor: '#f0fdf4',
    showTestimonials: true,

    isActive: true,
    createdAt: '',
    updatedAt: '',
  });

  // Check if user is admin
  useEffect(() => {
    if (status === 'loading') return;

    if (!session || session.user.role !== 'ADMIN') {
      router.push('/login');
      return;
    }

    fetchInitialData();
  }, [session, status, router]);



  // Show notification
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  const fetchInitialData = async () => {
    try {
      // Fetch all products for admin selection (no pagination limit)
      const productsRes = await fetch('/api/products?limit=1000');
      const productsData = await productsRes.json();
      setProducts(productsData.data || []);

      // Fetch current homepage settings
      const settingsRes = await fetch('/api/homepage-settings');
      const settingsData = await settingsRes.json();
      
      if (settingsData.success && settingsData.data.settings) {
        setSettings(settingsData.data.settings);
      } else {
        // Create default settings object
        setSettings({
          id: '',
          // Hero Section
          heroTitle: 'Natural Skincare Essentials',
          heroSubtitle: 'Discover our botanical collection crafted with nature\'s finest ingredients for radiant, healthy skin',
          heroCtaText: 'Shop Collection',
          heroCtaLink: '/shop',
          heroSecondaryCtaText: 'View Categories',
          heroSecondaryCtaLink: '/categories',
          heroBadgeText: 'New Collection',
          heroBackgroundColor: '#f0fdf4',
          showHero: true,

          // Hero Trust Indicators
          trustIndicator1Value: '100%',
          trustIndicator1Label: 'Natural',
          trustIndicator2Value: '500+',
          trustIndicator2Label: 'Happy Customers',
          trustIndicator3Value: '50+',
          trustIndicator3Label: 'Products',
          trustIndicator4Value: '4.8★',
          trustIndicator4Label: 'Rating',

          // Product of the Month
          productOfTheMonthId: null,
          showProductOfMonth: true,

          // Promotional Banner
          bannerText: null,
          bannerCtaText: null,
          bannerCtaLink: null,
          bannerBackgroundColor: '#22c55e',
          showBanner: true,

          // Sections
          showCategories: true,
          productSectionBgColor: '#f0fdf4',
          bestsellerIds: [],
          showBestsellers: true,

          // Newsletter
          newsletterTitle: 'Stay Updated',
          newsletterSubtitle: 'Get the latest updates on new products and exclusive offers',
          showNewsletter: true,

          // Trust Badges
          showTrustBadges: true,

          // Flash Sale Section
          flashSaleTitle: 'Weekend Flash Sale',
          flashSaleSubtitle: 'Get 25% off all natural skincare products',
          flashSaleEndDate: null,
          flashSaleBackgroundColor: '#16a34a',
          showFlashSale: true,

          // Testimonials Section
          testimonialsTitle: 'What Our Customers Say',
          testimonialsSubtitle: 'Real reviews from real customers who love our natural skincare',
          testimonialsBackgroundColor: '#f0fdf4',
          showTestimonials: true,

          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      showNotification('error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/homepage-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      if (result.success) {
        showNotification('success', 'Homepage settings saved successfully');
        // Update the settings with the returned data
        if (result.data) {
          setSettings(result.data);
        }
      } else {
        showNotification('error', result.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      showNotification('error', 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof HomepageSetting, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (status === 'loading' || loading) {
    return <AdminPageLoader />;
  }

  return (
    <div className="p-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ${
          notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {notification.type === 'success' ? (
            <Check className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {notification.message}
        </div>
      )}

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Homepage Settings</h1>
        <p className="text-gray-600">Customize your homepage content, featured products, and promotional banners</p>
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-blue-800 text-sm">
            <span className="font-medium">Tip:</span> Changes made here will be reflected on your store's homepage immediately after saving.
            For best results, use high-quality images and compelling copy for your featured products.
          </p>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Hero Section</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showHero}
              onChange={(e) => handleInputChange('showHero', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-600">Show Hero Section</span>
          </label>
        </div>
        <p className="text-gray-600 text-sm mb-4">Configure the main hero section that appears at the top of your homepage.</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Hero Title</label>
            <input
              type="text"
              value={settings.heroTitle || ''}
              onChange={(e) => handleInputChange('heroTitle', e.target.value)}
              placeholder="Natural Skincare Essentials"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">CTA Button Text</label>
            <input
              type="text"
              value={settings.heroCtaText || ''}
              onChange={(e) => handleInputChange('heroCtaText', e.target.value)}
              placeholder="Shop Collection"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Hero Subtitle</label>
          <textarea
            value={settings.heroSubtitle || ''}
            onChange={(e) => handleInputChange('heroSubtitle', e.target.value)}
            placeholder="Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin"
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">CTA Button Link</label>
            <input
              type="text"
              value={settings.heroCtaLink || ''}
              onChange={(e) => handleInputChange('heroCtaLink', e.target.value)}
              placeholder="/shop"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Secondary CTA Text</label>
            <input
              type="text"
              value={settings.heroSecondaryCtaText || ''}
              onChange={(e) => handleInputChange('heroSecondaryCtaText', e.target.value)}
              placeholder="View Categories"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Secondary CTA Link</label>
            <input
              type="text"
              value={settings.heroSecondaryCtaLink || ''}
              onChange={(e) => handleInputChange('heroSecondaryCtaLink', e.target.value)}
              placeholder="/categories"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Badge Text</label>
            <input
              type="text"
              value={settings.heroBadgeText || ''}
              onChange={(e) => handleInputChange('heroBadgeText', e.target.value)}
              placeholder="New Collection"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>

        <div className="mb-4">
          <ColorPicker
            value={settings.heroBackgroundColor || '#f0fdf4'}
            onChange={(color) => handleInputChange('heroBackgroundColor', color)}
            label="Hero Background Color"
          />
          <p className="text-gray-500 text-xs mt-1">Background color for the hero section</p>
        </div>

        {/* Trust Indicators */}
        <div className="border-t border-gray-200 pt-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Trust Indicators</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Value 1</label>
                <input
                  type="text"
                  value={settings.trustIndicator1Value || ''}
                  onChange={(e) => handleInputChange('trustIndicator1Value', e.target.value)}
                  placeholder="100%"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Label 1</label>
                <input
                  type="text"
                  value={settings.trustIndicator1Label || ''}
                  onChange={(e) => handleInputChange('trustIndicator1Label', e.target.value)}
                  placeholder="Natural"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Value 2</label>
                <input
                  type="text"
                  value={settings.trustIndicator2Value || ''}
                  onChange={(e) => handleInputChange('trustIndicator2Value', e.target.value)}
                  placeholder="500+"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Label 2</label>
                <input
                  type="text"
                  value={settings.trustIndicator2Label || ''}
                  onChange={(e) => handleInputChange('trustIndicator2Label', e.target.value)}
                  placeholder="Happy Customers"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Value 3</label>
                <input
                  type="text"
                  value={settings.trustIndicator3Value || ''}
                  onChange={(e) => handleInputChange('trustIndicator3Value', e.target.value)}
                  placeholder="50+"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Label 3</label>
                <input
                  type="text"
                  value={settings.trustIndicator3Label || ''}
                  onChange={(e) => handleInputChange('trustIndicator3Label', e.target.value)}
                  placeholder="Products"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Value 4</label>
                <input
                  type="text"
                  value={settings.trustIndicator4Value || ''}
                  onChange={(e) => handleInputChange('trustIndicator4Value', e.target.value)}
                  placeholder="4.8★"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Label 4</label>
                <input
                  type="text"
                  value={settings.trustIndicator4Label || ''}
                  onChange={(e) => handleInputChange('trustIndicator4Label', e.target.value)}
                  placeholder="Rating"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Product of the Month</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showProductOfMonth}
              onChange={(e) => handleInputChange('showProductOfMonth', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-600">Show Product of the Month</span>
          </label>
        </div>
        <p className="text-gray-600 text-sm mb-4">Select a featured product to highlight on the homepage. This product will be displayed prominently on the homepage.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search and Select Featured Product
            </label>

            {/* Searchable Product Dropdown */}
            <SearchableProductDropdown
              products={products}
              selectedProductId={settings.productOfTheMonthId}
              onProductSelect={(productId) => handleInputChange('productOfTheMonthId', productId)}
              placeholder="Search and select a product..."
            />
            <p className="text-gray-500 text-xs mt-2">If no product is selected, the system will automatically feature the first product marked as "Featured" in the product settings.</p>
          </div>
          
          {settings.productOfTheMonthId && (
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Selected Product Preview</h3>
              {(() => {
                const selectedProduct = products.find(p => p.id === settings.productOfTheMonthId);
                return selectedProduct ? (
                  <div className="flex items-center space-x-3">
                    {selectedProduct.images?.[0] && (
                      <img
                        src={selectedProduct.images[0].url}
                        alt={selectedProduct.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-gray-900">{selectedProduct.name}</h4>
                      <p className="text-gray-600 text-sm">₹{selectedProduct.price}</p>
                    </div>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>

        {/* Product Section Background Color */}
        <div className="mt-6">
          <ColorPicker
            value={settings.productSectionBgColor || '#f0fdf4'}
            onChange={(color) => handleInputChange('productSectionBgColor', color)}
            label="Product Section Background Color"
          />
          <p className="text-gray-500 text-xs mt-1">Background color for the Product of the Month section</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Promotional Banner</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showBanner}
              onChange={(e) => handleInputChange('showBanner', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-600">Show Promotional Banner</span>
          </label>
        </div>
        <p className="text-gray-600 text-sm mb-4">Create an eye-catching banner to promote special offers, sales, or important announcements.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Banner Text
            </label>
            <input
              type="text"
              value={settings.bannerText || ''}
              onChange={(e) => handleInputChange('bannerText', e.target.value)}
              className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              placeholder="Limited time offer! Get 20% off on all herbal products"
            />
            <p className="text-gray-500 text-xs mt-1">Keep it short and compelling to grab attention</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              CTA Button Text
            </label>
            <input
              type="text"
              value={settings.bannerCtaText || ''}
              onChange={(e) => handleInputChange('bannerCtaText', e.target.value)}
              className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              placeholder="Shop Now"
            />
            <p className="text-gray-500 text-xs mt-1">Action-oriented text like "Shop Now", "Learn More", or "Get Offer"</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              CTA Link
            </label>
            <input
              type="text"
              value={settings.bannerCtaLink || ''}
              onChange={(e) => handleInputChange('bannerCtaLink', e.target.value)}
              className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              placeholder="/shop"
            />
            <p className="text-gray-500 text-xs mt-1">URL where users should be redirected when clicking the CTA</p>
          </div>
          <div>
            <ColorPicker
              value={settings.bannerBackgroundColor || '#22c55e'}
              onChange={(color) => handleInputChange('bannerBackgroundColor', color)}
              label="Banner Background Color"
            />
            <p className="text-gray-500 text-xs mt-1">Choose a color that matches your brand</p>
          </div>
        </div>
        {settings.bannerText && (
          <div
            className="mt-6 p-4 rounded-lg text-white"
            style={{ backgroundColor: settings.bannerBackgroundColor || '#22c55e' }}
          >
            <h3 className="font-bold text-sm mb-1">{settings.bannerText}</h3>
            {settings.bannerCtaText && settings.bannerCtaLink && (
              <a href={settings.bannerCtaLink} className="inline-flex items-center text-xs font-medium underline">
                {settings.bannerCtaText}
                <ArrowRight className="ml-1 w-3 h-3" />
              </a>
            )}
          </div>
        )}
      </div>

      {/* Bestseller Selection */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Bestseller Products</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showBestsellers}
              onChange={(e) => handleInputChange('showBestsellers', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-600">Show Bestsellers Section</span>
          </label>
        </div>
        <p className="text-gray-600 text-sm mb-4">Manually select products to feature as bestsellers. If none are selected, the system will automatically show products with the most reviews.</p>

        <div className="space-y-4">
          {/* Current Bestsellers */}
          {settings.bestsellerIds && settings.bestsellerIds.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Bestsellers ({settings.bestsellerIds.length}/4)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {settings.bestsellerIds.map((productId, index) => {
                  const product = products.find(p => p.id === productId);
                  return product ? (
                    <div key={productId} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {product.images?.[0] && (
                          <img
                            src={product.images[0].url}
                            alt={product.name}
                            className="w-12 h-12 object-cover rounded-lg"
                          />
                        )}
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm">{product.name}</h4>
                          <p className="text-gray-600 text-xs">₹{product.price}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => {
                          const newBestsellerIds = settings.bestsellerIds.filter(id => id !== productId);
                          handleInputChange('bestsellerIds', newBestsellerIds);
                        }}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Add New Bestseller */}
          {(!settings.bestsellerIds || settings.bestsellerIds.length < 4) && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Add Bestseller Product</h3>
              <SearchableProductDropdown
                products={products.filter(p => !settings.bestsellerIds?.includes(p.id))}
                selectedProductId=""
                onProductSelect={(productId) => {
                  if (productId) {
                    const newBestsellerIds = [...(settings.bestsellerIds || []), productId];
                    handleInputChange('bestsellerIds', newBestsellerIds);
                  }
                }}
                placeholder="Search and select a bestseller product..."
              />
            </div>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Settings</h2>
        <p className="text-gray-600 text-sm mb-4">Control the visibility and activation of homepage features</p>
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="isActive"
              type="checkbox"
              checked={settings.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="isActive" className="font-medium text-gray-700">
              Enable Custom Homepage Settings
            </label>
            <p className="text-gray-500">
              When enabled, your custom settings will be displayed on the homepage. When disabled, default content will be shown.
            </p>
          </div>
        </div>
      </div>

      {/* Newsletter Settings */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Newsletter Signup</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showNewsletter}
              onChange={(e) => handleInputChange('showNewsletter', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-600">Show Newsletter Section</span>
          </label>
        </div>
        <p className="text-gray-600 text-sm mb-4">Customize the newsletter signup section that appears on the homepage</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Newsletter Title
            </label>
            <input
              type="text"
              value={settings.newsletterTitle || ''}
              onChange={(e) => handleInputChange('newsletterTitle', e.target.value)}
              className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              placeholder="Stay Updated"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Newsletter Subtitle
            </label>
            <input
              type="text"
              value={settings.newsletterSubtitle || ''}
              onChange={(e) => handleInputChange('newsletterSubtitle', e.target.value)}
              className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              placeholder="Get the latest updates on new products and exclusive offers"
            />
          </div>
        </div>
      </div>

      {/* Section Visibility Settings */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Section Visibility</h2>
        <p className="text-gray-600 text-sm mb-4">Control which sections appear on your homepage</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <input
              type="checkbox"
              checked={settings.showCategories}
              onChange={(e) => handleInputChange('showCategories', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-3 text-sm font-medium text-gray-700">Show Categories Section</span>
          </label>

          <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <input
              type="checkbox"
              checked={settings.showTrustBadges}
              onChange={(e) => handleInputChange('showTrustBadges', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-3 text-sm font-medium text-gray-700">Show Trust Badges Section</span>
          </label>
        </div>
      </div>

      {/* Flash Sale Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Flash Sale Section</h2>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={settings.showFlashSale}
              onChange={(e) => handleInputChange('showFlashSale', e.target.checked)}
              className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm font-medium text-gray-700">Show Flash Sale</span>
          </label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Flash Sale Title
            </label>
            <input
              type="text"
              value={settings.flashSaleTitle || ''}
              onChange={(e) => handleInputChange('flashSaleTitle', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Weekend Flash Sale"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Flash Sale Subtitle
            </label>
            <input
              type="text"
              value={settings.flashSaleSubtitle || ''}
              onChange={(e) => handleInputChange('flashSaleSubtitle', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Get 25% off all natural skincare products"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date & Time
            </label>
            <input
              type="datetime-local"
              value={settings.flashSaleEndDate || ''}
              onChange={(e) => handleInputChange('flashSaleEndDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Background Color
            </label>
            <ColorPicker
              value={settings.flashSaleBackgroundColor || '#16a34a'}
              onChange={(color) => handleInputChange('flashSaleBackgroundColor', color)}
            />
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Testimonials Section</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowTestimonialsModal(true)}
              className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Manage Testimonials
            </button>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.showTestimonials}
                onChange={(e) => handleInputChange('showTestimonials', e.target.checked)}
                className="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
              />
              <span className="ml-2 text-sm font-medium text-gray-700">Show Testimonials</span>
            </label>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Title
            </label>
            <input
              type="text"
              value={settings.testimonialsTitle || ''}
              onChange={(e) => handleInputChange('testimonialsTitle', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="What Our Customers Say"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Subtitle
            </label>
            <input
              type="text"
              value={settings.testimonialsSubtitle || ''}
              onChange={(e) => handleInputChange('testimonialsSubtitle', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Real reviews from real customers who love our natural skincare"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Background Color
            </label>
            <ColorPicker
              value={settings.testimonialsBackgroundColor || '#f0fdf4'}
              onChange={(color) => handleInputChange('testimonialsBackgroundColor', color)}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8 pt-6 border-t border-gray-200">
        <div className="flex items-center">
          {notification && (
            <div className={`mr-4 px-4 py-2 rounded-md text-sm ${
              notification.type === 'success'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {notification.message}
            </div>
          )}
          {settings.id && (
            <span className="text-sm text-gray-500 mr-4">
              Last saved: {new Date(settings.updatedAt).toLocaleString()}
            </span>
          )}
          <button
            type="button"
            onClick={handleSave}
            disabled={saving}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving Changes...
              </>
            ) : (
              <>
                <Check className="h-5 w-5 mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Testimonials Modal */}
      <TestimonialsModal
        isOpen={showTestimonialsModal}
        onClose={() => setShowTestimonialsModal(false)}
        onSave={() => {
          // Refresh testimonials data if needed
          setShowTestimonialsModal(false);
        }}
      />
    </div>
  );
}