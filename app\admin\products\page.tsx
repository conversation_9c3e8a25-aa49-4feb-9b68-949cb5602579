'use client';

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MoreVertical,
  Filter,
  Star,
  Eye,
  Package,
  Loader2,
  FileText,
  HelpCircle,
  Settings,
  Download,
  Upload,
  CheckSquare,
  Square,
  X
} from 'lucide-react';
import { formatPrice, formatStock, getStockColor, generateSlug, validateSlug } from '../../lib/currency';
import ImageSelector from '../../components/admin/ImageSelector';
import FAQManagementModal from '../../components/admin/FAQManagementModal';
import VariationsManagementModal from '../../components/admin/VariationsManagementModal';
import ImageGalleryManager from '../../components/admin/ImageGalleryManager';
import CategoryMultiSelect from '../../components/admin/CategoryMultiSelect';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  comparePrice?: number;
  isFeatured: boolean;
  isActive: boolean;
  category: {
    id: string;
    name: string;
  };
  productCategories?: Array<{
    category: {
      id: string;
      name: string;
    };
  }>;
  images: Array<{
    id: string;
    url: string;
    alt: string;
    position?: number;
  }>;
  variants?: ProductVariation[];
  faqs?: ProductFAQ[];
  createdAt: string;
  updatedAt: string;
}

interface ProductVariation {
  id: string;
  name: string;
  value: string;
  price?: number;
}

interface ProductFAQ {
  id: string;
  question: string;
  answer: string;
  position: number;
  isActive: boolean;
}

const ProductsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [importing, setImporting] = useState(false);
  const [bulkLoading, setBulkLoading] = useState(false);

  const [showFAQModal, setShowFAQModal] = useState(false);
  const [selectedProductForFAQ, setSelectedProductForFAQ] = useState<Product | null>(null);
  const [showVariationsModal, setShowVariationsModal] = useState(false);
  const [selectedProductForVariations, setSelectedProductForVariations] = useState<Product | null>(null);

  // Fetch products and categories
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products?limit=1000');
      const data = await response.json();

      if (data.success) {
        setProducts(data.data);
      } else {
        setError('Failed to fetch products');
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching products:', error);
      }
      setError('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();

      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching categories:', error);
      }
    }
  };

  // CSV Template Download
  const handleDownloadTemplate = () => {
    const csvContent = [
      'Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Variations',
      'Sample Product,sample-product,"A great product description","Short description",99.99,149.99,Skincare,"Skincare;Face Care",yes,yes,"[{""name"":""Size"",""value"":""50ml"",""price"":99.99}]"',
      '# Instructions:',
      '# - Name: Required product name',
      '# - Slug: SEO-friendly URL (auto-generated if empty)',
      '# - Price: Required base price in rupees',
      '# - Category: Single category name',
      '# - Categories: Multiple categories separated by semicolons',
      '# - Featured: yes/no',
      '# - Active: yes/no (defaults to yes)',
      '# - Variations: JSON array of variations with name, value, and price'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'products_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // JSON Template Download
  const handleDownloadJSONTemplate = () => {
    const jsonTemplate = {
      products: [
        {
          name: "Sample Product",
          slug: "sample-product",
          description: "A detailed product description",
          shortDescription: "Short description",
          price: 99.99,
          comparePrice: 149.99,
          categoryNames: ["Skincare", "Face Care"],
          isFeatured: true,
          isActive: true,
          variations: [
            {
              name: "Size",
              value: "50ml",
              price: 99.99
            },
            {
              name: "Size",
              value: "100ml",
              price: 179.99
            }
          ]
        }
      ]
    };

    const blob = new Blob([JSON.stringify(jsonTemplate, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'products_template.json';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Import functionality
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        setImporting(true);
        const text = await file.text();
        let importData = [];

        if (file.name.toLowerCase().endsWith('.json')) {
          // Handle JSON import
          try {
            const jsonData = JSON.parse(text);
            
            // Check if it's the expected format with products array
            if (jsonData.products && Array.isArray(jsonData.products)) {
              importData = jsonData.products;
            } else if (Array.isArray(jsonData)) {
              // Handle direct array of products
              importData = jsonData;
            } else {
              alert('Invalid JSON format. Expected format: {"products": [...]} or direct array of products.');
              return;
            }

            // Validate each product in JSON
            const validProducts = [];
            for (const product of importData) {
              if (!product.name) {
                if (process.env.NODE_ENV === 'development') {
                  console.warn('Skipping product without name:', product);
                }
                continue;
              }

              // Handle both single category and multiple categories
              const categoryNames = [];
              if (product.category) {
                categoryNames.push(product.category);
              }
              if (product.categoryNames && Array.isArray(product.categoryNames)) {
                categoryNames.push(...product.categoryNames);
              }

              if (categoryNames.length === 0) {
                if (process.env.NODE_ENV === 'development') {
                  console.warn('Skipping product without categories:', product.name);
                }
                continue;
              }

              // Check if price is valid or if variations provide pricing
              const hasValidPrice = (product.price && product.price > 0) ||
                                   (product.variations && product.variations.length > 0);

              if (!hasValidPrice) {
                if (process.env.NODE_ENV === 'development') {
                  console.warn('Skipping product without valid price or variations:', product.name);
                }
                continue;
              }

              validProducts.push({
                ...product,
                categoryNames,
                slug: product.slug || generateSlug(product.name),
                isFeatured: Boolean(product.isFeatured),
                isActive: product.isActive !== false, // Default to true
              });
            }

            importData = validProducts;

          } catch (jsonError) {
            alert('Invalid JSON file. Please check the file format.');
            return;
          }

        } else {
          // Handle CSV import
          const lines = text.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));
          const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

          if (process.env.NODE_ENV === 'development') {
            console.log('CSV Headers found:', headers);
          }

          // Validate headers
          const requiredHeaders = ['Name'];
          const missingHeaders = requiredHeaders.filter(header =>
            !headers.some(h => h.toLowerCase().includes(header.toLowerCase()))
          );

          if (missingHeaders.length > 0) {
            alert(`Missing required columns: ${missingHeaders.join(', ')}`);
            return;
          }

          // Check if we have either Category or Categories column
          const hasCategoryColumn = headers.some(h => h.toLowerCase().includes('category'));
          if (!hasCategoryColumn) {
            alert('Missing category information. Please include either "Category" or "Categories" column.');
            return;
          }

          for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
            
            // Find header indices more robustly
            const nameIndex = headers.findIndex(h => h.toLowerCase().includes('name'));
            const slugIndex = headers.findIndex(h => h.toLowerCase().includes('slug'));
            const variationsIndex = headers.findIndex(h => h.toLowerCase().includes('variations'));
            
            const productName = nameIndex >= 0 ? values[nameIndex] || '' : '';
            const productSlug = slugIndex >= 0 ? values[slugIndex] || '' : '';
            const variationsStr = variationsIndex >= 0 ? values[variationsIndex] || '' : '';

            // Parse variations from JSON string
            let variations = [];
            if (variationsStr) {
              try {
                variations = JSON.parse(variationsStr);
              } catch (e) {
                if (process.env.NODE_ENV === 'development') {
                  console.warn('Invalid variations JSON:', variationsStr);
                }
              }
            }

            // Parse categories
            const categoryNames: string[] = [];

            // Check for multiple categories in "Categories" column
            const categoriesIndex = headers.findIndex(h => h.toLowerCase() === 'categories');
            const categoriesValue = categoriesIndex >= 0 ? values[categoriesIndex] || '' : '';
            if (categoriesValue) {
              // Split by semicolon and clean up
              categoryNames.push(...categoriesValue.split(';').map((cat: string) => cat.trim()).filter((cat: string) => cat));
            }

            // Fallback to single category column if no multiple categories
            if (categoryNames.length === 0) {
              const categoryIndex = headers.findIndex(h => h.toLowerCase() === 'category');
              const singleCategory = categoryIndex >= 0 ? values[categoryIndex] || '' : '';
              if (singleCategory) {
                categoryNames.push(singleCategory);
              }
            }

            const priceIndex = headers.findIndex(h => h.toLowerCase() === 'price');
            const priceStr = priceIndex >= 0 ? values[priceIndex] || '' : '';
            const price = parseFloat(priceStr) || 0;
            
            // Check if price is valid or if variations provide pricing
            const hasValidPrice = price >= 0 || (variations && variations.length > 0);
            
            // Find remaining field indices
            const descriptionIndex = headers.findIndex(h => h.toLowerCase() === 'description');
            const shortDescriptionIndex = headers.findIndex(h => h.toLowerCase().includes('short description'));
            const comparePriceIndex = headers.findIndex(h => h.toLowerCase().includes('compare price'));
            const featuredIndex = headers.findIndex(h => h.toLowerCase() === 'featured');
            const activeIndex = headers.findIndex(h => h.toLowerCase() === 'active');

            const product = {
              name: productName,
              slug: validateSlug(productSlug, productName),
              description: descriptionIndex >= 0 ? values[descriptionIndex] || '' : '',
              shortDescription: shortDescriptionIndex >= 0 ? values[shortDescriptionIndex] || '' : '',
              price: price,
              comparePrice: comparePriceIndex >= 0 ? parseFloat(values[comparePriceIndex] || '0') || null : null,
              categoryNames: categoryNames,
              isFeatured: featuredIndex >= 0 ? values[featuredIndex]?.toLowerCase() === 'yes' : false,
              isActive: activeIndex >= 0 ? values[activeIndex]?.toLowerCase() !== 'no' : true,
              variations: variations
            };

            // Allow products with name, category, and valid price
            const hasValidCategory = categoryNames.length > 0;
            
            if (product.name && hasValidCategory && hasValidPrice) {
              importData.push(product);
            }
          }
        }

        if (importData.length === 0) {
          alert('No valid products found in the file. Please ensure:\n\n1. Each product has a Name\n2. Each product has at least one Category\n3. Price must be provided OR variations must have prices\n\nFor JSON: Use format {"products": [...]} or direct array\nFor CSV: Check the CSV template for the correct format.');
          return;
        }

        // Send import data to API
        const response = await fetch('/api/products/import', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            products: importData,
          }),
        });

        const result = await response.json();

        if (result.success) {
          const { success, failed, errors } = result.data;
          let message = `Import completed!\n✅ ${success} products imported successfully`;

          if (failed > 0) {
            message += `\n❌ ${failed} products failed`;
            if (errors.length > 0) {
              message += `\n\nErrors:\n${errors.slice(0, 5).join('\n')}`;
              if (errors.length > 5) {
                message += `\n... and ${errors.length - 5} more errors`;
              }
            }
          }

          alert(message);

          // Refresh the products list
          if (success > 0) {
            fetchProducts();
          }
        } else {
          alert(`Import failed: ${result.error}`);
        }

      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Import error:', error);
        }
        alert('Failed to import products. Please check the file format.');
      } finally {
        setImporting(false);
      }
    };
    input.click();
  };

  // Fixed filteredProducts function
  const filteredProducts = products.filter(product => {
    if (!searchTerm) return true;
    
    const nameMatch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const primaryCategoryMatch = product.category?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const multipleCategoriesMatch = product.productCategories?.some((pc: any) =>
      pc.category.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return nameMatch || primaryCategoryMatch || multipleCategoriesMatch;
  });

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    setSelectedProducts(
      selectedProducts.length === filteredProducts.length
        ? []
        : filteredProducts.map(p => p.id)
    );
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowEditModal(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        const response = await fetch(`/api/products/${productId}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (response.ok) {
          if (result.type === 'soft_delete') {
            alert(`Product deactivated: ${result.message}`);
          } else {
            alert('Product deleted successfully');
          }
          setProducts(prev => prev.filter(p => p.id !== productId));
        } else {
          alert(`Failed to delete product: ${result.error || 'Unknown error'}`);
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error deleting product:', error);
        }
        alert('Failed to delete product');
      }
    }
  };

  // Bulk operations
  const handleBulkAction = async (action: string) => {
    if (selectedProducts.length === 0) {
      alert('Please select products first');
      return;
    }

    const actionMessages = {
      delete: 'Are you sure you want to delete the selected products? This action cannot be undone.',
      activate: 'Are you sure you want to activate the selected products?',
      deactivate: 'Are you sure you want to deactivate the selected products?',
      feature: 'Are you sure you want to feature the selected products?',
      unfeature: 'Are you sure you want to unfeature the selected products?'
    };

    if (!confirm(actionMessages[action as keyof typeof actionMessages])) {
      return;
    }

    try {
      setBulkLoading(true);
      const response = await fetch('/api/products/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          productIds: selectedProducts,
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(result.message);
        setSelectedProducts([]);
        fetchProducts(); // Refresh the products list
      } else {
        alert(`Failed to ${action} products: ${result.error}`);
      }
    } catch (error) {
      console.error(`Error in bulk ${action}:`, error);
      alert(`Failed to ${action} products`);
    } finally {
      setBulkLoading(false);
    }
  };

  const handleManageFAQs = (product: Product) => {
    setSelectedProductForFAQ(product);
    setShowFAQModal(true);
  };

  const handleManageVariations = (product: Product) => {
    setSelectedProductForVariations(product);
    setShowVariationsModal(true);
  };

  const toggleFeatured = async (productId: string) => {
    try {
      const product = products.find(p => p.id === productId);
      if (!product) return;

      const response = await fetch(`/api/products/${productId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isFeatured: !product.isFeatured,
        }),
      });

      if (response.ok) {
        setProducts(prev => prev.map(p =>
          p.id === productId ? { ...p, isFeatured: !p.isFeatured } : p
        ));
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating product:', error);
      }
    }
  };

  const getCategoryColor = (categoryName: string) => {
    const colors: { [key: string]: string } = {
      'Skincare': 'bg-green-100 text-green-800',
      'Hair Care': 'bg-purple-100 text-purple-800',
      'Body Care': 'bg-blue-100 text-blue-800',
      'cleanser': 'bg-blue-100 text-blue-800',
      'serum': 'bg-purple-100 text-purple-800',
      'moisturizer': 'bg-green-100 text-green-800',
      'mask': 'bg-yellow-100 text-yellow-800',
      'exfoliator': 'bg-pink-100 text-pink-800',
      'eye-care': 'bg-indigo-100 text-indigo-800',
    };
    return colors[categoryName] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Products</h1>
            <p className="text-gray-600 mt-2">Manage your skincare product catalog</p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add Product
            </button>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-green-600" />
          <span className="ml-2 text-gray-600">Loading products...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-600">{error}</p>
          <button
            onClick={fetchProducts}
            className="mt-2 text-red-600 hover:text-red-700 underline"
          >
            Try again
          </button>
        </div>
      )}

      {/* Filters and Search */}
      {!loading && !error && (
      <>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-5 h-5 text-gray-600" />
            </button>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <button
                onClick={handleDownloadTemplate}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="Download CSV template"
              >
                <FileText className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={handleDownloadJSONTemplate}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium text-gray-600"
                title="Download JSON template"
              >
                JSON
              </button>
            </div>
            
            <button
              onClick={handleImport}
              disabled={importing}
              className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              title="Import products from CSV/JSON"
            >
              {importing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              {importing ? 'Importing...' : 'Import'}
            </button>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {selectedProducts.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedProducts([])}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                Clear selection
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkAction('activate')}
                disabled={bulkLoading}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50"
              >
                Activate
              </button>
              <button
                onClick={() => handleBulkAction('deactivate')}
                disabled={bulkLoading}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 disabled:opacity-50"
              >
                Deactivate
              </button>
              <button
                onClick={() => handleBulkAction('feature')}
                disabled={bulkLoading}
                className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50"
              >
                Feature
              </button>
              <button
                onClick={() => handleBulkAction('unfeature')}
                disabled={bulkLoading}
                className="px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 disabled:opacity-50"
              >
                Unfeature
              </button>
              <button
                onClick={() => handleBulkAction('delete')}
                disabled={bulkLoading}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 flex items-center"
              >
                {bulkLoading ? (
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-1" />
                )}
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={() => handleSelectProduct(product.id)}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <img
                        src={product.images[0]?.url || '/images/default-product.jpg'}
                        alt={product.images[0]?.alt || product.name}
                        className="w-12 h-12 rounded-lg object-cover mr-4"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.shortDescription}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {/* Show categories from many-to-many relationship first */}
                      {product.productCategories && product.productCategories.length > 0 ? (
                        product.productCategories.map((pc: any) => (
                          <span key={pc.category.id} className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(pc.category.name)}`}>
                            {pc.category.name}
                          </span>
                        ))
                      ) : (
                        /* Fallback to primary category if no many-to-many categories */
                        product.category && (
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(product.category.name)}`}>
                            {product.category.name}
                          </span>
                        )
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {product.variants && product.variants.length > 0 ? (
                      (() => {
                        const prices = product.variants.map(v => v.price).filter((p): p is number => typeof p === 'number' && p > 0);
                        if (prices.length === 0) return 'No pricing';
                        const min = Math.min(...prices);
                        const max = Math.max(...prices);
                        return min === max ? formatPrice(min) : `${formatPrice(min)} - ${formatPrice(max)}`;
                      })()
                    ) : (
                      <span className="text-gray-500">No variations</span>
                    )}
                    {product.comparePrice && (
                      <span className="text-xs text-gray-500 line-through ml-2">
                        {formatPrice(product.comparePrice)}
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {product.variants && product.variants.length > 0 ? (
                        <span className="text-blue-600">{product.variants.length} variations</span>
                      ) : (
                        <span className="text-gray-500">No variations</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      product.isFeatured ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {product.isFeatured ? 'Featured' : 'Active'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Product"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleManageFAQs(product)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="Manage FAQs"
                      >
                        <HelpCircle className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleManageVariations(product)}
                        className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                        title="Manage Variations"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Product"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Stats */}
      <div className="mt-6 text-sm text-gray-500">
        Showing {filteredProducts.length} of {products.length} products
      </div>
      </>
      )}

      {/* Add Product Modal */}
      {showAddModal && (
        <ProductModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSave={fetchProducts}
          categories={categories}
        />
      )}

      {/* Edit Product Modal */}
      {showEditModal && editingProduct && (
        <ProductModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingProduct(null);
          }}
          onSave={fetchProducts}
          categories={categories}
          product={editingProduct}
        />
      )}

      {/* FAQ Management Modal */}
      {showFAQModal && selectedProductForFAQ && (
        <FAQManagementModal
          isOpen={showFAQModal}
          onClose={() => {
            setShowFAQModal(false);
            setSelectedProductForFAQ(null);
          }}
          product={selectedProductForFAQ}
        />
      )}

      {/* Variations Management Modal */}
      {showVariationsModal && selectedProductForVariations && (
        <VariationsManagementModal
          isOpen={showVariationsModal}
          onClose={() => {
            setShowVariationsModal(false);
            setSelectedProductForVariations(null);
          }}
          product={selectedProductForVariations}
        />
      )}
    </div>
  );
};

// Product Modal Component
interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  categories: {id: string, name: string}[];
  product?: Product;
}

const ProductModal: React.FC<ProductModalProps> = ({ isOpen, onClose, onSave, categories, product }) => {
  const [formData, setFormData] = useState({
    name: product?.name || '',
    slug: product?.slug || '',
    description: product?.description || '',
    shortDescription: product?.shortDescription || '',
    comparePrice: product?.comparePrice?.toString() || '',
    categoryId: product?.category?.id || '',
    isFeatured: product?.isFeatured || false,
  });

  // Initialize selected categories from product data
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>(() => {
    if (product?.productCategories && product.productCategories.length > 0) {
      return product.productCategories.map((pc: any) => pc.category.id);
    } else if (product?.category?.id) {
      return [product.category.id];
    }
    return [];
  });

  const [productImages, setProductImages] = useState<Array<{
    id: string;
    url: string;
    alt: string;
    position: number;
  }>>(
    product?.images?.map(img => ({
      id: img.id || '',
      url: img.url,
      alt: img.alt || '',
      position: img.position || 0
    })) || []
  );

  const [variations, setVariations] = useState<Array<{
    id?: string;
    name: string;
    value: string;
    price: string;
  }>>(product?.variants?.map(v => ({
    id: v.id,
    name: v.name,
    value: v.value,
    price: v.price?.toString() || '0'
  })) || []);

  const [showVariations, setShowVariations] = useState(false);
  const [loading, setLoading] = useState(false);

  // Initialize variations when product changes
  useEffect(() => {
    if (product?.variants) {
      setVariations(product.variants.map(v => ({
        id: v.id,
        name: v.name,
        value: v.value,
        price: v.price?.toString() || '0'
      })));
    } else {
      setVariations([]);
    }
  }, [product]);

  const addVariation = () => {
    setVariations([...variations, {
      name: '',
      value: '',
      price: '0'
    }]);
  };

  const updateVariation = (index: number, field: string, value: string) => {
    const updatedVariations = [...variations];
    updatedVariations[index] = { ...updatedVariations[index], [field]: value };
    setVariations(updatedVariations);
  };

  const removeVariation = (index: number) => {
    setVariations(variations.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const url = product ? `/api/products/${product.id}` : '/api/products';
      const method = product ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          slug: formData.slug || generateSlug(formData.name),
          comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : null,
          categoryIds: selectedCategoryIds, // Send multiple category IDs
          images: productImages.map((img, index) => ({
            url: img.url,
            alt: img.alt || formData.name,
            position: index
          })),
          variations: variations.filter(v => v.name && v.value).map(v => ({
            name: v.name,
            value: v.value,
            price: v.price ? parseFloat(v.price) : 0,
          })),
        }),
      });

      const result = await response.json();
      if (response.ok) {
        if (result.warnings && result.warnings.length > 0) {
          alert(`Product saved successfully!\n\nWarnings:\n${result.warnings.join('\n')}`);
        }
        onSave();
        onClose();
      } else {
        alert(`Failed to save product: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error saving product:', error);
      }
      alert('Failed to save product');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {product ? 'Edit Product' : 'Add Product'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => {
                  const newName = e.target.value;
                  setFormData({
                    ...formData,
                    name: newName,
                    // Auto-generate slug if slug field is empty
                    slug: formData.slug || generateSlug(newName)
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                URL Slug
                <span className="text-xs text-gray-500 ml-1">(SEO-friendly URL)</span>
              </label>
              <input
                type="text"
                value={formData.slug}
                onChange={(e) => setFormData({ ...formData, slug: generateSlug(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="auto-generated-from-name"
              />
              <div className="text-xs text-gray-500 mt-1">
                Preview: /products/{formData.slug || generateSlug(formData.name)}
              </div>
            </div>

            <div>
              <CategoryMultiSelect
                categories={categories}
                selectedCategoryIds={selectedCategoryIds}
                onChange={setSelectedCategoryIds}
                label="Categories"
                placeholder="Select categories..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Compare Price (₹)
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.comparePrice}
                onChange={(e) => setFormData({ ...formData, comparePrice: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Enter compare price in Rupees"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Short Description
            </label>
            <input
              type="text"
              value={formData.shortDescription}
              onChange={(e) => setFormData({ ...formData, shortDescription: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          <ImageGalleryManager
            images={productImages}
            onChange={(images) => setProductImages(images.map(img => ({
              id: img.id || '',
              url: img.url,
              alt: img.alt || '',
              position: img.position || 0
            })))}
            productName={formData.name || 'Product'}
          />

          {/* Product Variations Section */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Product Variations</h3>
              <button
                type="button"
                onClick={() => setShowVariations(!showVariations)}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                {showVariations ? 'Hide Variations' : 'Add Variations'}
              </button>
            </div>

            {showVariations && (
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-3">
                  Add variations like Size, Color, Material, etc. Each variation can have its own price adjustment and stock.
                </div>

                {variations.map((variation, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Name (e.g., Size)
                      </label>
                      <input
                        type="text"
                        value={variation.name}
                        onChange={(e) => updateVariation(index, 'name', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Size"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Value (e.g., Large)
                      </label>
                      <input
                        type="text"
                        value={variation.value}
                        onChange={(e) => updateVariation(index, 'value', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Large"
                      />
                    </div>
                    <div className="flex items-end space-x-2">
                      <div className="flex-1">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Price Adjustment (₹)
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={variation.price}
                          onChange={(e) => updateVariation(index, 'price', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeVariation(index)}
                        className="px-2 py-1 text-red-600 hover:text-red-700 text-sm"
                        title="Remove variation"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addVariation}
                  className="w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
                >
                  + Add Variation
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="featured"
              checked={formData.isFeatured}
              onChange={(e) => setFormData({ ...formData, isFeatured: e.target.checked })}
              className="rounded border-gray-300 text-green-600 focus:ring-green-500"
            />
            <label htmlFor="featured" className="ml-2 text-sm text-gray-700">
              Featured Product
            </label>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Saving...' : (product ? 'Update' : 'Create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductsPage;
