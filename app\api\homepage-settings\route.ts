import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

// GET /api/homepage-settings - Get homepage settings
export async function GET() {
  try {
    // Get homepage settings
    const settings = await prisma.homepageSetting.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get featured product (Product of the Month)
    // First check if there's a specific product set in homepage settings
    let featuredProduct = null;
    
    if (settings.length > 0 && settings[0].productOfTheMonthId) {
      featuredProduct = await prisma.product.findUnique({
        where: {
          id: settings[0].productOfTheMonthId,
          isActive: true,
        },
        include: {
          images: true,
          category: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
      });
    }
    
    // If no specific product is set, fall back to the first featured product
    if (!featuredProduct) {
      featuredProduct = await prisma.product.findFirst({
        where: {
          isFeatured: true,
          isActive: true,
        },
        include: {
          images: true,
          category: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
      });
    }

    // Get bestsellers - either manually selected or auto-selected
    let bestsellers = [];

    if (settings.length > 0 && settings[0].bestsellerIds && settings[0].bestsellerIds.length > 0) {
      // Get manually selected bestsellers
      const bestsellerProducts = await Promise.all(
        settings[0].bestsellerIds.map(async (id: string) => {
          return await prisma.product.findUnique({
            where: {
              id,
              isActive: true,
            },
            include: {
              images: true,
              category: true,
              _count: {
                select: {
                  reviews: true,
                },
              },
            },
          });
        })
      );

      // Filter out null results and maintain order
      bestsellers = bestsellerProducts.filter(Boolean);
    } else {
      // Auto-select bestsellers (top 4 products by sales/reviews)
      bestsellers = await prisma.product.findMany({
        where: {
          isActive: true,
        },
        include: {
          images: true,
          category: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
        orderBy: {
          reviews: {
            _count: 'desc',
          },
        },
        take: 4,
      });
    }

    // Get categories for showcase
    const categories = await prisma.category.findMany({
      where: {
        isActive: true,
      },
      include: {
        _count: {
          select: {
            products: {
              where: {
                isActive: true,
              },
            },
          },
        },
      },
      take: 6,
    });

    return NextResponse.json({
      success: true,
      data: {
        settings: settings.length > 0 ? settings[0] : null,
        featuredProduct,
        bestsellers,
        categories,
      },
    });
  } catch (error) {
    console.error('Error fetching homepage settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch homepage settings' },
      { status: 500 }
    );
  }
}

// POST /api/homepage-settings - Create or update homepage settings
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      // Hero Section
      heroTitle,
      heroSubtitle,
      heroCtaText,
      heroCtaLink,
      heroBackgroundColor,
      showHero,

      // Product of the Month
      productOfTheMonthId,
      showProductOfMonth,

      // Promotional Banner
      bannerText,
      bannerCtaText,
      bannerCtaLink,
      bannerBackgroundColor,
      showBanner,

      // Sections
      showCategories,
      productSectionBgColor,
      bestsellerIds,
      showBestsellers,

      // Newsletter
      newsletterTitle,
      newsletterSubtitle,
      showNewsletter,

      // Trust Badges
      showTrustBadges,

      // Flash Sale Section
      flashSaleTitle,
      flashSaleSubtitle,
      flashSaleEndDate,
      flashSaleBackgroundColor,
      showFlashSale,

      // Testimonials Section
      testimonialsTitle,
      testimonialsSubtitle,
      testimonialsBackgroundColor,
      showTestimonials,

      isActive
    } = body;

    // Create or update homepage settings
    const settings = await prisma.homepageSetting.upsert({
      where: {
        id: 'homepage-settings',
      },
      update: {
        // Hero Section
        ...(heroTitle !== undefined && { heroTitle }),
        ...(heroSubtitle !== undefined && { heroSubtitle }),
        ...(heroCtaText !== undefined && { heroCtaText }),
        ...(heroCtaLink !== undefined && { heroCtaLink }),
        ...(heroBackgroundColor !== undefined && { heroBackgroundColor }),
        ...(showHero !== undefined && { showHero }),

        // Product of the Month
        ...(productOfTheMonthId !== undefined && { productOfTheMonthId }),
        ...(showProductOfMonth !== undefined && { showProductOfMonth }),

        // Promotional Banner
        ...(bannerText !== undefined && { bannerText }),
        ...(bannerCtaText !== undefined && { bannerCtaText }),
        ...(bannerCtaLink !== undefined && { bannerCtaLink }),
        ...(bannerBackgroundColor !== undefined && { bannerBackgroundColor }),
        ...(showBanner !== undefined && { showBanner }),

        // Sections
        ...(showCategories !== undefined && { showCategories }),
        ...(productSectionBgColor !== undefined && { productSectionBgColor }),
        ...(bestsellerIds !== undefined && { bestsellerIds }),
        ...(showBestsellers !== undefined && { showBestsellers }),

        // Newsletter
        ...(newsletterTitle !== undefined && { newsletterTitle }),
        ...(newsletterSubtitle !== undefined && { newsletterSubtitle }),
        ...(showNewsletter !== undefined && { showNewsletter }),

        // Trust Badges
        ...(showTrustBadges !== undefined && { showTrustBadges }),

        // Flash Sale Section
        ...(flashSaleTitle !== undefined && { flashSaleTitle }),
        ...(flashSaleSubtitle !== undefined && { flashSaleSubtitle }),
        ...(flashSaleEndDate !== undefined && { flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null }),
        ...(flashSaleBackgroundColor !== undefined && { flashSaleBackgroundColor }),
        ...(showFlashSale !== undefined && { showFlashSale }),

        // Testimonials Section
        ...(testimonialsTitle !== undefined && { testimonialsTitle }),
        ...(testimonialsSubtitle !== undefined && { testimonialsSubtitle }),
        ...(testimonialsBackgroundColor !== undefined && { testimonialsBackgroundColor }),
        ...(showTestimonials !== undefined && { showTestimonials }),

        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      },
      create: {
        id: 'homepage-settings',
        // Hero Section
        heroTitle: heroTitle || 'Natural Skincare Essentials',
        heroSubtitle: heroSubtitle || 'Discover our botanical collection crafted with nature\'s finest ingredients for radiant, healthy skin',
        heroCtaText: heroCtaText || 'Shop Collection',
        heroCtaLink: heroCtaLink || '/shop',
        heroBackgroundColor: heroBackgroundColor || '#f0fdf4',
        showHero: showHero !== undefined ? showHero : true,

        // Product of the Month
        productOfTheMonthId,
        showProductOfMonth: showProductOfMonth !== undefined ? showProductOfMonth : true,

        // Promotional Banner
        bannerText,
        bannerCtaText,
        bannerCtaLink,
        bannerBackgroundColor: bannerBackgroundColor || '#22c55e',
        showBanner: showBanner !== undefined ? showBanner : true,

        // Sections
        showCategories: showCategories !== undefined ? showCategories : true,
        productSectionBgColor: productSectionBgColor || '#f0fdf4',
        bestsellerIds: bestsellerIds || [],
        showBestsellers: showBestsellers !== undefined ? showBestsellers : true,

        // Newsletter
        newsletterTitle: newsletterTitle || 'Stay Updated',
        newsletterSubtitle: newsletterSubtitle || 'Get the latest updates on new products and exclusive offers',
        showNewsletter: showNewsletter !== undefined ? showNewsletter : true,

        // Trust Badges
        showTrustBadges: showTrustBadges !== undefined ? showTrustBadges : true,

        // Flash Sale Section
        flashSaleTitle: flashSaleTitle || 'Weekend Flash Sale',
        flashSaleSubtitle: flashSaleSubtitle || 'Get 25% off all natural skincare products',
        flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null,
        flashSaleBackgroundColor: flashSaleBackgroundColor || '#16a34a',
        showFlashSale: showFlashSale !== undefined ? showFlashSale : true,

        // Testimonials Section
        testimonialsTitle: testimonialsTitle || 'What Our Customers Say',
        testimonialsSubtitle: testimonialsSubtitle || 'Real reviews from real customers who love our natural skincare',
        testimonialsBackgroundColor: testimonialsBackgroundColor || '#f0fdf4',
        showTestimonials: showTestimonials !== undefined ? showTestimonials : true,

        isActive: isActive !== undefined ? isActive : true,
      },
    });

    return NextResponse.json({
      success: true,
      data: settings,
      message: 'Homepage settings updated successfully',
    });
  } catch (error) {
    console.error('Error updating homepage settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update homepage settings' },
      { status: 500 }
    );
  }
}