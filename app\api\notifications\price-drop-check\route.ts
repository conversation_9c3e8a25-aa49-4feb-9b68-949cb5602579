import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { wishlistNotifications } from '../../../lib/notification-helpers';
import { logger } from '../../../lib/logger';

/**
 * POST /api/notifications/price-drop-check
 * Check for price drops on wishlisted items and send notifications
 * This endpoint can be called by a cron job or admin action
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to trigger price drop checks
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    logger.info('Starting price drop check for all wishlisted items');

    // Get all wishlist items with their current and previous prices
    const wishlistItems = await prisma.wishlistItem.findMany({
      include: {
        product: true,
        user: {
          include: {
            preferences: true,
          },
        },
      },
    });

    let notificationsSent = 0;
    const processedProducts = new Set<string>();

    for (const item of wishlistItems) {
      try {
        // Skip if we've already processed this product
        if (processedProducts.has(item.productId)) {
          continue;
        }
        processedProducts.add(item.productId);

        const product = item.product;
        
        // Check if user has price drop notifications enabled
        const userPrefs = item.user.preferences;
        if (!userPrefs?.priceDropAlerts) {
          continue;
        }

        // For this demo, we'll simulate a price drop by checking if the product
        // has been updated recently and has a lower price than a threshold
        // In a real implementation, you'd store historical prices
        
        // Simple price drop simulation: if price ends with .99 or .49, consider it a sale
        const currentPrice = product.price || 0;
        const isPriceDropped = currentPrice.toString().endsWith('.99') || 
                              currentPrice.toString().endsWith('.49');

        if (isPriceDropped && currentPrice > 0) {
          // Calculate simulated old price (20% higher)
          const oldPrice = Math.round(currentPrice * 1.2 * 100) / 100;
          const discountPercentage = Math.round(((oldPrice - currentPrice) / oldPrice) * 100);

          // Get all users who have this product in their wishlist
          const usersWithProduct = await prisma.wishlistItem.findMany({
            where: {
              productId: product.id,
              user: {
                preferences: {
                  priceDropAlerts: true,
                },
              },
            },
            include: {
              user: true,
            },
          });

          // Send notification to each user
          for (const userItem of usersWithProduct) {
            try {
              await wishlistNotifications.priceDropAlert(userItem.userId, {
                productId: product.id,
                productName: product.name,
                oldPrice,
                newPrice: currentPrice,
                currency: 'INR',
                discountPercentage,
              });

              notificationsSent++;
              
              logger.info('Price drop notification sent', {
                userId: userItem.userId,
                productId: product.id,
                productName: product.name,
                oldPrice,
                newPrice: currentPrice,
                discountPercentage,
              });
            } catch (notificationError) {
              logger.error('Failed to send price drop notification', {
                error: notificationError,
                userId: userItem.userId,
                productId: product.id,
              });
            }
          }
        }
      } catch (itemError) {
        logger.error('Error processing wishlist item for price drop', {
          error: itemError,
          itemId: item.id,
          productId: item.productId,
        });
      }
    }

    logger.info('Price drop check completed', {
      totalItems: wishlistItems.length,
      notificationsSent,
    });

    return NextResponse.json({
      success: true,
      message: 'Price drop check completed',
      stats: {
        totalItems: wishlistItems.length,
        notificationsSent,
      },
    });

  } catch (error) {
    logger.error('Failed to check for price drops', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
