import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { reviewNotifications } from '../../../lib/notification-helpers';
import { logger } from '../../../lib/logger';

/**
 * POST /api/notifications/review-requests
 * Send review request notifications for delivered orders
 * This endpoint can be called by a cron job or admin action
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to trigger review request notifications
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    logger.info('Starting review request notifications for delivered orders');

    // Get delivered orders from the last 7 days that haven't had review requests sent
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const deliveredOrders = await prisma.order.findMany({
      where: {
        status: 'DELIVERED',
        updatedAt: {
          gte: sevenDaysAgo,
        },
        // Only include orders where we haven't sent a review request notification yet
        // We'll track this by checking if a REVIEW_REQUEST notification exists for this order
        NOT: {
          notifications: {
            some: {
              type: 'REVIEW_REQUEST',
            },
          },
        },
      },
      include: {
        user: {
          include: {
            preferences: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    let notificationsSent = 0;

    for (const order of deliveredOrders) {
      try {
        // Check if user has review notifications enabled
        const userPrefs = order.user.preferences;
        if (!userPrefs?.reviewReminders) {
          continue;
        }

        // Check if user has already reviewed any products from this order
        const existingReviews = await prisma.review.findMany({
          where: {
            userId: order.userId,
            productId: {
              in: order.items.map(item => item.productId),
            },
          },
        });

        // Only send notification if user hasn't reviewed any products from this order
        if (existingReviews.length === 0) {
          const productNames = order.items.map(item => item.product.name).join(', ');

          await reviewNotifications.reviewRequest(order.userId, {
            orderId: order.id,
            orderNumber: order.orderNumber,
            productNames,
          });

          notificationsSent++;
          
          logger.info('Review request notification sent', {
            orderId: order.id,
            orderNumber: order.orderNumber,
            userId: order.userId,
            productCount: order.items.length,
          });
        }
      } catch (orderError) {
        logger.error('Error processing order for review request', {
          error: orderError,
          orderId: order.id,
          orderNumber: order.orderNumber,
        });
      }
    }

    logger.info('Review request notifications completed', {
      totalOrders: deliveredOrders.length,
      notificationsSent,
    });

    return NextResponse.json({
      success: true,
      message: 'Review request notifications completed',
      stats: {
        totalOrders: deliveredOrders.length,
        notificationsSent,
      },
    });

  } catch (error) {
    logger.error('Failed to send review request notifications', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * GET /api/notifications/review-requests
 * Get statistics about review request notifications
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to view statistics
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    // Get statistics for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [
      deliveredOrders,
      reviewRequestsSent,
      reviewsSubmitted,
    ] = await Promise.all([
      // Count delivered orders in the last 30 days
      prisma.order.count({
        where: {
          status: 'DELIVERED',
          updatedAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // Count review request notifications sent in the last 30 days
      prisma.notification.count({
        where: {
          type: 'REVIEW_REQUEST',
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // Count reviews submitted in the last 30 days
      prisma.review.count({
        where: {
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
    ]);

    const conversionRate = reviewRequestsSent > 0 
      ? Math.round((reviewsSubmitted / reviewRequestsSent) * 100) 
      : 0;

    return NextResponse.json({
      success: true,
      stats: {
        deliveredOrders,
        reviewRequestsSent,
        reviewsSubmitted,
        conversionRate: `${conversionRate}%`,
        period: 'Last 30 days',
      },
    });

  } catch (error) {
    logger.error('Failed to get review request statistics', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
