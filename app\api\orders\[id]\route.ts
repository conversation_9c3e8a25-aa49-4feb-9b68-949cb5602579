import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { handleApiError, ValidationError, AuthenticationError, AppError, asyncHandler } from '../../../lib/errors';
import { logger } from '../../../lib/logger';
import { withRateLimit, generalLimiter } from '../../../lib/rate-limit';
import { orderNotifications, reviewNotifications } from '../../../lib/notification-helpers';

const updateOrderSchema = z.object({
  status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
  paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).optional(),
  notes: z.string().optional()
});

// GET /api/orders/[id] - Get specific order details
export const GET = asyncHandler(async (request: NextRequest, { params }: { params: { id: string } }) => {
  logger.apiRequest('GET', `/api/orders/${params.id}`);

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 20);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const orderId = params.id;
  const isAdmin = session.user.role === 'ADMIN';

  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                price: true,
                images: {
                  select: {
                    url: true,
                    alt: true
                  },
                  take: 1
                }
              }
            }
          }
        },
        address: true,
        user: isAdmin ? {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        } : false
      }
    });

    if (!order) {
      throw new ValidationError('Order not found');
    }

    // Non-admin users can only access their own orders
    if (!isAdmin && order.userId !== session.user.id) {
      throw new AuthenticationError('Unauthorized access to order');
    }

    logger.info('Order details retrieved', {
      orderId,
      userId: session.user.id,
      isAdmin,
      orderStatus: order.status
    });

    return NextResponse.json({
      success: true,
      order
    });

  } catch (error) {
    logger.error('Failed to retrieve order details', error as Error);
    throw error;
  }
});

// PUT /api/orders/[id] - Update order status (admin only)
export const PUT = asyncHandler(async (request: NextRequest, { params }: { params: { id: string } }) => {
  logger.apiRequest('PUT', `/api/orders/${params.id}`);

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 10);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== 'ADMIN') {
    throw new AuthenticationError('Admin access required');
  }

  const orderId = params.id;
  const body = await request.json();
  const validatedData = updateOrderSchema.parse(body);

  try {
    // Get current order
    const currentOrder = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        },
        address: true
      }
    });

    if (!currentOrder) {
      throw new ValidationError('Order not found');
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (validatedData.status) {
      updateData.status = validatedData.status;
    }
    if (validatedData.paymentStatus) {
      updateData.paymentStatus = validatedData.paymentStatus;
    }
    if (validatedData.notes) {
      updateData.notes = validatedData.notes;
    }

    // Update order
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: updateData,
      include: {
        items: {
          include: {
            product: true
          }
        },
        address: true,
        user: true
      }
    });

    logger.info('Order updated successfully', {
      orderId,
      adminId: session.user.id,
      previousStatus: currentOrder.status,
      newStatus: updatedOrder.status,
      previousPaymentStatus: currentOrder.paymentStatus,
      newPaymentStatus: updatedOrder.paymentStatus
    });

    // Send notifications for status changes
    try {
      if (validatedData.status && validatedData.status !== currentOrder.status) {
        const orderData = {
          orderId: updatedOrder.id,
          orderNumber: updatedOrder.orderNumber,
        };

        // Send appropriate notification based on status
        switch (validatedData.status) {
          case 'PROCESSING':
            await orderNotifications.orderProcessing(currentOrder.userId, orderData);
            break;
          case 'SHIPPED':
            await orderNotifications.orderShipped(currentOrder.userId, orderData);
            break;
          case 'DELIVERED':
            await orderNotifications.orderDelivered(currentOrder.userId, {
              ...orderData,
              deliveredAt: new Date().toLocaleDateString(),
            });

            // Schedule review request notification (send after 3 days)
            setTimeout(async () => {
              try {
                // Check if user has review notifications enabled
                const userPrefs = await prisma.userPreference.findUnique({
                  where: { userId: currentOrder.userId },
                });

                if (userPrefs?.reviewNotifications) {
                  const productIds = currentOrder.items.map(item => item.productId);
                  const productNames = currentOrder.items.map(item => item.product.name);

                  await reviewNotifications.reviewRequest(currentOrder.userId, {
                    orderId: currentOrder.id,
                    orderNumber: currentOrder.orderNumber,
                    productIds,
                    productNames,
                  });

                  logger.info('Review request notification sent after delivery', {
                    orderId: currentOrder.id,
                    userId: currentOrder.userId,
                  });
                }
              } catch (reviewRequestError) {
                logger.error('Failed to send review request notification', reviewRequestError as Error);
              }
            }, 3 * 24 * 60 * 60 * 1000); // 3 days in milliseconds
            break;
          case 'CANCELLED':
            await orderNotifications.orderCancelled(currentOrder.userId, {
              ...orderData,
              reason: validatedData.notes,
            });
            break;
        }

        logger.info('Order status notification sent', {
          orderId,
          userId: currentOrder.userId,
          status: updatedOrder.status
        });
      }
    } catch (notificationError) {
      logger.error('Failed to send order status notification', notificationError as Error);
      // Don't fail the update if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'Order updated successfully',
      order: updatedOrder
    });

  } catch (error) {
    logger.error('Failed to update order', error as Error);
    throw error;
  }
});
