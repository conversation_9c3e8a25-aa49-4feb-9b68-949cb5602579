import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../lib/auth';
import { prisma } from '../../lib/db';
import { handleApiError, ValidationError, AuthenticationError, asyncHandler } from '../../lib/errors';
import { logger } from '../../lib/logger';
import { withRateLimit, generalLimiter } from '../../lib/rate-limit';

const getOrdersSchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
  paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).optional()
});

// GET /api/orders - Get user's orders or all orders (admin)
export const GET = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('GET', '/api/orders');

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 30);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());
  const validatedParams = getOrdersSchema.parse(queryParams);

  const page = parseInt(validatedParams.page);
  const limit = parseInt(validatedParams.limit);
  const offset = (page - 1) * limit;

  const isAdmin = session.user.role === 'ADMIN';

  try {
    // Build where clause
    const whereClause: any = {};
    
    // Non-admin users can only see their own orders
    if (!isAdmin) {
      whereClause.userId = session.user.id;
    }

    // Add status filters if provided
    if (validatedParams.status) {
      whereClause.status = validatedParams.status;
    }
    if (validatedParams.paymentStatus) {
      whereClause.paymentStatus = validatedParams.paymentStatus;
    }

    // Get orders with pagination
    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        where: whereClause,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  price: true
                }
              }
            }
          },
          address: true,
          user: isAdmin ? {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          } : false
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      }),
      prisma.order.count({ where: whereClause })
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info('Orders retrieved successfully', {
      userId: session.user.id,
      isAdmin,
      count: orders.length,
      totalCount,
      page,
      filters: { status: validatedParams.status, paymentStatus: validatedParams.paymentStatus }
    });

    return NextResponse.json({
      success: true,
      orders,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    logger.error('Failed to retrieve orders', error as Error);
    throw error;
  }
});