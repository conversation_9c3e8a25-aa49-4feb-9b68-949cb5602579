import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/db';
import { handleApiError, AuthenticationError, NotFoundError, ConflictError, ValidationError, asyncHandler, PrismaError } from '../../../../lib/errors';
import { logger } from '../../../../lib/logger';
import { reviewNotifications } from '../../../../lib/notification-helpers';

export const GET = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const productId = params.id;
  logger.apiRequest('GET', `/api/products/${productId}/reviews`);
  
  const reviews = await prisma.review.findMany({
    where: {
      productId: productId,
      status: 'APPROVED'
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          avatar: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  logger.info('Product reviews fetched', {
    productId,
    count: reviews.length
  });

  return NextResponse.json({
    success: true,
    data: reviews
  });
});

export const POST = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const productId = params.id;
  logger.apiRequest('POST', `/api/products/${productId}/reviews`);

  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    throw new AuthenticationError();
  }

  const body = await request.json();
  const { rating, title, content } = body;

  // Validate required fields
  if (!rating || rating < 1 || rating > 5) {
    throw new ValidationError('Rating must be between 1 and 5');
  }

  // Check if we have either user ID or email
  if (!session.user.id && !session.user.email) {
    throw new AuthenticationError('Invalid session. Please log out and log back in.');
  }

  // Try to find user by ID first, then by email
  let userExists;
  let userId;

  if (session.user.id) {
    userExists = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, email: true, name: true }
    });
    userId = session.user.id;
  } else if (session.user.email) {
    userExists = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, email: true, name: true }
    });
    userId = userExists?.id;
  }

  if (!userExists || !userId) {
    throw new AuthenticationError('User not found. Please log out and log back in.');
  }

  // Verify the product exists
  const productExists = await prisma.product.findUnique({
    where: { id: productId },
    select: { id: true, name: true }
  });

  if (!productExists) {
    throw new NotFoundError('Product');
  }

  // Check if user has already reviewed this product
  const existingReview = await prisma.review.findUnique({
    where: {
      userId_productId: {
        userId: userId,
        productId: productId
      }
    }
  });

  if (existingReview) {
    throw new ConflictError('You have already reviewed this product');
  }

  // Check if user has purchased this product
  const hasPurchased = await prisma.order.findFirst({
    where: {
      userId: userId,
      items: {
        some: {
          productId: productId
        }
      },
      paymentStatus: 'PAID'
    }
  });

  const review = await prisma.review.create({
    data: {
      rating,
      title: title || null,
      content: content || null,
      isVerified: !!hasPurchased,
      status: 'PENDING' as const,
      userId: userId,
      productId: productId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          avatar: true
        }
      }
    }
  });

  logger.info('Review created', {
    reviewId: review.id,
    productId,
    userId,
    rating,
    isVerified: review.isVerified
  });

  // Send review submitted notification
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { name: true }
    });

    if (product) {
      await reviewNotifications.reviewSubmitted(userId, {
        productId,
        productName: product.name,
        rating,
      });
    }
  } catch (notificationError) {
    logger.error('Failed to send review submitted notification', notificationError as Error);
    // Don't fail the review creation if notification fails
  }

  return NextResponse.json({
    success: true,
    data: review,
    message: 'Review submitted for approval'
  });
});