import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// GET /api/products - List all products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'random'; // Default to random ordering

    const skip = (page - 1) * limit;

    const where: any = {
      isActive: true,
    };

    // Build AND conditions array
    const andConditions: any[] = [];

    if (category) {
      andConditions.push({
        OR: [
          // Match products with the category as primary category
          {
            category: {
              slug: category,
            },
          },
          // Match products with the category in their many-to-many relationships
          {
            productCategories: {
              some: {
                category: {
                  slug: category,
                },
              },
            },
          },
        ],
      });
    }

    if (search) {
      andConditions.push({
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      });
    }

    if (andConditions.length > 0) {
      where.AND = andConditions;
    }

    // Define ordering based on sort parameter
    let orderBy: any;
    switch (sort) {
      case 'name_asc':
        orderBy = { name: 'asc' };
        break;
      case 'name_desc':
        orderBy = { name: 'desc' };
        break;
      case 'price_asc':
        orderBy = { price: 'asc' };
        break;
      case 'price_desc':
        orderBy = { price: 'desc' };
        break;
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'random':
      default:
        // For random ordering, we'll use a different approach
        orderBy = undefined;
        break;
    }

    let products;
    let total;

    if (sort === 'random') {
      // For random ordering, we need to handle it differently
      // First get the total count
      total = await prisma.product.count({ where });
      
      // For random ordering, we'll fetch all products and shuffle them
      // This is acceptable for small to medium datasets
      const allProducts = await prisma.product.findMany({
        where,
        include: {
          category: true,
          productCategories: {
            include: {
              category: true,
            },
          },
          images: true,
          variants: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
      });

      // Shuffle the products using Fisher-Yates algorithm
      const shuffled = [...allProducts];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }

      // Apply pagination to shuffled results
      products = shuffled.slice(skip, skip + limit);
    } else {
      // For non-random sorting, use standard Prisma query
      [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          include: {
            category: true,
            productCategories: {
              include: {
                category: true,
              },
            },
            images: true,
            variants: true,
            _count: {
              select: {
                reviews: true,
              },
            },
          },
          orderBy,
          skip,
          take: limit,
        }),
        prisma.product.count({ where }),
      ]);
    }

    return NextResponse.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST /api/products - Create a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      categoryId,
      categoryIds = [],
      images,
      isFeatured,
      variations = [],
    } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Product name is required' },
        { status: 400 }
      );
    }

    // Allow zero or null base price - variations can provide pricing
    let basePrice = price !== undefined ? parseFloat(price.toString()) : null;
    
    // Validate that we have at least one pricing source
    const hasValidBasePrice = basePrice !== null && basePrice >= 0;
    const hasValidVariations = variations && variations.length > 0;
    const warnings: string[] = [];
    
    if (!hasValidBasePrice && !hasValidVariations) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product must have either a base price (can be 0) or variations with pricing'
        },
        { status: 400 }
      );
    }
    
    // Add warnings for edge cases
    if (basePrice === 0 && (!variations || variations.length === 0)) {
      warnings.push('Product has zero base price and no variations. Consider adding variations for pricing.');
    }
    
    if (basePrice === 0 && variations && variations.length > 0) {
      const hasZeroPricedVariations = variations.some((v: any) => !v.price || v.price === 0);
      if (hasZeroPricedVariations) {
        warnings.push('Some variations have zero price. Ensure all variations have valid pricing.');
      }
    }
    
    // Default to 0 if no base price provided
    if (basePrice === null) {
      basePrice = 0;
    }

    // Use categoryIds if provided, otherwise fall back to single categoryId for backward compatibility
    const categoriesToConnect = categoryIds.length > 0 ? categoryIds : (categoryId ? [categoryId] : []);

    const product = await prisma.product.create({
      data: {
        name,
        slug,
        description,
        shortDescription,
        price: basePrice,
        comparePrice: comparePrice ? parseFloat(comparePrice.toString()) : null,
        categoryId, // Keep for backward compatibility
        isFeatured: Boolean(isFeatured),
        images: images
          ? {
              create: images.map((img: any, index: number) => ({
                url: img.url,
                alt: img.alt || name,
                position: index,
              })),
            }
          : undefined,
        variants: variations.length > 0
          ? {
              create: variations.map((variation: any) => ({
                name: variation.name,
                value: variation.value,
                price: variation.price || null,
                pricingMode: variation.pricingMode || 'INCREMENT',
              })),
            }
          : undefined,
        productCategories: categoriesToConnect.length > 0
          ? {
              create: categoriesToConnect.map((catId: string) => ({
                categoryId: catId,
              })),
            }
          : undefined,
      },
      include: {
        category: true,
        productCategories: {
          include: {
            category: true,
          },
        },
        images: true,
        variants: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully',
      warnings: warnings.length > 0 ? warnings : undefined,
    });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
