'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ShoppingCart, Star, Heart, Plus } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../context/CartContext';
import { getProductImage } from '../lib/images';
import ProductCategories from './ProductCategories';

interface ProductCardProps {
  product: Product & { _raw?: any }; // Allow _raw field for database product data
  featured?: boolean;
  viewMode?: 'grid' | 'list';
}

const ProductCard: React.FC<ProductCardProps> = ({ product, featured = false, viewMode = 'grid' }) => {
  const { dispatch } = useCart();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Use highest price when base price is 0 and variations exist
    let priceToUse = product.price || 0;
    const variants = (product as any).variants;
    if (product.price === 0 && variants && variants.length > 0) {
      const prices = variants.map((v: any) => v.price ?? 0).filter((p: number) => p > 0);
      if (prices.length > 0) {
        priceToUse = Math.max(...prices);
      }
    }
    
    const productToAdd = {
      ...product,
      price: priceToUse
    };
    
    dispatch({ type: 'ADD_ITEM', payload: productToAdd });
  };

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };


  // Calculate price display
  const variants = (product as any).variants;
  let priceDisplay = `₹${product.price || 0}`;
  if (variants && variants.length > 0) {
    const prices = [product.price || 0, ...variants.map((v: any) => v.price ?? 0)].filter(p => p > 0);
    if (prices.length > 0) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      if (minPrice !== maxPrice) {
        priceDisplay = `₹${minPrice} - ₹${maxPrice}`;
      }
    }
  }

  if (viewMode === 'list') {
    return (
      <Link href={`/product/${product.slug || product.id}`} className="block group">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-green-200">
          <div className="flex">
            {/* Image Section */}
            <div className="relative w-28 h-28 sm:w-40 sm:h-40 flex-shrink-0">
              <div className="w-full h-full relative overflow-hidden rounded-l-2xl">
                {!imageLoaded && (
                  <div className="absolute inset-0 bg-gray-200 animate-pulse" />
                )}
                <Image
                  src={getProductImage(product.image)}
                  alt={product.name}
                  fill
                  className={`object-cover group-hover:scale-105 transition-transform duration-300 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  sizes="(max-width: 640px) 112px, 160px"
                  onLoad={() => setImageLoaded(true)}
                />
              </div>
              
            </div>

            {/* Content Section */}
            <div className="flex-1 p-3 sm:p-6">
              <div className="flex justify-between items-start mb-1">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1 line-clamp-1 text-base sm:text-lg">{product.name}</h3>

                  {/* Single Category */}
                  <div className="mb-2">
                    <ProductCategories product={product._raw || product} maxCategories={1} />
                  </div>
                </div>
                <button
                  onClick={handleWishlistToggle}
                  className={`p-1.5 sm:p-2 rounded-full transition-colors ml-2 ${
                    isWishlisted
                      ? 'text-red-500 bg-red-50 hover:bg-red-100'
                      : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
                  }`}
                >
                  <Heart className={`w-3.5 h-3.5 sm:w-4 sm:h-4 ${isWishlisted ? 'fill-current' : ''}`} />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <span className="text-base sm:text-lg font-bold text-gray-900">
                    {priceDisplay}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600 ml-1">{product.rating}</span>
                    </div>
                    <span className="text-xs text-gray-500">({product.reviews} reviews)</span>
                  </div>
                </div>
                
                <button
                  onClick={handleAddToCart}
                  className="bg-green-600 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center space-x-1.5 sm:space-x-2"
                >
                  <Plus className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                  <span className="text-xs sm:text-sm font-medium">Add</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link href={`/product/${product.slug || product.id}`} className="block group">
      <div className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-green-200 ${
        featured ? 'w-72 flex-shrink-0 lg:w-80' : 'w-full'
      }`}>
        <div className="relative">
          <div className="w-full aspect-square relative overflow-hidden">
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse" />
            )}
            <Image
              src={getProductImage(product.image)}
              alt={product.name}
              fill
              className={`object-cover group-hover:scale-105 transition-transform duration-300 ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              onLoad={() => setImageLoaded(true)}
            />
          </div>
          
          {/* Top Right Actions */}
          <div className="absolute top-3 right-3 flex flex-col space-y-2">
            <button
              onClick={handleWishlistToggle}
              className={`p-2 rounded-full shadow-sm transition-all duration-200 ${
                isWishlisted
                  ? 'bg-red-500 text-white'
                  : 'bg-white text-gray-600 hover:text-red-500 hover:bg-red-50'
              }`}
            >
              <Heart className={`w-4 h-4 ${isWishlisted ? 'fill-current' : ''}`} />
            </button>
          </div>

          {/* Rating Badge */}
          <div className="absolute top-3 left-3 bg-white rounded-full px-2 py-1 shadow-sm">
            <div className="flex items-center space-x-1">
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
              <span className="text-xs font-medium text-gray-700">{product.rating}</span>
            </div>
          </div>

        </div>
        
        <div className="p-4 lg:p-5">
          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1 text-base lg:text-lg leading-tight">{product.name}</h3>

          {/* Single Category */}
          <div className="mb-4">
            <ProductCategories product={product._raw || product} className="mb-2" maxCategories={1} />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <span className="text-base lg:text-lg font-bold text-gray-900 mb-1">
                {priceDisplay}
              </span>
              <span className="text-xs text-gray-500">{product.reviews} reviews</span>
            </div>
            
            <button
              onClick={handleAddToCart}
              className="bg-green-600 text-white p-2.5 lg:p-3 rounded-full hover:bg-green-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105 active:scale-95"
            >
              <ShoppingCart className="w-4 h-4 lg:w-5 lg:h-5" />
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;