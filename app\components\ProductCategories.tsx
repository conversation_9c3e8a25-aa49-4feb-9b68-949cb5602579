'use client';

import React from 'react';
import Link from 'next/link';
import { Leaf, Droplets, Sparkles, Eye, Zap, Heart } from 'lucide-react';
import { getProductCategories } from '../lib/productUtils';

interface ProductCategoriesProps {
  product: any;
  showAsLinks?: boolean;
  className?: string;
  maxCategories?: number; // Limit number of categories to display
}

const ProductCategories: React.FC<ProductCategoriesProps> = ({
  product,
  showAsLinks = false,
  className = '',
  maxCategories
}) => {
  const allCategories = getProductCategories(product);
  const categories = maxCategories ? allCategories.slice(0, maxCategories) : allCategories;

  if (categories.length === 0) {
    return null;
  }

  const getCategoryIcon = (categoryName: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      'Skincare': <Leaf className="w-3 h-3" />,
      'Hair Care': <Sparkles className="w-3 h-3" />,
      'Body Care': <Heart className="w-3 h-3" />,
      'cleanser': <Droplets className="w-3 h-3" />,
      'serum': <Zap className="w-3 h-3" />,
      'moisturizer': <Droplets className="w-3 h-3" />,
      'mask': <Eye className="w-3 h-3" />,
      'exfoliator': <Sparkles className="w-3 h-3" />,
      'eye-care': <Eye className="w-3 h-3" />,
    };
    return icons[categoryName] || <Leaf className="w-3 h-3" />;
  };

  return (
    <div className={`flex flex-wrap gap-3 ${className}`}>
      {categories.map((category) => {
        const icon = getCategoryIcon(category.name);

        if (showAsLinks) {
          return (
            <Link
              key={category.id}
              href={`/shop?category=${category.slug}`}
              className="inline-flex items-center gap-1.5 text-xs font-medium text-green-600 hover:text-black transition-colors"
            >
              {icon}
              <span>{category.name}</span>
            </Link>
          );
        }

        return (
          <span
            key={category.id}
            className="inline-flex items-center gap-1.5 text-xs font-medium text-green-600"
          >
            {icon}
            <span>{category.name}</span>
          </span>
        );
      })}
    </div>
  );
};

export default ProductCategories;
