'use client';

import React, { useState, useEffect } from 'react';
import { formatPrice } from '../lib/currency';

interface ProductVariation {
  id: string;
  name: string;
  value: string;
  price?: number;
  pricingMode?: 'REPLACE' | 'INCREMENT' | 'FIXED';
}

interface ProductVariationSelectorProps {
  productId: string;
  basePrice: number;
  onVariationChange?: (variation: ProductVariation | null, totalPrice: number) => void;
}

const ProductVariationSelector: React.FC<ProductVariationSelectorProps> = ({
  productId,
  basePrice,
  onVariationChange,
}) => {
  const [variations, setVariations] = useState<ProductVariation[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVariations, setSelectedVariations] = useState<Record<string, ProductVariation>>({});

  useEffect(() => {
    fetchVariations();
  }, [productId]);

  useEffect(() => {
    // Auto-select highest priced variation for each group when variations load
    if (variations.length > 0 && Object.keys(selectedVariations).length === 0) {
      const grouped = variations.reduce((acc, variation) => {
        if (!acc[variation.name]) {
          acc[variation.name] = [];
        }
        acc[variation.name].push(variation);
        return acc;
      }, {} as Record<string, ProductVariation[]>);

      const newSelectedVariations: Record<string, ProductVariation> = {};
      
      Object.entries(grouped).forEach(([variationName, options]) => {
        // Sort variations by price to find the highest priced option
        const sortedOptions = [...options].sort((a, b) => {
          const priceA = a.price ?? 0;
          const priceB = b.price ?? 0;
          return priceB - priceA; // Highest first
        });
        
        // Select the variation with the highest price
        newSelectedVariations[variationName] = sortedOptions[0];
      });

      setSelectedVariations(newSelectedVariations);
      
      // Calculate and trigger price update
      const selectedValues = Object.values(newSelectedVariations);
      const finalPrice = calculateFinalPrice(selectedValues);

      const selectedVariation = Object.keys(newSelectedVariations).length === 1
        ? Object.values(newSelectedVariations)[0]
        : null;

      if (onVariationChange) {
        onVariationChange(selectedVariation, finalPrice);
      }
    }
  }, [variations, basePrice, onVariationChange, selectedVariations]);

  const fetchVariations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${productId}/variations`);
      const result = await response.json();
      
      if (result.success) {
        setVariations(result.data);
      }
    } catch (error) {
      console.error('Error fetching variations:', error);
    } finally {
      setLoading(false);
    }
  };

  // Group variations by name
  const groupedVariations = variations.reduce((acc, variation) => {
    if (!acc[variation.name]) {
      acc[variation.name] = [];
    }
    acc[variation.name].push(variation);
    return acc;
  }, {} as Record<string, ProductVariation[]>);

  const calculateFinalPrice = (selectedValues: ProductVariation[]): number => {
    let finalPrice = basePrice ?? 0;

    if (selectedValues.length === 0) {
      return finalPrice;
    }

    // For product variations, we should use REPLACE mode by default
    // This means the variation price replaces the base price, not adds to it
    // Only use INCREMENT mode if explicitly specified and needed
    
    // Check if any variation explicitly uses INCREMENT mode
    const hasExplicitIncrement = selectedValues.some(v =>
      v.pricingMode === 'INCREMENT' && v.price !== undefined && v.price !== null
    );

    if (hasExplicitIncrement) {
      // INCREMENT mode - add all variations to base
      const totalAdjustment = selectedValues.reduce(
        (sum, v) => sum + (v.price ?? 0),
        0
      );
      finalPrice = (basePrice ?? 0) + totalAdjustment;
    } else {
      // REPLACE mode (default behavior) - use the variation price directly
      // For single variation, use its price
      // For multiple variations, use the highest priced one
      const validVariations = selectedValues.filter(v =>
        v.price !== undefined && v.price !== null && v.price > 0
      );
      
      if (validVariations.length > 0) {
        // Use the highest priced variation
        const highestPricedVariation = validVariations.reduce((max, current) =>
          (current.price ?? 0) > (max.price ?? 0) ? current : max
        );
        finalPrice = highestPricedVariation.price ?? finalPrice;
      }
    }

    return Math.max(0, finalPrice);
  };

  const handleVariationSelect = (variationName: string, variation: ProductVariation) => {
    const newSelectedVariations = {
      ...selectedVariations,
      [variationName]: variation,
    };
    setSelectedVariations(newSelectedVariations);

    const selectedValues = Object.values(newSelectedVariations);
    const finalPrice = calculateFinalPrice(selectedValues);

    // Get the currently selected variation (for single variation products)
    const selectedVariation = Object.keys(newSelectedVariations).length === 1
      ? Object.values(newSelectedVariations)[0]
      : null;

    if (onVariationChange) {
      onVariationChange(selectedVariation, finalPrice);
    }
  };

  const isVariationAvailable = (variation: ProductVariation) => {
    return true; // All variations are available since we removed quantity tracking
  };

  const getSelectedVariationInfo = () => {
    const selectedValues = Object.values(selectedVariations);
    if (selectedValues.length === 0) return null;

    const finalPrice = calculateFinalPrice(selectedValues);

    return {
      totalPrice: finalPrice,
      selectedValues,
    };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="flex space-x-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-10 w-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (variations.length === 0) {
    return null; // No variations available
  }

  const selectedInfo = getSelectedVariationInfo();

  return (
    <div className="space-y-6">
      {Object.entries(groupedVariations).map(([variationName, variationOptions]) => (
        <div key={variationName} className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 capitalize">
              {variationName}
            </h4>
            {selectedVariations[variationName] && (
              <span className="text-sm text-gray-600">
                Selected: {selectedVariations[variationName].value}
              </span>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            {variationOptions.map((variation) => {
              const isSelected = selectedVariations[variationName]?.id === variation.id;
              const isAvailable = isVariationAvailable(variation);
              
              return (
                <button
                  key={variation.id}
                  onClick={() => isAvailable && handleVariationSelect(variationName, variation)}
                  disabled={!isAvailable}
                  className={`
                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors
                    ${isSelected
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : isAvailable
                      ? 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                      : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  <span>{variation.value}</span>
                </button>
              );
            })}
          </div>
        </div>
      ))}

      {/* Selected Variation Summary */}
      {selectedInfo && (
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-lg font-semibold text-gray-900">
            {formatPrice(selectedInfo.totalPrice)}
          </div>
        </div>
      )}

      {/* Variation Selection Requirement */}
      {Object.keys(groupedVariations).length > 0 && Object.keys(selectedVariations).length === 0 && (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            Please select all required options before adding to cart.
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductVariationSelector;
