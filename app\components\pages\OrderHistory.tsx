'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Package, Truck, CheckCircle, Clock, RotateCcw, Eye } from 'lucide-react';

const OrderHistory: React.FC = () => {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState('all');

  const orders = [
    {
      id: 'ORD-2024-001',
      date: '2024-01-15',
      status: 'delivered',
      total: 89.97,
      items: [
        { name: 'Botanical Cleanser', quantity: 1, price: 28.99 },
        { name: 'Hydrating Serum', quantity: 1, price: 45.99 },
        { name: 'Nourishing Moisturizer', quantity: 1, price: 35.99 }
      ]
    },
    {
      id: 'ORD-2024-002',
      date: '2024-01-20',
      status: 'shipped',
      total: 62.98,
      items: [
        { name: 'Rejuvenating Face Mask', quantity: 1, price: 32.99 },
        { name: 'Gentle Exfoliator', quantity: 1, price: 29.99 }
      ]
    },
    {
      id: 'ORD-2024-003',
      date: '2024-01-25',
      status: 'processing',
      total: 74.98,
      items: [
        { name: 'Eye Care Cream', quantity: 1, price: 39.99 },
        { name: 'Botanical Cleanser', quantity: 1, price: 28.99 }
      ]
    }
  ];

  const statusConfig = {
    processing: { icon: Clock, color: 'text-green-600', bg: 'bg-green-100', label: 'Processing' },
    shipped: { icon: Truck, color: 'text-green-600', bg: 'bg-green-100', label: 'Shipped' },
    delivered: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'Delivered' }
  };

  const filters = [
    { id: 'all', label: 'All Orders', count: orders.length },
    { id: 'processing', label: 'Processing', count: orders.filter(o => o.status === 'processing').length },
    { id: 'shipped', label: 'Shipped', count: orders.filter(o => o.status === 'shipped').length },
    { id: 'delivered', label: 'Delivered', count: orders.filter(o => o.status === 'delivered').length }
  ];

  const filteredOrders = selectedFilter === 'all' 
    ? orders 
    : orders.filter(order => order.status === selectedFilter);

  const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Header */}
        <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-bold text-gray-800">Order History</h1>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 py-6 bg-gradient-to-br from-green-50 to-green-100">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{orders.length}</div>
              <div className="text-sm text-gray-600">Total Orders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">₹{totalSpent.toFixed(2)}</div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="px-4 py-4 bg-white border-b">
          <div className="flex space-x-2 overflow-x-auto">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setSelectedFilter(filter.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedFilter === filter.id
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{filter.label}</span>
                <span className={`px-2 py-0.5 rounded-full text-xs ${
                  selectedFilter === filter.id
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {filter.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Orders List */}
        <div className="px-4 py-6">
          {filteredOrders.length > 0 ? (
            <div className="space-y-4">
              {filteredOrders.map((order) => {
                const StatusIcon = statusConfig[order.status as keyof typeof statusConfig].icon;
                const statusStyle = statusConfig[order.status as keyof typeof statusConfig];
                
                return (
                  <div key={order.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-800">{order.id}</h3>
                        <p className="text-sm text-gray-600">{new Date(order.date).toLocaleDateString()}</p>
                      </div>
                      <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusStyle.bg}`}>
                        <StatusIcon className={`w-4 h-4 ${statusStyle.color}`} />
                        <span className={`text-sm font-medium ${statusStyle.color}`}>
                          {statusStyle.label}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      {order.items.map((item, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span className="text-gray-600">{item.quantity}x {item.name}</span>
                          <span className="font-medium text-gray-800">₹{item.price}</span>
                        </div>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="text-lg font-bold text-gray-900">
                        Total: ₹{order.total.toFixed(2)}
                      </div>
                      <div className="flex space-x-2">
                        <button className="flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors">
                          <Eye className="w-4 h-4" />
                          <span>Details</span>
                        </button>
                        {order.status === 'delivered' && (
                          <button className="flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors">
                            <RotateCcw className="w-4 h-4" />
                            <span>Reorder</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No orders found</h3>
              <p className="text-gray-600 mb-6">No orders match the selected filter</p>
              <button
                onClick={() => setSelectedFilter('all')}
                className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
              >
                View All Orders
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>

          <h1 className="text-4xl font-bold text-gray-800 mb-8">Order History</h1>

          {/* Summary Stats */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8 mb-8">
            <div className="grid grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">{orders.length}</div>
                <div className="text-gray-600">Total Orders</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">₹{totalSpent.toFixed(2)}</div>
                <div className="text-gray-600">Total Spent</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {orders.filter(o => o.status === 'delivered').length}
                </div>
                <div className="text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  ₹{(totalSpent / orders.length).toFixed(2)}
                </div>
                <div className="text-gray-600">Average Order</div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex space-x-4 mb-8">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setSelectedFilter(filter.id)}
                className={`flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ${
                  selectedFilter === filter.id
                    ? 'bg-green-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                }`}
              >
                <span>{filter.label}</span>
                <span className={`px-3 py-1 rounded-full text-sm ${
                  selectedFilter === filter.id
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {filter.count}
                </span>
              </button>
            ))}
          </div>

          {/* Orders List */}
          {filteredOrders.length > 0 ? (
            <div className="space-y-6">
              {filteredOrders.map((order) => {
                const StatusIcon = statusConfig[order.status as keyof typeof statusConfig].icon;
                const statusStyle = statusConfig[order.status as keyof typeof statusConfig];
                
                return (
                  <div key={order.id} className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">{order.id}</h3>
                        <p className="text-gray-600">Ordered on {new Date(order.date).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}</p>
                      </div>
                      <div className={`flex items-center space-x-3 px-4 py-2 rounded-xl ${statusStyle.bg}`}>
                        <StatusIcon className={`w-5 h-5 ${statusStyle.color}`} />
                        <span className={`font-medium ${statusStyle.color}`}>
                          {statusStyle.label}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-8 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-800 mb-3">Items Ordered</h4>
                        <div className="space-y-2">
                          {order.items.map((item, index) => (
                            <div key={index} className="flex justify-between">
                              <span className="text-gray-600">{item.quantity}x {item.name}</span>
                              <span className="font-medium text-gray-800">₹{item.price}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-gray-800 mb-3">Order Summary</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-gray-600">
                            <span>Subtotal</span>
                            <span>₹{order.total.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between text-gray-600">
                            <span>Shipping</span>
                            <span>Free</span>
                          </div>
                          <div className="flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200">
                            <span>Total</span>
                            <span>₹{order.total.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                      
                      <div className="flex space-x-3 ml-auto">
                        <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors">
                          <Eye className="w-4 h-4" />
                          <span>View Details</span>
                        </button>
                        {order.status === 'delivered' && (
                          <button className="flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors">
                            <RotateCcw className="w-4 h-4" />
                            <span>Reorder</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16">
              <Package className="w-24 h-24 text-gray-300 mx-auto mb-6" />
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">No orders found</h3>
              <p className="text-gray-600 mb-8">No orders match the selected filter</p>
              <button
                onClick={() => setSelectedFilter('all')}
                className="bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors"
              >
                View All Orders
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderHistory;