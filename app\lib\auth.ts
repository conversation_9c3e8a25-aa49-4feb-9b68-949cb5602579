import { NextAuthOptions } from "next-auth";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import * as bcrypt from "bcryptjs";
import { prisma } from "./db";

interface ExtendedUser {
  id: string;
  email: string;
  name?: string | null;
  role: string;
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Invalid credentials");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user || !user.password) {
          throw new Error("Invalid credentials");
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          throw new Error("Invalid credentials");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        } as ExtendedUser;
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // If this is the first time the user signs in
      if (user) {
        token.sub = user.id;
        token.role = (user as ExtendedUser).role;
      }
      
      // For OAuth providers, ensure we get the correct user ID from database
      if (account && token.email) {
        try {
          const dbUser = await prisma.user.findUnique({
            where: { email: token.email },
            select: { id: true, role: true }
          });
          
          if (dbUser) {
            token.sub = dbUser.id;
            token.role = dbUser.role;
          }
        } catch (error) {
          // Log error in development only
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching user in JWT callback:', error);
          }
        }
      }
      
      return token;
    },
    async session({ session, token }) {
      // Always try to get fresh user data from database
      if (token.email) {
        try {
          const dbUser = await prisma.user.findUnique({
            where: { email: token.email },
            select: { id: true, role: true, email: true, name: true }
          });
          
          if (dbUser) {
            return {
              ...session,
              user: {
                ...session.user,
                id: dbUser.id,
                role: dbUser.role,
                email: dbUser.email,
                name: dbUser.name,
              },
            };
          }
        } catch (error) {
          // Log error in development only
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching user in session callback:', error);
          }
        }
      }
      
      // Fallback: if we have token.sub but no database lookup worked
      if (session.user && token.sub) {
        return {
          ...session,
          user: {
            ...session.user,
            id: token.sub,
            role: token.role as string,
          },
        };
      }
      
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log sign in events in development only
      if (process.env.NODE_ENV === 'development') {
        console.log('Sign in event:', {
          userId: user.id,
          email: user.email,
          provider: account?.provider,
          isNewUser
        });
      }
    },
  },
  pages: {
    signIn: "/login",
    signOut: "/",
    error: "/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
};