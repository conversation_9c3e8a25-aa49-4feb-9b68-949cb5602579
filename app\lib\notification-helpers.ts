import { notificationService, NotificationData } from './notifications';
import { NotificationType, NotificationPriority } from '@prisma/client';

/**
 * Order notification helpers
 */
export const orderNotifications = {
  /**
   * Send order placed notification
   */
  async orderPlaced(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    total: number;
    currency: string;
    itemCount: number;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_PLACED,
      title: 'Order Placed Successfully',
      message: `Your order #${orderData.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        amount: orderData.total,
        currency: orderData.currency,
        itemCount: orderData.itemCount,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
    });
  },

  /**
   * Send order confirmed notification
   */
  async orderConfirmed(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    estimatedDelivery?: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_CONFIRMED,
      title: 'Order Confirmed',
      message: `Your order #${orderData.orderNumber} has been confirmed and is being prepared for shipment.${orderData.estimatedDelivery ? ` Estimated delivery: ${orderData.estimatedDelivery}` : ''}`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        estimatedDelivery: orderData.estimatedDelivery,
      },
      priority: NotificationPriority.NORMAL,
      sendEmail: true,
    });
  },

  /**
   * Send order processing notification
   */
  async orderProcessing(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_PROCESSING,
      title: 'Order Being Processed',
      message: `Your order #${orderData.orderNumber} is currently being processed. We'll notify you once it's shipped.`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
      },
      priority: NotificationPriority.NORMAL,
      sendEmail: true,
    });
  },

  /**
   * Send order shipped notification
   */
  async orderShipped(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    estimatedDelivery?: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_SHIPPED,
      title: 'Order Shipped',
      message: `Great news! Your order #${orderData.orderNumber} has been shipped.${orderData.estimatedDelivery ? ` Estimated delivery: ${orderData.estimatedDelivery}` : ''}`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        estimatedDelivery: orderData.estimatedDelivery,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
    });
  },

  /**
   * Send order delivered notification
   */
  async orderDelivered(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    deliveredAt?: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_DELIVERED,
      title: 'Order Delivered',
      message: `Your order #${orderData.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        deliveredAt: orderData.deliveredAt,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
    });
  },

  /**
   * Send order cancelled notification
   */
  async orderCancelled(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    reason?: string;
    refundAmount?: number;
    currency?: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ORDER_CANCELLED,
      title: 'Order Cancelled',
      message: `Your order #${orderData.orderNumber} has been cancelled.${orderData.reason ? ` Reason: ${orderData.reason}` : ''}${orderData.refundAmount ? ` A refund of ${orderData.currency} ${orderData.refundAmount} will be processed within 3-5 business days.` : ''}`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        reason: orderData.reason,
        refundAmount: orderData.refundAmount,
        currency: orderData.currency,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
    });
  },
};

/**
 * Wishlist notification helpers
 */
export const wishlistNotifications = {
  /**
   * Send wishlist item added notification
   */
  async itemAdded(userId: string, productData: {
    productId: string;
    productName: string;
    price?: number;
    currency?: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.WISHLIST_ADDED,
      title: 'Item Added to Wishlist',
      message: `${productData.productName} has been added to your wishlist. We'll notify you of any price changes!`,
      data: {
        productId: productData.productId,
        productName: productData.productName,
        price: productData.price,
        currency: productData.currency,
      },
      priority: NotificationPriority.LOW,
      sendEmail: false, // Usually don't email for wishlist additions
    });
  },

  /**
   * Send wishlist item removed notification
   */
  async itemRemoved(userId: string, productData: {
    productId: string;
    productName: string;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.WISHLIST_REMOVED,
      title: 'Item Removed from Wishlist',
      message: `${productData.productName} has been removed from your wishlist.`,
      data: {
        productId: productData.productId,
        productName: productData.productName,
      },
      priority: NotificationPriority.LOW,
      sendEmail: false,
    });
  },

  /**
   * Send price drop alert
   */
  async priceDropAlert(userId: string, productData: {
    productId: string;
    productName: string;
    oldPrice: number;
    newPrice: number;
    currency: string;
    discountPercentage: number;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.PRICE_DROP_ALERT,
      title: 'Price Drop Alert!',
      message: `Great news! ${productData.productName} is now ${productData.discountPercentage}% off! Price dropped from ${productData.currency} ${productData.oldPrice} to ${productData.currency} ${productData.newPrice}.`,
      data: {
        productId: productData.productId,
        productName: productData.productName,
        oldPrice: productData.oldPrice,
        newPrice: productData.newPrice,
        currency: productData.currency,
        discountPercentage: productData.discountPercentage,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Expire in 7 days
    });
  },
};

/**
 * Review notification helpers
 */
export const reviewNotifications = {
  /**
   * Send review request notification
   */
  async reviewRequest(userId: string, orderData: {
    orderId: string;
    orderNumber: string;
    productIds: string[];
    productNames: string[];
  }) {
    const productList = orderData.productNames.join(', ');
    
    return await notificationService.createNotification({
      userId,
      type: NotificationType.REVIEW_REQUEST,
      title: 'How was your experience?',
      message: `We'd love to hear about your experience with ${productList}. Your review helps other customers make informed decisions!`,
      data: {
        orderId: orderData.orderId,
        orderNumber: orderData.orderNumber,
        productIds: orderData.productIds,
        productNames: orderData.productNames,
      },
      priority: NotificationPriority.NORMAL,
      sendEmail: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Expire in 30 days
    });
  },

  /**
   * Send review submitted confirmation
   */
  async reviewSubmitted(userId: string, reviewData: {
    productId: string;
    productName: string;
    rating: number;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.REVIEW_SUBMITTED,
      title: 'Review Submitted',
      message: `Thank you for your ${reviewData.rating}-star review of ${reviewData.productName}! Your feedback is valuable to us and other customers.`,
      data: {
        productId: reviewData.productId,
        productName: reviewData.productName,
        rating: reviewData.rating,
      },
      priority: NotificationPriority.LOW,
      sendEmail: false,
    });
  },
};



/**
 * System notification helpers
 */
export const systemNotifications = {
  /**
   * Send system notification
   */
  async sendSystemNotification(userId: string, messageData: {
    title: string;
    message: string;
    data?: NotificationData;
    priority?: NotificationPriority;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.SYSTEM,
      title: messageData.title,
      message: messageData.message,
      data: messageData.data,
      priority: messageData.priority || NotificationPriority.NORMAL,
      sendEmail: false, // System notifications are usually in-app only
    });
  },
};

/**
 * Admin notification helpers
 */
export const adminNotifications = {
  /**
   * Send admin message to user
   */
  async adminMessage(userId: string, data: {
    title: string;
    content: string;
    type?: string;
    priority?: string;
    sendEmail?: boolean;
    sendInApp?: boolean;
  }) {
    return await notificationService.createNotification({
      userId,
      type: (data.type as NotificationType) || NotificationType.ADMIN_MESSAGE,
      title: data.title,
      message: data.content,
      data: {
        sentByAdmin: true,
        sendEmail: data.sendEmail !== false,
        sendInApp: data.sendInApp !== false,
      },
      priority: (data.priority as NotificationPriority) || NotificationPriority.NORMAL,
      sendEmail: data.sendEmail !== false,
    });
  },

  /**
   * Send system alert
   */
  async systemAlert(userId: string, data: {
    title: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical'
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.SYSTEM,
      title: data.title,
      message: data.message,
      data: {
        severity: data.severity,
      },
      priority: data.severity === 'critical' ? NotificationPriority.URGENT :
                data.severity === 'high' ? NotificationPriority.HIGH :
                NotificationPriority.NORMAL,
      sendEmail: data.severity === 'critical' || data.severity === 'high',
    });
  },

  /**
   * Send maintenance notice
   */
  async maintenanceNotice(userId: string, data: {
    startTime: string;
    endTime: string;
    description?: string
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.SYSTEM,
      title: 'Scheduled Maintenance',
      message: `Our system will be under maintenance from ${data.startTime} to ${data.endTime}. ${data.description || 'We apologize for any inconvenience.'}`,
      data: {
        maintenanceStart: data.startTime,
        maintenanceEnd: data.endTime,
      },
      priority: NotificationPriority.HIGH,
      sendEmail: true,
    });
  },

  /**
   * Send admin message to specific user
   */
  async sendMessage(userId: string, messageData: {
    title: string;
    message: string;
    priority?: NotificationPriority;
    sendEmail?: boolean;
  }) {
    return await notificationService.createNotification({
      userId,
      type: NotificationType.ADMIN_MESSAGE,
      title: messageData.title,
      message: messageData.message,
      priority: messageData.priority || NotificationPriority.NORMAL,
      sendEmail: messageData.sendEmail || false,
    });
  },

  /**
   * Send broadcast message to all users
   */
  async sendBroadcast(messageData: {
    title: string;
    message: string;
    priority?: NotificationPriority;
    sendEmail?: boolean;
    userIds?: string[];
  }) {
    return await notificationService.sendBroadcast({
      type: NotificationType.BROADCAST,
      title: messageData.title,
      message: messageData.message,
      priority: messageData.priority || NotificationPriority.NORMAL,
      sendEmail: messageData.sendEmail || false,
      userIds: messageData.userIds,
    });
  },

  /**
   * Send promotional notification
   */
  async sendPromotion(messageData: {
    title: string;
    message: string;
    data?: NotificationData;
    expiresAt?: Date;
    sendEmail?: boolean;
    userIds?: string[];
  }) {
    return await notificationService.sendBroadcast({
      type: NotificationType.PROMOTIONAL,
      title: messageData.title,
      message: messageData.message,
      data: messageData.data,
      priority: NotificationPriority.NORMAL,
      expiresAt: messageData.expiresAt,
      sendEmail: messageData.sendEmail || false,
      userIds: messageData.userIds,
    });
  },
};
