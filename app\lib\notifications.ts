import { prisma } from './db';
import { sendEmail } from './email';
import { logger } from './logger';
import { NotificationType, NotificationPriority } from '@prisma/client';

export interface NotificationData {
  orderId?: string;
  orderNumber?: string;
  productId?: string;
  productName?: string;
  amount?: number;
  currency?: string;
  [key: string]: any;
}

export interface CreateNotificationOptions {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
  priority?: NotificationPriority;
  expiresAt?: Date;
  templateId?: string;
  sendEmail?: boolean;
}

export interface SendBroadcastOptions {
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
  priority?: NotificationPriority;
  expiresAt?: Date;
  templateId?: string;
  sendEmail?: boolean;
  userIds?: string[]; // If specified, send only to these users
}

class NotificationService {
  /**
   * Create a notification for a specific user
   */
  async createNotification(options: CreateNotificationOptions) {
    try {
      // Check user preferences
      const userPreferences = await prisma.userPreference.findUnique({
        where: { userId: options.userId },
      });

      // Check if user wants this type of notification
      if (!this.shouldSendNotification(options.type, userPreferences)) {
        logger.info(`Notification skipped for user ${options.userId} due to preferences`);
        return null;
      }

      // Create the notification
      const notification = await prisma.notification.create({
        data: {
          userId: options.userId,
          type: options.type,
          title: options.title,
          message: options.message,
          data: options.data || {},
          priority: options.priority || NotificationPriority.NORMAL,
          expiresAt: options.expiresAt,
          templateId: options.templateId,
        },
        include: {
          user: true,
          template: true,
        },
      });

      // Send email if requested and user allows email notifications
      if (options.sendEmail && userPreferences?.emailNotifications !== false) {
        await this.sendEmailNotification(notification);
      }

      logger.info(`Notification created: ${notification.id} for user ${options.userId}`);
      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Send broadcast notification to multiple users
   */
  async sendBroadcast(options: SendBroadcastOptions) {
    try {
      let targetUsers;

      if (options.userIds && options.userIds.length > 0) {
        // Send to specific users
        targetUsers = await prisma.user.findMany({
          where: {
            id: { in: options.userIds },
          },
          include: {
            preferences: true,
          },
        });
      } else {
        // Send to all users who allow broadcast messages
        targetUsers = await prisma.user.findMany({
          include: {
            preferences: true,
          },
          where: {
            OR: [
              { preferences: { broadcastMessages: true } },
              { preferences: null }, // Users without preferences (default to true)
            ],
          },
        });
      }

      const notifications = [];

      for (const user of targetUsers) {
        if (this.shouldSendNotification(options.type, user.preferences)) {
          const notification = await prisma.notification.create({
            data: {
              userId: user.id,
              type: options.type,
              title: options.title,
              message: options.message,
              data: options.data || {},
              priority: options.priority || NotificationPriority.NORMAL,
              expiresAt: options.expiresAt,
              templateId: options.templateId,
            },
          });

          notifications.push(notification);

          // Send email if requested and user allows email notifications
          if (options.sendEmail && user.preferences?.emailNotifications !== false) {
            await this.sendEmailNotification({
              ...notification,
              user,
              template: null,
            });
          }
        }
      }

      logger.info(`Broadcast sent to ${notifications.length} users`);
      return notifications;
    } catch (error) {
      logger.error('Error sending broadcast:', error);
      throw error;
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: any) {
    try {
      const user = notification.user;
      if (!user?.email) {
        throw new Error('User email not found');
      }

      // Use template if available, otherwise use notification content
      const emailSubject = notification.template?.emailSubject || notification.title;
      const emailContent = notification.template?.emailTemplate || this.generateEmailContent(notification);

      await sendEmail({
        to: user.email,
        subject: emailSubject,
        html: emailContent,
      });

      // Update notification to mark email as sent
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          emailSent: true,
          emailSentAt: new Date(),
        },
      });

      logger.info(`Email notification sent to ${user.email}`);
    } catch (error) {
      // Update notification to mark email error
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          emailError: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      logger.error('Error sending email notification:', error);
    }
  }

  /**
   * Check if notification should be sent based on user preferences
   */
  private shouldSendNotification(type: NotificationType, preferences: any): boolean {
    if (!preferences) return true; // Default to true if no preferences set

    // Check general notification preferences
    if (!preferences.inAppNotifications) return false;

    // Check specific notification type preferences
    switch (type) {
      case NotificationType.ORDER_PLACED:
      case NotificationType.ORDER_CONFIRMED:
      case NotificationType.ORDER_PROCESSING:
      case NotificationType.ORDER_SHIPPED:
      case NotificationType.ORDER_DELIVERED:
      case NotificationType.ORDER_CANCELLED:
        return preferences.orderNotifications;

      case NotificationType.WISHLIST_ADDED:
      case NotificationType.WISHLIST_REMOVED:
        return preferences.wishlistNotifications;

      case NotificationType.PRICE_DROP_ALERT:
        return preferences.priceDropAlerts;

      case NotificationType.REVIEW_REQUEST:
      case NotificationType.REVIEW_SUBMITTED:
        return preferences.reviewNotifications;

      case NotificationType.ADMIN_MESSAGE:
        return preferences.adminMessages;

      case NotificationType.BROADCAST:
      case NotificationType.PROMOTIONAL:
        return preferences.broadcastMessages;

      default:
        return true;
    }
  }

  /**
   * Generate basic email content for notifications
   */
  private generateEmailContent(notification: any): string {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    
    return `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${notification.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${notification.message}</p>
        </div>
        
        ${notification.data && Object.keys(notification.data).length > 0 ? `
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(notification.data)}
          </div>
        ` : ''}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${baseUrl}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `;
  }

  /**
   * Format notification data for email display
   */
  private formatNotificationData(data: NotificationData): string {
    let html = '<ul style="color: #666; line-height: 1.6;">';
    
    if (data.orderNumber) {
      html += `<li><strong>Order Number:</strong> ${data.orderNumber}</li>`;
    }
    if (data.productName) {
      html += `<li><strong>Product:</strong> ${data.productName}</li>`;
    }
    if (data.amount && data.currency) {
      html += `<li><strong>Amount:</strong> ${data.currency} ${data.amount}</li>`;
    }
    
    html += '</ul>';
    return html;
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string) {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
          userId: userId, // Ensure user can only mark their own notifications
        },
        data: {
          isRead: true,
        },
      });

      return notification;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId: userId,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      });

      return result;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(userId: string, options: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    type?: NotificationType;
  } = {}) {
    try {
      const { page = 1, limit = 20, unreadOnly = false, type } = options;
      const skip = (page - 1) * limit;

      const where: any = {
        userId: userId,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      };

      if (unreadOnly) {
        where.isRead = false;
      }

      if (type) {
        where.type = type;
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          include: {
            template: true,
          },
        }),
        prisma.notification.count({ where }),
      ]);

      return {
        notifications,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(userId: string) {
    try {
      const count = await prisma.notification.count({
        where: {
          userId: userId,
          isRead: false,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
      });

      return count;
    } catch (error) {
      logger.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Delete old notifications (cleanup job)
   */
  async cleanupOldNotifications(daysOld: number = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await prisma.notification.deleteMany({
        where: {
          createdAt: { lt: cutoffDate },
          isRead: true,
        },
      });

      logger.info(`Cleaned up ${result.count} old notifications`);
      return result;
    } catch (error) {
      logger.error('Error cleaning up notifications:', error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
