import { S3Client, PutObjectCommand, DeleteObjectCommand, ListObjectsV2Command, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Configure R2 client
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
  forcePathStyle: true,
});

// Debug function to check R2 configuration
export function checkR2Config() {
  return {
    hasAccessKey: !!process.env.R2_ACCESS_KEY_ID,
    hasSecretKey: !!process.env.R2_SECRET_ACCESS_KEY,
    hasBucketName: !!process.env.R2_BUCKET_NAME,
    hasAccountId: !!process.env.R2_ACCOUNT_ID,
    hasPublicUrl: !!process.env.R2_PUBLIC_URL,
    bucketName: BUCKET_NAME,
    publicUrl: PUBLIC_URL,
    endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`
  };
}

const BUCKET_NAME = process.env.R2_BUCKET_NAME || 'herbalicious-images';
const PUBLIC_URL = process.env.R2_PUBLIC_URL || `https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;

// Helper function to construct proper public URL
function getPublicUrl(key: string): string {
  // For pub-*.r2.dev URLs, files are directly accessible without bucket name
  if (PUBLIC_URL.includes('pub-') && PUBLIC_URL.includes('.r2.dev')) {
    return `${PUBLIC_URL}/${key}`;
  }
  // If PUBLIC_URL already includes the bucket name, don't add it again
  if (PUBLIC_URL.includes(BUCKET_NAME)) {
    return `${PUBLIC_URL}/${key}`;
  }
  // Otherwise, add the bucket name
  return `${PUBLIC_URL}/${BUCKET_NAME}/${key}`;
}

export interface MediaFile {
  key: string;
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
  folder?: string;
}

export interface UploadResult {
  success: boolean;
  file?: MediaFile;
  error?: string;
}

// Upload file to R2
export async function uploadToR2(
  file: File,
  folder: string = 'uploads'
): Promise<UploadResult> {
  try {
    const timestamp = Date.now();
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const key = `${folder}/${timestamp}_${sanitizedName}`;

    const buffer = await file.arrayBuffer();

    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: new Uint8Array(buffer),
      ContentType: file.type,
      ContentLength: file.size,
    });

    await r2Client.send(command);

    const mediaFile: MediaFile = {
      key,
      name: file.name,
      size: file.size,
      type: getFileType(file.name, file.type),
      url: getPublicUrl(key),
      lastModified: new Date(),
      folder,
    };

    return { success: true, file: mediaFile };
  } catch (error) {
    console.error('Error uploading to R2:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Upload failed' 
    };
  }
}

// Delete file from R2
export async function deleteFromR2(key: string): Promise<boolean> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await r2Client.send(command);
    return true;
  } catch (error) {
    console.error('Error deleting from R2:', error);
    return false;
  }
}

// List files from R2
export async function listR2Files(
  folder?: string,
  maxKeys: number = 100
): Promise<MediaFile[]> {
  try {
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: folder ? `${folder}/` : undefined,
      MaxKeys: maxKeys,
    });

    const response = await r2Client.send(command);
    
    if (!response.Contents) {
      return [];
    }

    return response.Contents.map(object => {
      const key = object.Key!;
      const name = key.split('/').pop() || key;
      return {
        key,
        name,
        size: object.Size || 0,
        type: getFileType(name),
        url: getPublicUrl(key),
        lastModified: object.LastModified || new Date(),
        folder: key.includes('/') ? key.split('/')[0] : undefined,
      };
    });
  } catch (error) {
    console.error('Error listing R2 files:', error);
    return [];
  }
}

// Generate presigned URL for secure uploads
export async function generatePresignedUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<string> {
  try {
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      ContentType: contentType,
    });

    return await getSignedUrl(r2Client, command, { expiresIn });
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw error;
  }
}

// Get file type from extension and MIME type
function getFileType(filename: string, mimeType?: string): string {
  const extension = filename.split('.').pop()?.toLowerCase();

  // Check MIME type first if available
  if (mimeType) {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType === 'application/pdf' || mimeType.startsWith('application/msword') ||
        mimeType.startsWith('application/vnd.openxmlformats-officedocument')) return 'document';
  }

  // Fallback to extension
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico'];
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'];
  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx'];

  if (imageTypes.includes(extension || '')) return 'image';
  if (videoTypes.includes(extension || '')) return 'video';
  if (documentTypes.includes(extension || '')) return 'document';

  return 'other';
}

// Get file size in human readable format
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Validate file type and size
export function validateFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'video/mp4',
    'video/webm',
    'application/pdf',
  ];

  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 10MB' };
  }

  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'File type not supported' };
  }

  return { valid: true };
}
