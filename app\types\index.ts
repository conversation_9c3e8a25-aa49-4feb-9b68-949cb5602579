export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  position: number;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
}

export interface Product {
  id: string;
  slug?: string; // Add slug field
  name: string;
  description: string;
  shortDescription: string;
  price: number;
  image?: string; // Keep for backward compatibility
  images?: ProductImage[];
  category: string; // Keep for backward compatibility
  categories?: Category[]; // New field for multiple categories
  featured: boolean;
  ingredients: string[];
  benefits: string[];
  rating: number;
  reviews: number;
  variants?: Array<{
    id: string;
    name: string;
    value: string;
    price?: number;
  }>;
}

export interface CartItem {
  product: Product;
  quantity: number;
  selectedVariants?: Array<{
    id: string;
    name: string;
    value: string;
    price?: number;
  }>;
  variantKey?: string; // Unique key to identify this specific variant combination
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  orders: Order[];
}

export interface Order {
  id: string;
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered';
  items: CartItem[];
}

export interface Review {
  id: string;
  rating: number;
  title?: string;
  content?: string;
  isVerified: boolean;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  userId: string;
  productId: string;
  user?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
  };
  product?: {
    id: string;
    name: string;
    slug?: string;
  };
}

export type ReviewStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export interface ReviewCreateInput {
  rating: number;
  title?: string;
  content?: string;
}

export interface ReviewUpdateInput {
  status: ReviewStatus;
}

export interface Coupon {
  id: string;
  code: string;
  name: string;
  description?: string;
  type: CouponType;
  discountType: DiscountType;
  discountValue: number;
  minimumAmount?: number;
  maximumDiscount?: number;
  usageLimit?: number;
  usageCount: number;
  userUsageLimit?: number;
  isActive: boolean;
  isStackable: boolean;
  showInModule: boolean;
  validFrom: string;
  validUntil?: string;
  applicableProducts: string[];
  applicableCategories: string[];
  excludedProducts: string[];
  excludedCategories: string[];
  customerSegments: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CouponUsage {
  id: string;
  couponId: string;
  userId: string;
  orderId?: string;
  usedAt: string;
}

export interface AppliedCoupon {
  coupon: Coupon;
  discountAmount: number;
  isValid: boolean;
  errorMessage?: string;
}

export interface CouponValidationResult {
  isValid: boolean;
  discountAmount: number;
  errorMessage?: string;
  coupon?: Coupon;
}

export interface CartCouponState {
  appliedCoupons: AppliedCoupon[];
  totalDiscount: number;
  availableCoupons: Coupon[];
}

export type CouponType =
  | 'STORE_WIDE'
  | 'PRODUCT_SPECIFIC'
  | 'CATEGORY_SPECIFIC'
  | 'MINIMUM_PURCHASE'
  | 'BUNDLE_DEAL'
  | 'FIRST_TIME_CUSTOMER'
  | 'LOYALTY_REWARD'
  | 'SEASONAL';

export type DiscountType =
  | 'PERCENTAGE'
  | 'FIXED_AMOUNT'
  | 'FREE_SHIPPING'
  | 'BUY_X_GET_Y';

export interface CouponCreateInput {
  code: string;
  name: string;
  description?: string;
  type: CouponType;
  discountType: DiscountType;
  discountValue: number;
  minimumAmount?: number;
  maximumDiscount?: number;
  usageLimit?: number;
  userUsageLimit?: number;
  isActive?: boolean;
  isStackable?: boolean;
  showInModule?: boolean;
  validFrom?: string;
  validUntil?: string;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  customerSegments?: string[];
}

export interface CouponUpdateInput extends Partial<CouponCreateInput> {
  id: string;
}