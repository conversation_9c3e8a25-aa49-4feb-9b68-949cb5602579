{"name": "herbalicious-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "lucide-react": "^0.344.0", "multer": "^2.0.1", "next": "^14.0.0", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "prisma": "^6.11.1", "razorpay": "^2.9.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "zod": "^4.0.5"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.0.0", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.0"}}