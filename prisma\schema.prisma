generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String         @id @default(cuid())
  email            String         @unique
  name             String?
  phone            String?
  avatar           String?
  password         String?
  role             UserRole       @default(CUSTOMER)
  emailVerified    DateTime?
  resetToken       String?
  resetTokenExpiry DateTime?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  addresses        Address[]
  orders           Order[]
  reviews          Review[]
  wishlist         WishlistItem[]
  preferences      UserPreference?
  couponUsages     CouponUsage[]
  notifications    Notification[]

  @@map("users")
}

model UserPreference {
  id                String   @id @default(cuid())
  userId            String   @unique
  language          String   @default("en-US")
  theme             String   @default("light")
  orderUpdates      <PERSON>olean  @default(true)
  promotions        Boolean  @default(false)
  newsletter        Bo<PERSON>an  @default(true)
  smsNotifications  Boolean  @default(false)
  // Notification preferences
  emailNotifications <PERSON>olean  @default(true)
  inAppNotifications Boolean  @default(true)
  orderNotifications Bo<PERSON>an  @default(true)
  wishlistNotifications Boolean @default(true)
  reviewNotifications Boolean  @default(true)
  priceDropAlerts   Boolean  @default(false)
  adminMessages     <PERSON>olean  @default(true)
  broadcastMessages Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Category {
  id                String            @id @default(cuid())
  name              String            @unique
  slug              String            @unique
  description       String?
  parentId          String?
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  parent            Category?         @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children          Category[]        @relation("CategoryHierarchy")
  productCategories ProductCategory[]
  products          Product[]

  @@map("categories")
}

model Product {
  id                String            @id @default(cuid())
  name              String
  slug              String            @unique
  description       String?
  shortDescription  String?
  comparePrice      Float?
  costPrice         Float?
  weight            Float?
  dimensions        String?
  isActive          Boolean           @default(true)
  isFeatured        Boolean           @default(false)
  metaTitle         String?
  metaDescription   String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  categoryId        String?
  price             Float?
  orderItems        OrderItem[]
  productCategories ProductCategory[]
  faqs              ProductFAQ[]
  images            ProductImage[]
  variants          ProductVariant[]
  category          Category?         @relation(fields: [categoryId], references: [id])
  reviews           Review[]
  wishlist          WishlistItem[]
  homepageSettings  HomepageSetting[] @relation("ProductOfTheMonth")

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  position  Int     @default(0)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id          String      @id @default(cuid())
  name        String
  value       String
  price       Float?
  productId   String
  pricingMode PricingMode @default(INCREMENT)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

model ProductFAQ {
  id        String   @id @default(cuid())
  question  String
  answer    String
  position  Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_faqs")
}

model Order {
  id            String        @id @default(cuid())
  orderNumber   String        @unique
  status        OrderStatus   @default(PENDING)
  paymentStatus PaymentStatus @default(PENDING)
  paymentMethod String?
  paymentId     String?
  subtotal      Float
  tax           Float         @default(0)
  shipping      Float         @default(0)
  discount      Float         @default(0)
  couponDiscount Float        @default(0)
  couponCodes   String[]      // Applied coupon codes
  total         Float
  currency      String        @default("INR")
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  userId        String
  address       OrderAddress?
  items         OrderItem[]
  user          User          @relation(fields: [userId], references: [id])
  couponUsages  CouponUsage[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Float
  total     Float
  orderId   String
  productId String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model OrderAddress {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  orderId    String  @unique
  order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_addresses")
}

model Address {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  isDefault  Boolean @default(false)
  userId     String
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

model Review {
  id         String       @id @default(cuid())
  rating     Int
  title      String?
  content    String?
  isVerified Boolean      @default(false)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  userId     String
  productId  String
  status     ReviewStatus @default(PENDING)
  product    Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  user       User         @relation(fields: [userId], references: [id])

  @@unique([userId, productId])
  @@map("reviews")
}

model WishlistItem {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  userId    String
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

model ProductCategory {
  id         String   @id @default(cuid())
  productId  String
  categoryId String
  createdAt  DateTime @default(now())
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, categoryId])
  @@map("product_categories")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String @default("string")

  @@map("settings")
}

enum UserRole {
  ADMIN
  CUSTOMER
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PricingMode {
  REPLACE
  INCREMENT
  FIXED
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

model Coupon {
  id                String        @id @default(cuid())
  code              String        @unique
  name              String
  description       String?
  type              CouponType
  discountType      DiscountType
  discountValue     Float
  minimumAmount     Float?
  maximumDiscount   Float?
  usageLimit        Int?
  usageCount        Int           @default(0)
  userUsageLimit    Int?
  isActive          Boolean       @default(true)
  isStackable       Boolean       @default(false)
  showInModule      Boolean       @default(false)
  validFrom         DateTime      @default(now())
  validUntil        DateTime?
  applicableProducts String[]     // Product IDs
  applicableCategories String[]   // Category IDs
  excludedProducts  String[]      // Product IDs
  excludedCategories String[]     // Category IDs
  customerSegments  String[]      // Customer segments
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  usages            CouponUsage[]

  @@map("coupons")
}

model CouponUsage {
  id        String   @id @default(cuid())
  couponId  String
  userId    String
  orderId   String?
  usedAt    DateTime @default(now())
  coupon    Coupon   @relation(fields: [couponId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  order     Order?   @relation(fields: [orderId], references: [id])

  @@unique([couponId, userId, orderId])
  @@map("coupon_usages")
}

enum CouponType {
  STORE_WIDE
  PRODUCT_SPECIFIC
  CATEGORY_SPECIFIC
  MINIMUM_PURCHASE
  BUNDLE_DEAL
  FIRST_TIME_CUSTOMER
  LOYALTY_REWARD
  SEASONAL
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
  BUY_X_GET_Y
}

model HomepageSetting {
  id                     String   @id
  // Hero Section
  heroTitle              String?  @default("Natural Skincare Essentials")
  heroSubtitle           String?  @default("Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin")
  heroCtaText            String?  @default("Shop Collection")
  heroCtaLink            String?  @default("/shop")
  heroSecondaryCtaText   String?  @default("View Categories")
  heroSecondaryCtaLink   String?  @default("/categories")
  heroBadgeText          String?  @default("New Collection")
  heroBackgroundColor    String?  @default("#f0fdf4") // Default green-50
  showHero               Boolean  @default(true)

  // Hero Trust Indicators
  trustIndicator1Value   String?  @default("100%")
  trustIndicator1Label   String?  @default("Natural")
  trustIndicator2Value   String?  @default("500+")
  trustIndicator2Label   String?  @default("Happy Customers")
  trustIndicator3Value   String?  @default("50+")
  trustIndicator3Label   String?  @default("Products")
  trustIndicator4Value   String?  @default("4.8★")
  trustIndicator4Label   String?  @default("Rating")

  // Product of the Month
  productOfTheMonthId    String?
  showProductOfMonth     Boolean  @default(true)

  // Promotional Banner
  bannerText             String?
  bannerCtaText          String?
  bannerCtaLink          String?
  bannerBackgroundColor  String?  @default("#22c55e") // Default green-500
  showBanner             Boolean  @default(true)

  // Categories Section
  showCategories         Boolean  @default(true)

  // Bestsellers Section
  productSectionBgColor  String?  @default("#f0fdf4") // Default green-50
  bestsellerIds          String[] @default([])
  showBestsellers        Boolean  @default(true)

  // Newsletter Section
  newsletterTitle        String?  @default("Stay Updated")
  newsletterSubtitle     String?  @default("Get the latest updates on new products and exclusive offers")
  showNewsletter         Boolean  @default(true)

  // Trust Badges Section
  showTrustBadges        Boolean  @default(true)

  // Flash Sale Section
  flashSaleTitle         String?  @default("Weekend Flash Sale")
  flashSaleSubtitle      String?  @default("Get 25% off all natural skincare products")
  flashSaleEndDate       DateTime?
  flashSaleBackgroundColor String? @default("#16a34a") // Default green-600
  showFlashSale          Boolean  @default(true)

  // Testimonials Section
  testimonialsTitle      String?  @default("What Our Customers Say")
  testimonialsSubtitle   String?  @default("Real reviews from real customers who love our natural skincare")
  testimonialsBackgroundColor String? @default("#f0fdf4") // Default green-50
  showTestimonials       Boolean  @default(true)

  isActive               Boolean  @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  productOfTheMonth      Product? @relation("ProductOfTheMonth", fields: [productOfTheMonthId], references: [id])

  @@map("homepage_settings")
}

model NewsletterSubscriber {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  whatsapp    String?  // WhatsApp number for sales and offers
  isActive    Boolean  @default(true)
  source      String?  @default("homepage") // homepage, checkout, etc.
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletter_subscribers")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  content   String
  rating    Int      @default(5)
  image     String?
  position  String?
  company   String?
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonials")
}

model Notification {
  id              String            @id @default(cuid())
  type            NotificationType
  title           String
  message         String
  data            Json?             // Additional data (order details, product info, etc.)
  isRead          Boolean           @default(false)
  emailSent       Boolean           @default(false)
  emailSentAt     DateTime?
  emailError      String?
  priority        NotificationPriority @default(NORMAL)
  expiresAt       DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  userId          String
  templateId      String?
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  template        NotificationTemplate? @relation(fields: [templateId], references: [id])

  @@index([userId, isRead])
  @@index([type, createdAt])
  @@map("notifications")
}

model NotificationTemplate {
  id            String   @id @default(cuid())
  name          String   @unique
  type          NotificationType
  title         String
  message       String
  emailSubject  String?
  emailTemplate String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  notifications Notification[]

  @@map("notification_templates")
}

enum NotificationType {
  ORDER_PLACED
  ORDER_CONFIRMED
  ORDER_PROCESSING
  ORDER_SHIPPED
  ORDER_DELIVERED
  ORDER_CANCELLED
  WISHLIST_ADDED
  WISHLIST_REMOVED
  PRICE_DROP_ALERT
  REVIEW_REQUEST
  REVIEW_SUBMITTED
  ADMIN_MESSAGE
  BROADCAST
  PROMOTIONAL
  SYSTEM
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
